.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

.header {
  // padding: 10px 0px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .breadcrumbContainer {
    margin-bottom: 10px;

    .breadcrumb {
      font-size: 14px;

      :global {
        .ant-breadcrumb-separator {
          margin: 0 8px;
          color: var(--list-value-text-color);
        }
      }
    }

    .breadcrumbCurrent {
      color: rgba(255, 178, 29, 1);
      background-color: rgba(255, 178, 29, 0.15);
      border-radius: 20px;
      padding: 5px 10px;
      cursor: pointer;

      // &:hover {
      //   text-decoration: underline;
      // }
    }

    .breadcrumbLink {
      color: rgba(140, 147, 176, 1);
      background-color: rgba(140, 147, 176, 0.1);
      border-radius: 20px;
      padding: 5px 10px;
      cursor: pointer;
    }
  }
}

.actionBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 20px 10px;
  border-bottom: 1px solid var(--border-color);
  .title {
    font-family: MiSans W;
    font-weight: 600;
    font-size: 22px;
    line-height: 24px;
    letter-spacing: 0%;
    vertical-align: middle;
    color: var(--text-color);
  }

  .buttonGroup {
    display: flex;
    gap: 10px;

    .downloadButton,
    .uploadButton {
      color: var(--subtitle-text-color);
      border-color: var(--thinLine-background-color);
      background-color: var(--background-color);

      &:disabled {
        color: var(--subtitle-text-color);
        border-color: var(--thinLine-background-color);
        opacity: 0.6;
      }
      &:hover {
        color: var(--subtitle-text-color);
        border-color: var(--thinLine-background-color);
        background-color: var(--background-color);
      }
    }
  }
}
.selectionInfo {
  font-size: 14px;
  color: var(--list-value-text-color);
  margin-right: 16px;
}

.content {
  flex: 1;
  overflow: hidden; /* 改为hidden，由子元素控制滚动 */
  padding: 0 20px;
}

// 文件列表样式
.fileListContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .fileListHeader {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid var(--thinLine-background-color);
    font-weight: bold;
    color: rgba(140, 147, 176, 1);
    background-color: var(--background-color);
    position: sticky;
    top: 0;
    z-index: 1;

    .selectAllHeader {
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .selectAllIcon {
        width: 32px;
        height: 32px;
        opacity: 0.8;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 1;
        }

        &.disabled {
          opacity: 0.3;
          cursor: not-allowed;

          &:hover {
            opacity: 0.3;
          }
        }
      }
    }

    .fileNameHeader,
    .fileTimeHeader,
    .fileSizeHeader {
      cursor: pointer;
      display: flex;
      align-items: center;
      text-align: center;

      &:hover {
        color: rgba(140, 147, 176, 1);
      }

      .sortIconContainer {
        display: inline-flex;
        flex-direction: column;
        margin-left: 4px;
        height: 14px;
        line-height: 0;

        .sortIcon {
          font-size: 14px;
          height: 7px;
          color: rgba(140, 147, 176, 0.5);

          &.activeIcon {
            color: var(--primary-color);
          }
        }
      }
    }

    .fileNameHeader {
      flex: 3;
    }

    .fileTimeHeader {
      flex: 1;
      text-align: center;
      justify-content: center;
    }

    .fileSizeHeader {
      flex: 1;
      justify-content: center;
    }
  }

  .fileListContent {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 24px;

    .fileItem {
      display: flex;
      padding: 12px 0;
      border-bottom: 1px solid var(--border-color);

      &:hover {
        background-color: var(--hover-background-color, rgba(0, 0, 0, 0.05));
      }

      &.selectedItem {
        background-color: var(
          --item-selected-background-color,
          rgba(0, 0, 0, 0.05)
        );
      }

      .checkboxCell {
        width: 40px;
        display: flex;
        align-items: center;
        justify-content: center;

        .fileCheckbox {
          &.hidden {
            opacity: 0;
            transition: opacity 0.2s ease;
          }

          &.visible {
            opacity: 1;
            transition: opacity 0.2s ease;
          }
        }

        :global {
          .ant-checkbox-wrapper {
            transition: opacity 0.2s ease;
          }
          :where(.css-dev-only-do-not-override-1d4w9r2).ant-checkbox .ant-checkbox-inner{
            background-color: var(--cancel-btn-background-color);
            border-color: var(--background-color);
          }
          :where(.css-dev-only-do-not-override-1d4w9r2).ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-checked:not(.ant-checkbox-disabled) .ant-checkbox-inner{
            background-color: var(--primary-color);
          }
        }
      }

      .fileName {
        flex: 3;
        display: flex;
        align-items: center;
        cursor: pointer;

        .fileIcon {
          margin-right: 10px;
          width: 36px;
          height: 36px;

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .fileNameText {
          font-family: MiSans W;
          font-weight: 500;
          font-size: 17px;
          line-height: 100%;
          letter-spacing: 0px;
          color: var(--text-color);
        }
      }

      .fileTime,
      .fileSize {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: MiSans W;
        font-weight: 500;
        font-size: 12px;
        line-height: 100%;
        letter-spacing: 0px;
        text-align: center;
        color: rgba(149, 149, 149, 1);
      }
    }
  }
}

// 空状态样式
.emptyStateWrapper {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 加载状态
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 20px;

  .loadingText {
    font-size: 16px;
    color: var(--list-value-text-color);
    margin-top: 10px;
  }
}

// 面包屑导航样式
.breadcrumbContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow-x: auto;
  padding: 12px 20px;
  white-space: nowrap;

  &::-webkit-scrollbar {
    display: none;
  }
}

.breadcrumbContent {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.breadcrumbNextContainer {
  display: flex;
  align-items: center;
  margin: 0 5px;
}

.breadcrumbNextImg {
  // width: 16px;
  height: 16px;
}

.breadcrumbItem {
  font-size: 14px;
  color: rgba(140, 147, 176, 1);
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 20px;
  background-color: rgba(140, 147, 176, 0.1);

  // &:hover {
  //   background-color: rgba(0, 0, 0, 0.05);
  // }
}

.breadcrumbItemActive {
  color: rgba(255, 178, 29, 1);
  font-weight: 500;
  background-color: rgba(255, 178, 29, 0.15);
}

// Loading 样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600px;
  gap: 16px;
}
