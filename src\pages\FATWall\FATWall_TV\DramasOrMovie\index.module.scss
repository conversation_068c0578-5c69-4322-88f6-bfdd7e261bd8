.container {
    width: 100%;
    height: 100vh;
    position: relative;
    color: white;
    background-color: #000;
    overflow: hidden;
  }
  
  .videoBackground {
    width: 100%;
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter: blur(5px);
      transform: scale(1.05);
    }
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.9));
    }
  }
  
  .backButton {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    font-size: 24px;
    margin-bottom: 20px;
    width: auto;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 20px;
    cursor: pointer;
    padding: 0 20px;
    gap: 10px;
    transition: all 0.3s ease;
    
    &:focus {
      background-color: rgba(255, 255, 255, 0.2);
      outline: 2px solid #fff;
      outline-offset: 2px;
      transform: scale(1.05);
    }
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    span {
      font-size: 18px;
    }
  }
  
  .scrollableContent {
    position: relative;
    z-index: 2;
    padding: 20px;
    height: 100%;
    max-height: 100vh;
    left: 2%;
    margin-top: 50px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
   //  padding-top: 80px; /* 给返回按钮留出空间 */
    
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }
  }
  
  .title {
    font-size: 72px;
    font-weight: 600;
    margin-bottom: 10px;
    padding-top: 100px;
  }
  
  .infoRow {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 30px;
    font-weight: 400;
  }
  
  .rating {
    color: #ffaa00;
  }
  
  .tag {
    padding: 2px 8px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    font-size: 12px;
  }
  
  .descriptionContainer {
    // margin: 20px 0;
    position: relative;
    max-width: 40%;
  }
  
  .description {
    font-size: 30px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
    max-height: 4.8em; // 限制高度为3行
    display: -webkit-box;
    -webkit-line-clamp: 3; // 限制为3行
    -webkit-box-orient: vertical;
    transition: max-height 0.3s ease;
  
    &.expanded {
      max-height: 1000px;
      -webkit-line-clamp: unset;
    }
  }
  
 .film_card_container{
    display: flex;
    justify-content: start;
    flex-wrap: wrap;
    gap: 50px;
    margin-top: 40px;
 }

// TVFocusable 焦点样式
.focus_item {
  &:focus {
    outline: none;
    
    // 为卡片添加焦点效果
    > div {
      transform: scale(1.05);
      transition: transform 0.3s ease;
      box-shadow: 0 0 0 3px #ffaa00;
      border-radius: 8px;
    }
  }
}