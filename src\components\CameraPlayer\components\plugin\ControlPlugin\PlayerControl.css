.timeAxis_container {
  position: absolute;
  width: 100%;
  height: 42px;
  bottom: 0;
  left: 0;
}

.timeAxis_container.ios {
  height: 100px;
}

.timeAxis_container.pc {
  height: 120px;
}

.monitorPlayer_content {
  position: absolute;
  width: 100%;
  z-index: 1;
  display: flex;
  transition: opacity .5s ease, visibility .5s ease;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
}

.monitorPlayer_content.notFull.pc {
  bottom: 0px;
}

.monitorPlayer_content.notFull.pc.modal {
  justify-content: center;
}

.monitorPlayer_content.full.pc {
  bottom: 120px;
  justify-content: center;
}

.monitorPlayer_content.pc.notFull.dashboard {
  bottom: 0px;
}

.monitorPlayer_content.full.pc.Movie {
  bottom: 0px;
}

.monitorPlayer_content.full.pc.Live,
.monitorPlayer_content.full.mobile.Live {
  display: flex;
  flex-direction: column;
}

.monitorPlayer_content.full.mobile {
  bottom: 0;
  /* width: calc(100% - 60px); */
  width: 100%;
  padding: 0 28px;
  justify-content: flex-start;
}

.monitorPlayer_content.full.mobile.Live {
  bottom: 42px;
}

.monitorPlayer_content.full.mobile.ios.Live {
  bottom: 100px
}

.monitorPlayer_content.notFull.mobile {
  bottom: 0px;
  justify-content: center;
}

.monitorPlayer_controls {
  /* width: calc(100% - 56px); */
  width: 100%;
  position: relative;
  padding: 0 28px;
  display: flex;
  justify-content: center;
}

.monitorPlayer_controls_btns {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
}

.monitorPlayer_controls_btns.full {
  justify-content: left;
}

.monitorPlayer_controls_btns.notFull.modal {
  justify-content: left;
}

.monitorPlayer_controls.pc {
  flex-direction: column;
}

.monitorPlayer_controls.pc.Live.full {
  height: 80px;
}

.monitorPlayer_controls.pc.Movie,
.monitorPlayer_controls.pc.Live {
  padding: 0 12px;
}

.monitorPlayer_controls.pc.Movie.notFull.modal {
  padding: 0 24px;
}

.monitorPlayer_controls.pc.Movie.full.modal {
  padding: 0 12px 12px 12px;
}

.monitorPlayer_controls.mobile {
  padding: 6px;
}

.monitorPlayer_controls.mobile.ios.full {
  display: flex;
  align-items: center;
}

.monitorPlayer_controls.mobile.Movie.full,
.monitorPlayer_controls.mobile.Live.full {
  flex-direction: column;
  width: 100%;
}

.fullScreen_viewRecord {
  position: absolute;
  right: -60%;
}

.iconBtn_container {
  /* width: 28px; */
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  /* 辅助平衡系统 */
  padding: 2px;
  /* 不同形状的微妙补偿 */
  margin: -2px;
  /* 抵消补偿产生的溢位 */

  /* img {
    height: 28px;
    transform: scale(0.5);
    cursor: pointer;
  } */
}

.iconBtn_container_img {
  height: 28px;
  transform: scale(0.5);
  cursor: pointer;
}

.iconBtn_container.inRight {
  position: absolute;
  right: 20px;
}

.iconBtn_container.full {
  margin-right: 15px;
}

.iconBtn_container_img.full.android {
  height: 16px;
}

.iconBtn_container_img.full.pc {
  height: 40px;
}

.iconBtn_container.notFull.pc {
  height: 56px;
}

.iconBtn_container_img.notFull.pc {
  height: 28px;
  transform: scale(1);
}

.iconBtn_container.notFull.pc.dashboard {
  height: 56px;
}

.iconBtn_container_img.pc.dashboard {
  height: 40px;
  transform: scale(1);
}

.iconBtn_container.notFull.pc.modal {
  margin-right: 28px;
}

.screenshot_save {
  position: absolute;
  background-color: #FFF;
  padding: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2%;
}

.screenshot_save.full {
  top: 100px;
  left: 28px;
}

.screenshot_save.full.android {
  top: 20px;
  left: 28px;
}

.screenshot_save.notFull {
  top: 20px;
  left: 28px;
}

.record_monitoring_time {
  width: 90px;
  height: 28px;
  background-color: rgba(2, 2, 2, 0.8);
  color: #FFF;
  position: relative;
  border-radius: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0%;
  left: calc(50% - 45px);
}

.record_monitoring_time.pc {
  width: 30px;
  height: 10px;
  left: calc(50% - 15px);
  top: 10%;
}

.record_monitoring_time_rec.pc {
  width: 2px;
  height: 2px;
  border-radius: 2px;
  background-color: red;
  margin-right: 5px;
}

.record_monitoring_time_rec {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background-color: red;
  margin-right: 10px;
}

.control_player_screenshot_modal {
  width: 100%;
}

.control_player_screenshot_modal_close {
  position: absolute;
  top: 20px;
  left: 30px;
}

.control_player_screenshot_modal_close_img {
  height: 24px;
}

.screenshot_save_play {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  bottom: 10px;
  left: 15px;
}

.screenshot_save_play_span {
  color: #FFF;
  text-align: center;
  font-family: MiSans;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.screenshot_save_play_img {
  height: 12px;
}