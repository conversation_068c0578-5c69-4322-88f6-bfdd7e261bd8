import React, { useState, useEffect } from "react";
import { Toast, Image } from "antd-mobile";
import { useHistory, useLocation } from "react-router-dom";
import type { RadioValue } from "antd-mobile/es/components/radio";
import classNames from "classnames";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import { getRecordConfig, setupRecordCamera, CameraInfo } from "@/api/ipc";
import { useRequest } from "ahooks";
import picker from "@/Resources/camMgmtImg/picker.png";
import circleArrow from "@/Resources/camMgmtImg/circle-arrow.png";
import circleDef from "@/Resources/camMgmtImg/circle-arrow-def.png";
import circleArrDark from "@/Resources/camMgmtImg/circle-arrow-dark.png";

export interface LocationState {
  camera?: CameraInfo;
  schedule?: Array<{
    start: string;
    end: string;
  }>;
}

const RecordingPlan = () => {
  const history = useHistory();
  const { isDarkMode } = useTheme();
  const location = useLocation<LocationState>();

  // 获取传递的摄像机数据
  const [camera, setCamera] = useState<CameraInfo | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<RadioValue>("全时段");
  const [scheduleCount, setScheduleCount] = useState<number>(0);

  // 优先使用location.state.schedule
  useEffect(() => {
    if (location.state?.camera) {
      setCamera(location.state.camera);
    }
    if (location.state?.schedule) {
      setSelectedPlan("自定义");
      setScheduleCount(location.state.schedule.length);
    }
  }, [location.state]);

  // 获取录制配置
  const { run: fetchConfig } = useRequest(getRecordConfig, {
    manual: true,
    onSuccess: (result) => {
      console.log("获取录制计划成功:", result);
      const recordConfig = result.data;

      // 设置计划类型
      if (recordConfig.record_schedule?.type) {
        setSelectedPlan(
          recordConfig.record_schedule.type === "full_time"
            ? "全时段"
            : "自定义"
        );

        // 设置时段数量
        if (recordConfig.record_schedule.schedule) {
          setScheduleCount(recordConfig.record_schedule.schedule.length);
        }
      }
    },
    onError: (error) => {
      console.error("获取录制计划失败:", error);
      Toast.show({
        content: "获取设置失败",
        position: "bottom",
      });
    },
  });

  // 保存录制计划
  const { run: saveConfig } = useRequest(setupRecordCamera, {
    manual: true,
    onSuccess: (result) => {
      if(result && (result.code === 5000 || result.code === 1701)){
        Toast.show(result?.result);
        return;
      }
      Toast.show({
        content: "设置成功",
        position: "bottom",
      });
    },
    onError: (error) => {
      console.error("保存录制计划失败:", error);
      Toast.show({
        content: "设置失败，请重试",
        position: "bottom",
      });
    },
  });

  // 当摄像机ID可用时获取配置
  useEffect(() => {
    if (camera?.did) {
      fetchConfig({ camera: camera.did });
    }
  }, [camera, fetchConfig]);

  const handlePlanChange = (value: RadioValue) => {
    // 如果点击已选中的全时段，则切换为未选中状态（显示自定义）
    if (value === selectedPlan && value === "全时段") {
      setSelectedPlan("自定义");

      // 保存设置到服务器
      if (camera?.did) {
        saveConfig({
          camera: [camera.did],
          config: {
            record_schedule: {
              type: "customized",
              schedule: [],
            },
          },
        });
      }
      return;
    }

    // 正常切换选中状态
    if (value === selectedPlan) return;

    setSelectedPlan(value);

    // 保存设置到服务器
    if (camera?.did) {
      saveConfig({
        camera: [camera.did],
        config: {
          record_schedule: {
            type: value === "全时段" ? "full_time" : "customized",
            schedule: [], // 对于全时段，不需要具体时间表
          },
        },
      });
    }
  };

  const goToCustomize = () => {
    history.push({
      pathname: "/cameraManagement_app/storageManagement/customize",
      state: {camera},
    });
  };

  const getIconSrc = (selectedPlan: RadioValue, isDarkMode: boolean) => {
    if (selectedPlan === "自定义") return circleArrow;
    return isDarkMode ? circleArrDark : circleDef;
  };

  return (
    <div className={styles.container}>
      <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
      <div className={styles.title}>录制计划</div>
      <div className={styles.planCards}>
        <div
          className={classNames(styles.planCard, {
            [styles.selected]: selectedPlan === "全时段",
          })}
          onClick={() => handlePlanChange("全时段")}
        >
          <div className={styles.planContent}>
            <div className={styles.checkIconWrapper}>
              {selectedPlan === "全时段" && (
                <Image src={picker} className={styles.checkIcon} />
              )}
            </div>
            <div className={styles.planInfo}>
              <div className={styles.planTitle}>全时段</div>
            </div>
          </div>
        </div>

        <div
          className={classNames(styles.planCard, {
            [styles.selected]: selectedPlan === "自定义",
          })}
          onClick={() => {
            handlePlanChange("自定义");
            // if (selectedPlan === "自定义") {
            //   goToCustomize();
            // }
          }}
        >
          <div className={styles.planContent}>
            <div className={styles.checkIconWrapper}>
              {selectedPlan === "自定义" && (
                <Image src={picker} className={styles.checkIcon} />
              )}
            </div>
            <div className={styles.planInfo}>
              <div className={styles.planTitle}>自定义</div>
              {selectedPlan === "自定义" && scheduleCount > 0 && (
                <div className={styles.planSubtitle}>{scheduleCount}个时段</div>
              )}
              {selectedPlan === "自定义" && scheduleCount === 0 && (
                <div className={styles.planSubtitle}>无时段</div>
              )}
            </div>
            <Image
              src={getIconSrc(selectedPlan, isDarkMode)}
              className={styles.arrowIcon}
              onClick={() => goToCustomize()}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecordingPlan;
