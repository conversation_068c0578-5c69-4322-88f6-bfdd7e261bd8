.modalContent {
  position: fixed;
  top: 35px;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  height: 100vh;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.leftIcon,
.rightIcon {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selectedIcon {
  opacity: 0.6;
  filter: brightness(0.8);
}

.title {
  font-size: 32px;
  font-weight: 400;
  color: var(--title-color);
  padding: 10px 16px;
}

.content {
  flex: 1;
  padding: 0 12px;
  overflow-y: auto;
  padding-bottom: 100px; // 为底部操作栏留出空间
}

.videoGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding-bottom: 16px;
}

.videoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.thumbnailContainer {
  position: relative;
  width: 100%;
  aspect-ratio: 4/3;
  margin-bottom: 8px;
}

.thumbnail {
  position: relative;
  width: 106px;
  height: 74px;
  background-color: #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.thumbnailPlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: end;
  justify-content: start;
  position: relative;

  &.hidden {
    display: none;
  }
}

.playIcon {
  opacity: 0.6;
  transition: opacity 0.2s ease;
  margin-left: 5px;

  svg {
    width: 20px;
    height: 20px;
  }
}

.checkboxContainer {
  position: absolute;
  bottom: 5px;
  right: 5px;
  z-index: 2;
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #fff;
  border-radius: 50%;
  background-color: var(--card-background-color);
  display: flex;
  align-items: center;
  justify-content: center;

  &.checked {
    background-color: #007AFF;
    border-color: #007AFF;

    // 对勾样式
    &::after {
      content: '';
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      margin-top: -2px;
    }
  }
}

.timeLabel {
  font-size: 12px;
  color: var(--text-color);
  text-align: center;
  font-weight: 400;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--background-color);
  border-top: 1px solid var(--border-color);
  padding: 10px 16px;
  display: flex;
  gap: 1px;
  z-index: 10;
}

.actionButton {
  flex: 1;
  height: 60px;
  border: none;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--background-color);
  color: var(--text-color);

  img {
    width: 24px;
    height: 24px;
  }

  &.disabled {
    opacity: 0.6;
    cursor: default;
    pointer-events: none;
  }

  &:not(.disabled):active {
    opacity: 0.8;
  }
}
