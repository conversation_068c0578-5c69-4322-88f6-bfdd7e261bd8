import request from "@/request";

const macBackupRequest = <T>(params: any): Promise<T> => {
  // 判断当前环境
  if (process.env.NODE_ENV === 'development') {
    // 开发环境使用代理
    return request.post('/timemachine/timemachine.cgi', params, {
      baseURL: ''
    });
  } else {
    // 生产环境中使用HTTP协议 使用完整的主机名（包含端口号）
    const host = window.location.host;

    // 从URL路径中提取ID
    let pluginId = '2958338817'; // 默认ID
    const pathMatch = window.location.pathname.match(/\/plugin\/(\d+)/);
    if (pathMatch && pathMatch[1]) {
      pluginId = pathMatch[1];
    }

    return request.post(`/plugin/${pluginId}/timemachine/timemachine.cgi`, params, {
      baseURL: `http://${host}`
    });
  }
};

interface MAC_BASE_RESPONSE {
  code: number;
}

export interface macBackupStatusResponse extends MAC_BASE_RESPONSE {
  enable: '0' | '1',
  path: string;
  limit: string;
}

export enum MacBackupAction {
  'GET_STATUS' = 'get_status',
  'OPEN_TIME_MACHINE' = 'open_timemachine',
  'OPEN_SMB' = 'open_smb',
  'SET_LIMIT' = 'set_limit'
}

// 获取mac备份助手状态
export const getMacBackupStatus = (): Promise<MAC_BASE_RESPONSE> => {
  return macBackupRequest<macBackupStatusResponse>({
    action: MacBackupAction.GET_STATUS
  })
}

// 设置容量限制
export const setLimitByMacBackup = () => {

}

// 开启smb/afp
