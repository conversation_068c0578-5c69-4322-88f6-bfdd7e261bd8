body {
  :global {
    .ant-list-item {
      padding: 12px 0 !important;
    }
    .ant-form-item {
      width: 100%;
    }
    .ant-select-dropdown,
    .ant-select-item,
    .ant-select-item-option {
      background-color: var(--modal-content-background-color);
      color: var(--text-color) !important;
    }
    .ant-select-item-option-active {
      color: var(--text-color) !important;
      background-color: var(--primary-btn-background-color) !important;
    }
    .ant-select-item-option-selected {
      color: var(--text-color) !important;
      background-color: var(--primary-color) !important;
    }
  }
}

.listItemContainer {
  width: 100%;
  height: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  img {
    margin-left: 6px;
    height: 20px;
  }
}

.listItem_label {
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  vertical-align: middle;
  color: var(--text-color);
}

.listItem_value {
  height: 100%;
  font-family: MiSans;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: right;
  vertical-align: middle;
  color: var(--list-value-text-color);
}

.listItem_value_container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.onClick {
  cursor: pointer;
}

.defaultModalWrapper {
  display: flex;
  justify-content: center;
  position: fixed;
  left: 0;
  right: 0;

  >div {
    width: 100%;
  }
  
  :global {
    .ant-modal-content {
      // max-width: calc(100% - 16px);
      // padding: 24px !important;
      background-color: var(--modal-content-background-color);
      border-radius: 32px;
    }
    .ant-modal-confirm-title {
      background-color: var(--modal-content-background-color);
      margin-bottom: 0;
      border-radius: 32px;
      text-align: center;
    }
    .ant-modal-footer {
      margin: 0;
    }
    .ant-select {
      width: 85px;
      height: 54px;
    }
    .ant-input {
      height: 54px;
      &:hover {
        border-color: var(--primary-color);
      }
      font-family: MiSans;
      font-weight: 500;
      font-size: 17px;
      line-height: 100%;
      letter-spacing: 0px;
      vertical-align: middle;
      color: var(--text-color) !important;
      padding: 4px 11px !important;
    }
    .ant-input-clear-icon {
      color: var(--text-color) !important;
    }
    .ant-input-number {
      width: 100%;
      height: 54px;
      font-family: MiSans;
      font-weight: 500;
      font-size: 17px;
      line-height: 100%;
      letter-spacing: 0px;
      vertical-align: middle;
      color: var(--text-color) !important;
      &:hover {
        border-color: var(--primary-color);
      }
    }
    .ant-input-number-input {
      height: 54px;
      flex: 1;
    }
    .ant-input-outlined,
    .ant-input-number-outlined {
      padding: 0 12px;
      border-radius: 18px;
      margin-top: 8px;
      background-color: var(--modal-content-background-color);
    }
    .ant-input-number-handler-wrap {
      display: none;
    }
    .ant-select-outlined {
      margin-top: 8px;
    }
    .ant-select-selector {
      padding: 0 12px !important;
      border-radius: 18px !important;
      background-color: var(--modal-content-background-color) !important;
    }
    .ant-input-number-input,
    .ant-select-arrow,
    .ant-select-selection-item {
      color: var(--text-color) !important;
    }
    .ant-input-status-error:not(.ant-input-disabled) {
      background-color: var(--modal-content-background-color);
      &:focus,
      &:hover,
      &:active,
      &:focus-within {
        background-color: var(--modal-content-background-color);
        border-color: rgba(243, 0, 24, 1);
      }
    }
    .ant-form-item-additional {
      margin: 12px 0 0 0;
    }
    .ant-form-item-explain-error {
      font-family: MiSans;
      font-weight: 500;
      font-size: 14px;
      line-height: 100%;
      letter-spacing: 0%;
      vertical-align: bottom;
      color: rgba(243, 0, 24, 1);
      padding: 0 4px;
    }
    .ant-modal-confirm-paragraph {
      max-width: 100%;
    }
  }
}

.modalFooter {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modalCancelBtn,
.modalConfirmBtn {
  width: 150px;
  height: 50px;
  border-radius: 16px;
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
  margin: 6px 12px 0 12px;
}

.modalCancelBtn {
  border: transparent;
  background-color: var(--cancel-btn-background-color);
  color: var(--text-color);
}

.modalConfirmBtn {
  border: transparent;
  color: rgba(255, 255, 255, 1);
  background-color: var(--primary-color);
}

.modalContent {
  margin: 12px 0;
  // min-height: 300px;
  max-height: calc(100% - 64px);
  overflow-y: auto;
  scrollbar-width: none;
  color: var(--text-color);
}

.modalInputContainer {
  display: flex;
  align-items: center;
  justify-content: center;

  & > div {
    margin: 0 8px;
  }
}

.modalShowTitle {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);

  img {
    height: 28px;
  }

  .modalShowExtra {
    position: absolute;
    right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.bottom {
  align-items: flex-end;
  bottom: 30px;
}

.top {
  align-items: flex-start;
  top: 100px;
}

.center {
  align-items: center;
  top: 20%;
}
