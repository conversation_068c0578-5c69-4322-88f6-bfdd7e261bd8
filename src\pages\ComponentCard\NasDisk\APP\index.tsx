import { useCallback, useMemo, useState } from "react";
import { useHistory } from "react-router-dom";
import { useRequest } from "ahooks";
import { getBaiduUserInfo, getTaskInfo } from "@/api/nasDisk";
import styles from "./index.module.scss";
import { PreloadImage } from "@/components/Image";

// 同步状态类型
type SyncStatus = "transferring" | "completed" | "none" | "error";

// 定义共通的按钮配置
const DEFAULT_BUTTON_CONFIG = {
  text: "查看同步任务",
  action: "/TaskManager"
};

const LOGIN_BUTTON_CONFIG = {
  text: "去登录",
  action: "/login"
};

const DashboardCardNasDiskApp = () => {
  const history = useHistory();

  const [syncStatus, setSyncStatus] = useState<SyncStatus>("completed");
  // 登录状态
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  // 登录状态加载中
  const [userLoading, setUserLoading] = useState<boolean>(true);

  // 获取百度网盘用户信息
  const { data: userInfo, loading: userInfoLoading } = useRequest(
    () => getBaiduUserInfo({ action: "uinfo" }),
    {
      onSuccess: (result) => {
        if (result && result.token) {
          // 有token表示已登录
          setIsLoggedIn(true);
        } else {
          // 未登录或token无效
          setIsLoggedIn(false);
        }
        setUserLoading(false);
      },
      onError: () => {
        // 请求失败，设置为未登录
        setIsLoggedIn(false);
        setUserLoading(false);
      },
    }
  );

  // 获取活动任务信息
  const { loading: activeTasksLoading } = useRequest(
    () => getTaskInfo({
      selector: [
        { key: "module", value: ["bpan"] },
        { key: "type", value: ["active"] }
      ]
    }),
    {
      ready: isLoggedIn, // 只在登录后请求
      refreshDeps: [isLoggedIn],
      onSuccess: (response) => {
        if (response.code === 0 && response.data?.info) {
          const activeTasks = response.data.info;
          // 根据活动任务状态更新UI
          if (activeTasks.length > 0) {
            // 查找是否有running或waiting状态的任务
            const runningTasks = activeTasks.filter(task => 
              task.status === "running" || task.status === "waiting");
            
            if (runningTasks.length > 0) {
              setSyncStatus("transferring");
            } else {
              // 只有paused或success_waiting状态的任务
              setSyncStatus("completed");
            }
          } else {
            // 没有活动任务，检查历史任务
            historyTasksRun();
          }
        }
      },
      onError: () => {
        // 任务接口错误，设置任务状态为错误，但不影响登录状态
        setSyncStatus("error");
      }
    }
  );

  // 获取历史任务信息
  const { runAsync: historyTasksRun, loading: historyTasksLoading } = useRequest(
    () => getTaskInfo({
      selector: [
        { key: "module", value: ["bpan"] },
        { key: "type", value: ["history"] }
      ]
    }),
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data?.info) {
          const historyTasks = response.data.info;
          // 有历史任务则显示"已完成"，没有则显示"无任务"
          if (historyTasks.length > 0) {
            setSyncStatus("completed");
          } else {
            setSyncStatus("none");
          }
        } else {
          setSyncStatus("none");
        }
      },
      onError: () => {
        // 历史任务接口错误，设置任务状态为错误，但不影响登录状态
        setSyncStatus("error");
      }
    }
  );

  // 状态内容配置
  const statusConfig = useMemo(
    () => ({
      transferring: {
        title: "传输中",
        subtitle: "任务",
        color: "var(--primary-color)",
      },
      completed: {
        title: "已完成",
        subtitle: "任务",
        color: "var(--text-color)",
      },
      none: {
        title: "无",
        subtitle: "任务",
        color: "var(--text-color)",
      },
      error: {
        title: "获取失败",
        subtitle: "任务",
        color: "var(--error-color, red)",
      },
    }),
    []
  );

  // 获取按钮配置（根据登录状态决定）
  const getButtonConfig = useCallback(() => {
    if (!isLoggedIn) {
      return LOGIN_BUTTON_CONFIG;
    }
    return DEFAULT_BUTTON_CONFIG;
  }, [isLoggedIn]);

  // 当前配置
  const currentConfig = statusConfig[syncStatus];
  const buttonConfig = getButtonConfig();
  
  // 判断是否正在加载
  const isLoading = userInfoLoading || (isLoggedIn && (activeTasksLoading || historyTasksLoading));

  // 按钮点击处理
  const handleButtonClick = useCallback(() => {
    history.push(`${"/baiduNetdisk_app"}${buttonConfig.action}`);
  }, [history, buttonConfig.action]);

  return (
    <div className={styles.cardContainer}>
      {/* 卡片头部 */}
      <div className={styles.cardHeader}>
        <div className={styles.logoArea}>
          <PreloadImage
            src={userInfo?.avatar_url || ""}
            className={styles.avatar}
          />
          <span className={styles.title}>百度网盘</span>
        </div>
      </div>

      {/* 卡片内容 */}
      <div className={styles.cardContent}>
        <div className={styles.statusInfo}>
          {userLoading ? (
            <div className={styles.statusTitle}>加载中...</div>
          ) : !isLoggedIn ? (
            <div className={styles.statusTitle}>未登录</div>
          ) : isLoading ? (
            <div className={styles.statusTitle}>加载中...</div>
          ) : (
            <>
              <div
                className={styles.statusTitle}
                style={{ color: currentConfig.color }}
              >
                {currentConfig.title}
              </div>
              {currentConfig.subtitle && (
                <div className={styles.statusSubtitle}>
                  {currentConfig.subtitle}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* 卡片底部按钮 */}
      <div className={styles.cardFooter}>
        <button
          className={styles.actionButton}
          onClick={handleButtonClick}
          disabled={userLoading}
          style={{ opacity: userLoading ? 0.7 : 1 }}
        >
          {buttonConfig.text}
        </button>
      </div>
    </div>
  );
};

export default DashboardCardNasDiskApp;
