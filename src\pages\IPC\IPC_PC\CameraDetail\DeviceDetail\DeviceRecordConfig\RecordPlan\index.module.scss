.container {
  width: 400px;
  height: 400px;
  // background-color: var(--background-color);
  :global {
    a.adm-list-item:active:not(.adm-list-item-disabled) {
      background-color: var(--background-color) !important;
    }
  }

  padding: 40px 0;

  .header {
    padding: 0 10px;
    .arrows {
      width: 40px;
      height: 40px;
    }
  }

  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 0 20px;
  }

  .format {
    font-size: 12px;
    padding: 8px 16px;
    border-radius: 8px;
  }

  .customRadio {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;

    &.checked {
      border-color: var(--primary-color);
      background-color: var(--primary-color);
    }

    .innerCircle {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: var(--background-color);
    }
  }

  .planCards {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 0 16px;
    margin-top: 20px;
  }

  .planCard {
    background-color: var(--cancel-btn-background-color);
    font-size: 16px;
    color: var(--text-color);
    font-weight: 500;
    border-radius: 16px;
    padding: 20px 16px;
    cursor: pointer;
    transition: all 0.3s ease;

    &.selected {
      background-color: var(--primary-btn-background-color);
      color: var(--primary-color);
    }
  }

  .planContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .checkIconWrapper {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .planInfo {
    flex: 1;
  }

  .planTitle {
    font-size: 16px;
    font-weight: 500;
  }

  .planSubtitle {
    font-size: 14px;
    color: var(--primary-color);
    margin-top: 4px;
  }

  .checkIcon {
    width: 20px;
    height: 20px;
  }

  .arrowIcon {
    width: 16px;
    height: 16px;
  }

  :global {
    .adm-list {
      --border-inner: 0;
      --border-bottom: 0;
      --border-top: 0;
      background-color: var(--background-color);
      .adm-list-item {
        padding: 16px;
        background-color: var(--background-color);
      }
      .adm-list-item-content-prefix {
        color: var(--text-color);
      }
    }
  }
}
