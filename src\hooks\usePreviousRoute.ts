import { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';

function usePreviousRoute() {
  const history = useHistory<any>();
  const [previousRoute, setPreviousRoute] = useState<any>((() => {
    const route = localStorage.getItem('previousRoute');
    if (route) {
      return route
    } else {
      return undefined;
    }
  })());

  useEffect(() => {
    const unListen = history.listen((location) => {
      console.log('离开路由，记录上一个路由地址');
      if (!location) return;
      // 在路由变化时存储当前的路由地址
      setPreviousRoute(location);
      localStorage.setItem('previousRoute', location.pathname);
    });

    // 组件卸载时取消监听
    return () => {
      unListen();
    };
  }, [history]);

  return previousRoute;
}

export default usePreviousRoute;