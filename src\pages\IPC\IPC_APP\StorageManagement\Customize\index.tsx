import { useState, useEffect } from "react";
import { Image, Switch, Toast, Checkbox } from "antd-mobile";
import { useHistory, useLocation } from "react-router-dom";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import edit from "@/Resources/camMgmtImg/edit.png";
import listNull from "@/Resources/camMgmtImg/list-null.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import editDark from "@/Resources/camMgmtImg/edit-dark.png";
import listNullDark from "@/Resources/camMgmtImg/list-null-dark.png";
import add from "@/Resources/camMgmtImg/add.png";
import finish from "@/Resources/camMgmtImg/finish.png";
import finishDark from "@/Resources/camMgmtImg/finish-dark.png";
import deleteIcon from "@/Resources/camMgmtImg/delete.png";
import deleteDark from "@/Resources/camMgmtImg/deletes-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { CameraInfo, getRecordConfig, setupRecordCamera } from "@/api/ipc";
import ShutDown from "../ShutDown";
import { modalShow } from "@/components/List";

// 接口参数类型
interface LocationState {
  camera?: CameraInfo;
  refresh?: boolean;
}

// 定义时间段类型
interface TimeSlot {
  id: number;
  start: string;
  end: string;
  repeat_policy: string;
  customized: string[];
  enabled: boolean;
  selected?: boolean;
}

// 中英文星期映射
const dayMapping: Record<string, string> = {
  Mon: "周一",
  Tue: "周二",
  Wed: "周三",
  Thu: "周四",
  Fri: "周五",
  Sat: "周六",
  Sun: "周日",
};

// 英文星期映射回来
// const reverseDayMapping: Record<string, string> = {
//   周一: "Mon",
//   周二: "Tue",
//   周三: "Wed",
//   周四: "Thu",
//   周五: "Fri",
//   周六: "Sat",
//   周日: "Sun",
// };

const Customize = () => {
  const history = useHistory();
  const location = useLocation<LocationState>();
  const { isDarkMode } = useTheme();
  const [isEditing, setIsEditing] = useState(false);
  const [slots, setSlots] = useState<TimeSlot[]>([]);

  // 获取原始录制配置
  const [, setOriginalConfig] = useState<any>(null);
  const camera = location.state?.camera;

  const { run: fetchConfig } = useRequest(
    getRecordConfig,
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0 && res.data) {
          setOriginalConfig(res.data);

          // 转换时间段数据
          if (
            res.data.record_schedule &&
            res.data.record_schedule.schedule &&
            res.data.record_schedule.schedule.length > 0
          ) {
            const mappedSlots = res.data.record_schedule.schedule.map(
              (item: any, index: number) => ({
                id: index + 1,
                start: item.start,
                end: item.end,
                repeat_policy: item.repeat_policy,
                customized: item.customized || [],
                enabled: item.enabled !== undefined ? item.enabled : true,
                selected: false,
              })
            );

            setSlots(mappedSlots);
          }
        } else {
          Toast.show({
            content: "获取录制配置失败:" + res?.result,
          });
        }
      },
      onError: (error) => {
        console.error("获取录制配置失败:", error);
        Toast.show({
          content: "获取录制配置失败，使用默认数据",
        });
      },
    }
  );

  // 使用useRequest保存配置
  const { run: saveConfig } = useRequest(
    setupRecordCamera,
    {
      manual: true,
      onSuccess: (res) => {
        console.log("保存录制配置成功:", res);
        if (res && res.code === 0) {
          Toast.show({
            content: "保存成功",
          });
          if (camera?.did) {
            fetchConfig({ camera: camera.did });
          }
        } else {
          Toast.show({
            content: "保存失败: " + (res?.result || "未知错误"),
          });
        }
      },
      onError: (error) => {
        console.error("保存录制配置失败:", error);
        Toast.show({
          content: "保存失败，请重试",
        });
      },
    }
  );

  // 获取录制配置
  useEffect(() => {
    if (camera?.did) {
      fetchConfig({ camera: camera.did });
    }
  }, [camera, fetchConfig]);

  // 从ShutDown页面返回时检查是否需要刷新数据
  useEffect(() => {
    if (location.state?.refresh && camera?.did) {
      fetchConfig({ camera: camera.did });

      // 清除刷新标志，防止多次刷新
      const newState = { ...location.state };
      delete newState.refresh;
      history.replace({ pathname: location.pathname, state: newState });
    }
  }, [location.state, camera, history, location.pathname, fetchConfig]);

  // 开关时间段
  const handleSwitchChange = (id: number, checked: boolean) => {
    // 更新本地状态
    const updatedSlots = slots.map((slot) =>
      slot.id === id ? { ...slot, enabled: checked } : slot
    );
    setSlots(updatedSlots);

    if (!camera?.did) {
      return;
    }

    // 准备录制计划
    const scheduleItems = updatedSlots.map((slot) => ({
      start: slot.start,
      end: slot.end,
      repeat_policy: slot.repeat_policy,
      customized: slot.customized,
      enabled: slot.enabled,
    }));

    saveConfig({
      camera: [camera.did],
      config: {
        // ...originalConfig,
        record_schedule: {
          type: "customized",
          schedule: scheduleItems,
        },
      },
    });
  };

  const toggleEditMode = () => {
    if (isEditing) {
      // 直接退出编辑模式
      setIsEditing(false);

      // 重置所有选择状态
      setSlots(
        slots.map((slot) => ({
          ...slot,
          selected: false,
        }))
      );

      // 只有在有摄像头的情况下才保存
      if (camera?.did) {
        // 准备录制计划
        const scheduleItems = slots.map((slot) => ({
          start: slot.start,
          end: slot.end,
          repeat_policy: slot.repeat_policy,
          customized: slot.repeat_policy === "customized" ? slot.customized : [],
          enabled: slot.enabled,
        }));

        saveConfig({
          camera: [camera.did],
          config: {
            // ...originalConfig,
            record_schedule: {
              type: "customized",
              schedule: scheduleItems,
            },
          },
        });
      } else {
      }
    } else {
      // 进入编辑模式
      setIsEditing(true);
    }
  };

  const handleBackClick = () => {
    if (isEditing) {
      // 显示确认弹窗
      modalShow(
        "退出后修改将丢失",
        <div className={styles.modalConfirmText}>{/* 退出后修改将丢失 */}</div>,
        (m) => {
          m.destroy();
          setIsEditing(false);
          // 退出编辑模式但不保存更改
        },
        () => {}, // onCancel
        false, // onlyShow
        {
          okBtnText: "确定",
          cancelBtnText: "取消",
          okBtnStyle: {
            color: "var(--emergency-text-color)",
            background: "var(--cancel-btn-background-color)",
          },
          position: "bottom",
        }
      );
    } else {
      // 返回时带上当前slots（自定义时段数据）
      history.replace({
        pathname: "/cameraManagement_app/storageManagement/recordPlan",
        state: { camera, schedule: slots }
      });
    }
  };

  const toggleItemSelection = (id: number) => {
    setSlots(
      slots.map((slot) =>
        slot.id === id ? { ...slot, selected: !slot.selected } : slot
      )
    );
  };

  const deleteSelectedSlots = () => {
    const hasSelectedItems = slots.some((slot) => slot.selected);

    if (!hasSelectedItems) {
      return;
    }

    const updatedSlots = slots.filter((slot) => !slot.selected);
    setSlots(updatedSlots);

    // 当删除后列表为空时，退出编辑模式并调用接口保存空列表
    if (updatedSlots.length === 0) {
      setIsEditing(false);
      
      // 只有在有摄像头的情况下才保存
      if (camera?.did) {
        saveConfig({
          camera: [camera.did],
          config: {
            record_schedule: {
              type: "customized",
              schedule: [], // 保存空列表
            },
          },
        });
      }
    }
  };

  // 转换英文日期为中文显示
  const formatDays = (days: string[]) => {
    return days.map((day) => dayMapping[day] || day);
  };

  // 是否处于加载状态
  // const isLoading = configLoading || saveLoading;

  const [shutDownModalVisible, setShutDownModalVisible] = useState(false);

  // const hasSelectedItems = slots.some((slot) => slot.selected);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Image
          className={styles.arrows}
          src={isDarkMode ? arrowLeftDark : arrowLeft}
          onClick={handleBackClick}
        />
        {slots.length > 0 && (
          <Image
            className={styles.edit}
            style={{width:40,height:40}}
            src={
              isEditing
                ? isDarkMode
                  ? finishDark
                  : finish
                : isDarkMode
                ? editDark
                : edit
            }
            onClick={toggleEditMode}
          />
        )}
      </div>
      <div className={styles.title}>{isEditing ? "编辑" : "自定义"}</div>

      {slots.length > 0 ? (
        <div className={styles.timeSlotList}>
          {slots.map((slot) => (
            <div
              key={slot.id}
              className={styles.timeSlotItem}
              onClick={() => isEditing && toggleItemSelection(slot.id)}
            >
              <div className={styles.timeSlotContent}>
                <div>
                  <div
                    className={styles.timeRange}
                  >{`${slot.start}-${slot.end}`}</div>
                  <div className={styles.daysRow}>
                    <span className={styles.shutdownTag}>关机</span>
                    {slot.repeat_policy === "once" && (
                      <span className={styles.dayTag}>仅一次</span>
                    )}
                    {slot.repeat_policy === "everyday" && (
                      <span className={styles.dayTag}>每天</span>
                    )}
                    {slot.repeat_policy === "workday" && (
                      <span className={styles.dayTag}>工作日</span>
                    )}
                    {slot.repeat_policy === "holiday" && (
                      <span className={styles.dayTag}>节假日</span>
                    )}
                    {slot.repeat_policy === "customized" &&
                      formatDays(slot.customized).map((day, dayIndex) => (
                        <span key={dayIndex} className={styles.dayTag}>
                          {day}
                        </span>
                      ))}
                  </div>
                </div>
                {isEditing ? (
                  <Checkbox
                    checked={slot.selected}
                    className={styles.checkbox}
                  />
                ) : (
                  <Switch
                    checked={slot.enabled}
                    onChange={(checked) => handleSwitchChange(slot.id, checked)}
                    className={styles.switch}
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className={styles.emptyState}>
          <Image
            src={isDarkMode ? listNullDark : listNull}
            className={styles.emptyIcon}
          />
          <div className={styles.emptyText}>列表为空</div>
        </div>
      )}

      <div className={styles.addButtonWrapper}>
        {(!isEditing || slots.length === 0) && (
          <Image
            className={styles.addButton}
            src={add}
            onClick={() => setShutDownModalVisible(true)}
          />
        )}
      </div>

      {isEditing && (
        <div className={styles.deleteContainer}>
          <div className={styles.deleteButton} onClick={deleteSelectedSlots}>
            <Image
              src={isDarkMode ? deleteDark : deleteIcon}
              className={styles.deleteIcon}
            />
            <div className={styles.deleteText}>删除</div>
          </div>
        </div>
      )}

      {shutDownModalVisible && (
        <div
          style={{
            position: "fixed",
            zIndex: 10,
            top: 35,
            left: 0,
            width: "100vw",
            height: "100vh",
            background: "var(--background-color)",
          }}
        >
          <ShutDown
            onClose={() => setShutDownModalVisible(false)}
            onFinish={() => {
              setShutDownModalVisible(false);
              if (camera?.did) {
                fetchConfig({ camera: camera.did });
              }
            }}
            camera={camera}
          />
        </div>
      )}
    </div>
  );
};

export default Customize;
