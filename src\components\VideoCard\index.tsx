import './index.scss';
interface Card {
  imgUrl: string
  name: string
  title: string
  type: boolean
}

/**
 * 
 * @param props 
 * imgurl:图片路径地址
 * name；片名
 * title：描述
 * type：true为高的样式，false为矮的样式
 * @returns 
 */



const Videocard = (props: Card) => {
  const { imgUrl, name, title, type } = props;
  return (

    <div className='cardStyle'>
        <div className='imgStyle'>
            <img src='https://cdn.pixabay.com/photo/2015/06/09/11/25/eiffel-tower-803488_1280.jpg' alt=""  style={{height: type? '134px':'76px',width:type? '106px':'121px',borderRadius:type? '0px':'5px'}}/>
        </div>
        <div >
            <span className='nameStyle' style={{fontSize:type?'17px':'12px'}}>{name}</span>
        </div>
        <div >
            <span className='titleStyle'  style={{color:type?'#000':'#a3a4a6'}}>{title}</span>
        </div>
    </div>
  )
}

export default Videocard;