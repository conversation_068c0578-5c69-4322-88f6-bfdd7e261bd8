// import Player from "xgplayer/es/player";
// import MonitorPlayer, { PlayerType } from "../MonitorPlayer/MonitorPlayer";

// // camera1设定为主摄，camera2为副摄
// interface IDualCamera {
//   cameraRef1: React.MutableRefObject<Player | null>
//   cameraRef2: React.MutableRefObject<Player | null>
//   urls: {
//     camera1: string
//     camera2: string
//   }
//   types: {
//     camera1: PlayerType,
//     camera2: PlayerType,
//   }
//   mediaNames: {
//     camera1: string
//     camera2: string
//   }
//   poster: {
//     camera1: string
//     camera2: string
//   }
// }

// const DualCamera = (props: IDualCamera) => {
//   const { cameraRef1, cameraRef2, urls, types, mediaNames, poster } = props;
//   return (
//     <>
//       <MonitorPlayer baseConfig={{ url: urls.camera1, type: types.camera1, mediaName: mediaNames.camera1, }} cameraRef={cameraRef1} dualOptions={{
//         urls: {
//           main: urls.camera1,
//           secondary: urls.camera2
//         },
//         poster: {
//           main: poster.camera1,
//           secondary: poster.camera2
//         },
//         cameraRef_secondary: cameraRef2
//       }} />
//       <MonitorPlayer baseConfig={{ url: urls.camera2, type: types.camera2, mediaName: mediaNames.camera2, }} cameraRef={cameraRef2} dualOptions={{
//         urls: {
//           main: urls.camera2,
//           secondary: urls.camera1
//         },
//         poster: {
//           main: poster.camera2,
//           secondary: poster.camera1
//         },
//         cameraRef_secondary: cameraRef1
//       }} />
//     </>
//   )
// }

// export default DualCamera;