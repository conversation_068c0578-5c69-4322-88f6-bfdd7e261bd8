import React from 'react';
import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import { useTheme } from '@/utils/themeDetector';
import delete_dark from '@/Resources/icon/delete.png';
import delete_light from '@/Resources/icon/delete_white.png'; 
import pen_dark from '@/Resources/icon/pen_light.png';
import pen_light from '@/Resources/icon/pen_dark.png';
import seen_dark from '@/Resources/icon/seen_light.png';
import seen_light from '@/Resources/icon/seen_dark.png';

interface ActionButtonsProps {
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onShare: (id: string) => void;
  onExitShare?: (id: string) => void;
  id: string;
  isOne?: boolean;
  isShared?: boolean;
}

// 操作按钮组件
const ActionButtons: React.FC<ActionButtonsProps> = ({ 
  onEdit, 
  onDelete, 
  onShare, 
  onExitShare,
  id, 
  isOne,
  isShared = false
}) => {
  const { isDarkMode } = useTheme();
  return (
    <div className={styles.actionButtons}>
      {isShared ? (
        <div className={styles.actionBtn} onClick={() => onExitShare?.(id)}>
          <PreloadImage src={require('@/Resources/camMgmtImg/delete.png')} alt="退出分享" />
        </div>
      ) : isOne ? (
        <div className={styles.actionBtn} onClick={() => onDelete(id)}>
          <PreloadImage src={isDarkMode ?  delete_light:delete_dark } alt="删除" />
        </div>
      ) : (
        <>
          <div className={styles.actionBtn} onClick={() => onDelete(id)}>
            <PreloadImage src={isDarkMode ?  delete_light:delete_dark } alt="删除" />
          </div>
          <div className={styles.actionBtn} onClick={() => onEdit(id)}>
            <PreloadImage src={isDarkMode ?  pen_light:pen_dark } alt="编辑" />
          </div>
          <div className={styles.actionBtn} onClick={() => onShare(id)}>
            <PreloadImage src={isDarkMode ?  seen_light:seen_dark } alt="扫描" />
          </div>
        </>
      )}
    </div>
  );
};

export default ActionButtons; 