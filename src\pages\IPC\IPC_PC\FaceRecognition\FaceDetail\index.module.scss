.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: var(--desktop-modal-bg-color);
  padding: 16px;
  box-sizing: border-box;
}

.faceDetailModal {
  :global {
    .ant-modal-content {
      border-radius: 16px;
      overflow: hidden;
      padding: 0;
      background-color: var(--desktop-modal-bg-color);
    }

    .ant-modal-header {
      display: none;
    }

    .ant-modal-body {
      padding: 0;
      position: relative;
      background-color: var(--desktop-modal-bg-color);
    }
  }
}

.deleteSuccessToast {
  position: absolute;
  top: 80%;
  left: 50%;
  background-color: var(--thinLine-background-color);
  color: var(--title-color);
  border-radius: 16px;
  padding: 8px 16px;
  z-index: 1000;
  font-size: 14px;
  transform: translate(-50%, -50%);
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 24px;
  position: relative;
}

.closeButton {
  position: absolute;
  left: 16px;
  font-size: 16px;
  cursor: pointer;
  line-height: 1;
  color: var(--title-color);

  &:hover {
    color: var(--title-color);
  }
}

.modalTitle {
  font-size: 20px;
  font-weight: 500;
  color: var(--title-color);
  text-align: center;
}

.modalContent {
  display: flex;
}

.leftSection {
  width: 220px;
  border-right: 1px solid var(--thinLine-background-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  position: relative;

  .avatarContainer {
    width: 100px;
    height: 100px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    overflow: hidden;

    .avatarPlaceholder {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .nameLabel {
    font-size: 18px;
    color: var(--title-color);
    text-align: center;
    margin-top: 8px;
    cursor: pointer;
    max-width: 180px;
    word-break: break-all;
    
    &.unmarkedName {
      color: var(--primary-color);
      border: 1px dashed var(--primary-color);
      padding: 6px 12px;
      border-radius: 6px;
      
      .addRemarkText {
        font-size: 16px;
        color: var(--primary-color);
      }
    }
  }

  .nameInput {
    width: 100%;
    max-width: 180px;
    text-align: center;
    margin-top: 8px;
  }
}

.rightSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tabsContainer {
  display: flex;
  flex-direction: column;
}

.tabButtons {
  display: flex;
  background-color: var(--desktop-modal-bg-color);
  border-radius: 12px;
  margin: 20px;
  color: var(--subtitle-text-color);
  overflow: hidden;
}

.tabButton {
  flex: 1;
  text-align: center;
  padding: 12px;
  cursor: pointer;
  font-size: 14px;
  color: var(--title-color);
  transition: all 0.3s;

  &.active {
    background-color: var(--tab-active-bg-color);
    color: var(--title-color);
    font-weight: 500;
    border-radius: 12px;
  }
}

.tabContent {
  flex: 1;
  overflow-y: auto;
  /* Webkit 滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--tab-active-bg-color);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(52, 130, 255, 0.8);
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--tab-active-bg-color) transparent;
}

.nameLabel {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
  user-select: none;
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 16px;

  // &:hover {
  //   // background-color: #f5f5f5;
  // }
}

.nameInput {
  width: 140px;
  height: 32px;
  text-align: center;
  font-size: 16px;
  border-radius: 4px;
  background-color: var(--cancel-btn-background-color) !important;
  border: none;
  color: var(--text-color);
}

.actionButtons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px 16px 16px;

  .deleteUserButton {
    color: var(--emergency-text-color);
    background: var(--cancel-btn-background-color);
    font-size: 17px;
    box-shadow: none;
    padding: 0 12px;
    height: 40px;
    border-radius: 16px;
    border: none;
    margin-right: 5px;

    &:hover {
      color: var(--emergency-text-color);
      background: var(--cancel-btn-background-color) !important;
    }
  }

  .saveButton {
    width: 120px;
    height: 40px;
    border-radius: 16px;
    font-size: 17px;
    background-color: var(--primary-color);
    border-color: var(--primary-color);

    &:hover {
      background-color: #4096ff;
      border-color: #4096ff;
    }
  }
}

.editButtonGroup {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: auto;

  .exitEditButton {
    height: 40px;
    padding: 0 24px;
    border-radius: 16px;
    background: var(--cancel-btn-background-color);
    font-size: 17px;
    color: var(--title-color);
    font-weight: 500;
    border: none;

    &:hover {
      background: var(--cancel-btn-background-color) !important;
      border: var(--cancel-btn-background-color) !important;
      color: var(--title-color) !important;
    }
  }

  .deletePhotosButton {
    height: 40px;
    padding: 0 36px;
    border-radius: 16px;
    background: var(--cancel-btn-background-color);
    font-size: 17px;
    font-weight: 500;
    color: var(--emergency-text-color);
    border: none;
    font-size: 17px;

    &:hover {
      background: var(--cancel-btn-background-color) !important;
      border: var(--cancel-btn-background-color) !important;
    }

    &:disabled {
      background-color: var(--cancel-btn-background-color);
      color: var(--title-color);
    }
  }
}

.photoTab {
  padding: 0 20px 20px;
  max-height: 400px;

  .albumSection {
    margin-bottom: 24px;
    min-height: 350px;

    &:last-child {
      margin-bottom: 0;
    }

    .sectionTitle {
      font-size: 14px;
      color: #8c93b0;
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .editButton {
        color: var(--title-color);
        cursor: pointer;
        font-size: 16px;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .photoGrid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 12px;

      // @media (max-width: 1200px) {
      //   grid-template-columns: repeat(3, 1fr);
      // }
    }

    .photoItem {
      position: relative;
      height: 0;
      padding-bottom: 100%;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      cursor: pointer;

      .photo {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .selectMark {
        position: absolute;
        right: 6px;
        bottom: 6px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;

        &.selected {
          background-color: var(--primary-color);
          color: white;
        }

        .emptyCircle {
          width: 22px;
          height: 22px;
          border-radius: 50%;
          border: 2px solid var(--thinLine-background-color);
          background-color: rgba(255, 255, 255, 0.3);
          box-sizing: border-box;
        }
      }

      .selectedMark {
        position: absolute;
        right: 6px;
        top: 6px;
        width: 24px;
        height: 24px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }
    }

    .addPhotoBox {
      position: relative;
      height: 0;
      padding-bottom: 100%;
      background-color: rgba(0, 0, 0, 0.08);
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background-color: var(--desktop-modal-bg-color);
      }

      .plusIcon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;

        &::before,
        &::after {
          content: "";
          position: absolute;
          background-color: #ccc;
        }

        &::before {
          width: 24px;
          height: 2px;
          top: 11px;
          left: 0;
        }

        &::after {
          width: 2px;
          height: 24px;
          top: 0;
          left: 11px;
        }
      }

      .addText {
        position: absolute;
        bottom: 12px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 12px;
        color: #8c93b0;
      }
    }
  }
}

.videoTab {
  padding: 0 20px 20px;
  overflow-y: auto;
  max-height: 400px;
}

.videoDateGroup {
  margin-bottom: 16px;
  min-height: 334px;
}

.videoDateHeader {
  font-size: 14px;
  color: #8c93b0;
  padding: 8px 0;
}

.videoDateDivider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 8px 0;
}

.videoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.videoItemLeft {
  display: flex;
  align-items: center;
}

.videoAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
}

.videoInfo {
  display: flex;
  flex-direction: column;
}

.videoTime {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
}

.videoDesc {
  font-size: 14px;
  color: var(--subtitle-text-color);
}

.videoItemRight {
  display: flex;
  align-items: center;
}

.videoThumb {
  width: 100px;
  height: 64px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 12px;
  cursor: pointer;
}

.reportButton {
  min-height: 32px;
  padding: 5px 15px;
  border-radius: 16px;
  color: #f5a623;
  background-color: rgba(245, 166, 35, 0.1);
  border: none;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    color: #f5a623 !important;
    background-color: rgba(245, 166, 35, 0.1) !important;
  }
}

.emptyVideoContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;

  .emptyIcon {
    width: 100px;
    height: 64px;
    border-radius: 50%;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: "?";
      font-size: 40px;
      color: #ccc;
    }
  }

  .emptyText {
    font-size: 14px;
    color: var(--list-value-text-color);
  }
}

.reportConfirmModal {
  :global {
    .ant-modal-confirm-body {
      text-align: center;
    }

    .ant-modal-confirm-btns {
      text-align: center;
      margin-top: 24px;

      .ant-btn + .ant-btn {
        margin-left: 16px;
      }
    }
  }
}

// 删除用户确认弹窗
.deleteConfirmModal {
  :global {
    .ant-modal-content {
      border-radius: 16px;
      overflow: hidden;
      background-color: var(--desktop-modal-bg-color);
    }
  }
}

.deleteConfirmContent {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.deleteConfirmText {
  font-size: 18px;
  text-align: center;
  margin-bottom: 32px;
  font-weight: 500;
  color: var(--text-color);
}

.deleteConfirmButtons {
  display: flex;
  justify-content: center;
  gap: 16px;
  width: 100%;

  .cancelButton {
    width: 45%;
    height: 40px;
    border-radius: 16px;
    font-size: 17px;
    font-weight: 500;
    border: none;
    background: var(--cancel-btn-background-color);
    color: var(--title-color);

    &:hover {
      background: var(--cancel-btn-background-color) !important;
      color: var(--title-color) !important;
    }
  }

  .confirmDeleteButton {
    width: 45%;
    height: 40px;
    border-radius: 16px;
    font-size: 17px;
    font-weight: 500;
    background: var(--cancel-btn-background-color);
    border: none;
    color: var(--emergency-text-color);

    &:hover {
      background: var(--cancel-btn-background-color) !important;
      color: var(--emergency-text-color) !important;
    }
  }
  
}
