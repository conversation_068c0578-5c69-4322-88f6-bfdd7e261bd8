.cardContainer {
  background-color: var(--componentcard-bg-color);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.cardHeader {
  padding: 14px 20px;
  display: flex;
  align-items: center;
}

.logoArea {
  display: flex;
  align-items: center;
}

.avatar {
  width: 32px;
  height: 32px;
  background-color: #D7D7D7;
  border-radius: 6px;
  margin-right: 8px;
  position: relative;
  overflow: hidden;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

.cardContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: 5px;
}

.statusInfo {
  text-align: center;
}

.statusTitle {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1.2;
}

.statusSubtitle {
  font-size: 16px;
  color: var(--list-value-text-color);
}

.cardFooter {
  padding: 15px 20px 16px;
}

.actionButton {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  background-color: var(--componentcard-btn-bg-color);
  border: none;
  font-size: 16px;
  font-weight: 500;
  border-radius: 16px;
  color: var(--title-color);
  cursor: pointer;
  
  // &:hover {
  //   background-color: #e8e8e8;
  // }
}

.devButton {
  position: absolute;
  bottom: 5px;
  right: 5px;
  font-size: 10px;
  opacity: 0.3;
  background: none;
  border: none;
  cursor: pointer;
} 