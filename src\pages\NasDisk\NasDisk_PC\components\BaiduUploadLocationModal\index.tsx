import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, List } from "antd";
import { ArrowLeftOutlined, RightOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import { PreloadImage } from "@/components/Image";
import folderIcon from "@/Resources/nasDiskImg/file-icon.png";
import { getBaiduNetdiskFileList } from "@/api/nasDisk";
import CreateBdFolder from "../CreateBdFolder";

interface BaiduUploadLocationModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (selectedPath: string, displayPath: string) => void;
}

interface BaiduFileItem {
  id: number;
  name: string;
  type: "folder" | "file";
  time: string;
  path: string;
  isDirectory: boolean;
}

const BaiduUploadLocationModal: React.FC<BaiduUploadLocationModalProps> = ({
  visible,
  onClose,
  onSelect,
}) => {
  // 面包屑导航路径
  const [breadcrumbPath, setBreadcrumbPath] = useState<string[]>(['百度网盘']);
  
  // 当前路径
  const [currentPath, setCurrentPath] = useState<string>('/');
  
  // 文件列表
  const [fileList, setFileList] = useState<BaiduFileItem[]>([]);
  
  // 选中的文件夹
  const [selectedFolder, setSelectedFolder] = useState<BaiduFileItem | null>(null);
  
  // 加载状态
  const [loading, setLoading] = useState(false);

  // 新建文件夹相关状态
  const [newFolderModalVisible, setNewFolderModalVisible] = useState<boolean>(false);

  // 使用 useRequest 获取文件列表
  const { run: fetchFileList } = useRequest(
    (params: { path: string }) => {
      setLoading(true);
      // 调用百度网盘接口
      return getBaiduNetdiskFileList({
        action: "remotelist",
        path: params.path,
        order:'name',
        desc:1
        // web: 1, // 添加web参数，确保返回缩略图数据
      })
        .catch((error) => {
          console.error("获取百度网盘文件列表失败", error);
          throw error;
        })
        .finally(() => {
          setLoading(false);
        });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response && response.errno === 0) {
          // 只显示文件夹
          const folders: BaiduFileItem[] = response.list
            .filter((item: any) => item.isdir === 1)
            .map((item: any) => ({
              id: item.fs_id,
              name: item.server_filename,
              type: "folder" as const,
              time: new Date(item.server_mtime * 1000).toLocaleString(),
              path: item.path,
              isDirectory: true,
            }));

          setFileList(folders);
        } else {
          console.error("获取文件列表失败:", response);
          setFileList([]);
        }
      },
      onError: (error) => {
        console.error("加载文件列表出错:", error);
        setFileList([]);
      },
    }
  );

  // 初始化时获取根目录
  useEffect(() => {
    if (visible) {
      fetchFileList({ path: '/' });
      setBreadcrumbPath(['百度网盘']);
      setCurrentPath('/');
      setSelectedFolder(null); // 重置选中状态
    }
  }, [visible, fetchFileList]);

  // 处理文件夹选中（只是标记选中状态，不关闭弹窗）
  const handleFolderSelect = (folder: BaiduFileItem) => {
    setSelectedFolder(folder);
  };

  // 处理文件夹点击（进入子文件夹）
  const handleFolderEnter = (folder: BaiduFileItem) => {
    // 构建新路径
    const newPath = currentPath === '/' ? `/${folder.name}` : `${currentPath}/${folder.name}`;
    
    // 更新导航和路径
    setCurrentPath(newPath);
    setBreadcrumbPath([...breadcrumbPath, folder.name]);
    
    // 清除选中状态，因为进入了新目录
    setSelectedFolder(null);
    
    // 获取子文件夹内容
    fetchFileList({ path: newPath });
  };

  // 处理面包屑导航点击
  const handleBreadcrumbClick = (index: number) => {
    // 清除选中状态，因为切换了目录
    setSelectedFolder(null);
    
    if (index === 0) {
      // 点击根目录
      setCurrentPath('/');
      setBreadcrumbPath(['百度网盘']);
      fetchFileList({ path: '/' });
    } else {
      // 点击中间路径
      const newPath = breadcrumbPath.slice(0, index + 1);
      setBreadcrumbPath(newPath);
      
      // 构建路径
      const pathToFetch = '/' + newPath.slice(1).join('/');
      setCurrentPath(pathToFetch);
      
      // 获取该路径下的内容
      fetchFileList({ path: pathToFetch });
    }
  };

  // 处理确认选择
  const handleConfirm = () => {
    let fullPath: string;
    let displayPath: string;
    
    if (selectedFolder) {
      // 如果选中了文件夹，使用选中文件夹的路径
      fullPath = currentPath === '/' ? `/${selectedFolder.name}` : `${currentPath}/${selectedFolder.name}`;
      displayPath = [...breadcrumbPath, selectedFolder.name].join(' > ');
    } else {
      // 如果没有选中文件夹，使用当前目录
      fullPath = currentPath;
      displayPath = breadcrumbPath.join(' > ');
    }
    
    // 调用选择回调
    onSelect(fullPath, displayPath);
    onClose();
  };

  // 打开新建文件夹弹窗
  const showNewFolderModal = () => {
    setNewFolderModalVisible(true);
  };

  // 关闭新建文件夹弹窗
  const closeNewFolderModal = () => {
    setNewFolderModalVisible(false);
  };

  // 创建文件夹成功后的回调
  const handleFolderCreated = () => {
    // 刷新当前目录
    fetchFileList({ path: currentPath });
    closeNewFolderModal();
    
    // 创建成功后短暂延迟再刷新，确保列表完全更新
    setTimeout(() => {
      fetchFileList({ path: currentPath });
    }, 500);
  };

  // 渲染新建文件夹按钮
  const renderNewFolderButton = () => {
    return (
      <div className={styles.newFolderButton} onClick={showNewFolderModal}>
        <div className={styles.folderIconContainer}>
          <PreloadImage src={folderIcon} alt="folder" className={styles.folderIcon} />
        </div>
        <span className={styles.newFolderText}>新建文件夹</span>
      </div>
    );
  };

  // 渲染面包屑导航
  const renderBreadcrumb = () => {
    return (
      <div className={styles.breadcrumbContainer}>
        {breadcrumbPath.map((item, index) => (
          <React.Fragment key={index}>
            <span 
              className={`${styles.breadcrumbItem} ${index === breadcrumbPath.length - 1 ? styles.breadcrumbCurrent : styles.breadcrumbLink}`}
              onClick={() => handleBreadcrumbClick(index)}
            >
              {item}
            </span>
            {index < breadcrumbPath.length - 1 && (
              <span className={styles.breadcrumbSeparator}> &gt; </span>
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <Modal
      title={null}
      open={visible}
      footer={null}
      onCancel={onClose}
      width={546}
      className={styles.uploadLocationModal}
      closeIcon={null}
      centered
      styles={{
        body: {
          height: "calc(636px - 90px)",
          padding: 0,
          overflow: "hidden",
        },
      }}
    >
      <div className={styles.modalContent}>
        <div className={styles.modalHeader}>
          <ArrowLeftOutlined className={styles.backIcon} onClick={onClose} />
          <span className={styles.modalTitle}>选择上传位置</span>
        </div>
        
        <div className={styles.breadcrumbHeader}>
          {renderBreadcrumb()}
        </div>
        
        <div className={styles.fileListContainer}>
          {loading ? (
            <div className={styles.loadingContainer}>
              <div className={styles.loadingText}>加载中...</div>
            </div>
          ) : fileList.length > 0 ? (
            <List
              className={styles.fileList}
              dataSource={fileList}
              renderItem={(file) => (
                <List.Item 
                  key={file.id}
                  className={`${styles.fileItem} ${selectedFolder?.id === file.id ? 'ant-list-item-selected' : ''}`}
                >
                  <div 
                    className={styles.fileContent}
                    onClick={() => handleFolderSelect(file)}
                    title="点击选择此文件夹为上传位置"
                  >
                    <PreloadImage 
                      src={folderIcon} 
                      alt="folder" 
                      className={styles.fileIcon} 
                    />
                    <div className={styles.fileInfo}>
                      <div className={styles.fileName}>{file.name}</div>
                      <div className={styles.fileTime}>{file.time}</div>
                    </div>
                  </div>
                  <div 
                    className={styles.arrowIcon} 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFolderEnter(file);
                    }}
                    style={{ color: 'var(--text-secondary-color)' }}
                    title="进入文件夹"
                  >
                    <RightOutlined />
                  </div>
                </List.Item>
              )}
            />
          ) : (
            <div className={styles.emptyContainer}>
              <div className={styles.emptyText}>该目录下没有文件夹</div>
            </div>
          )}
        </div>
        
        <div className={styles.footerContainer}>
          <div className={styles.footerLeft}>
            {renderNewFolderButton()}
          </div>
          <div className={styles.footerRight}>
            <Button 
              type="primary" 
              className={styles.confirmButton}
              onClick={handleConfirm}
            >
              确定
            </Button>
          </div>
        </div>
      </div>

      {/* 新建文件夹弹窗 */}
      <CreateBdFolder
        visible={newFolderModalVisible}
        onCancel={closeNewFolderModal}
        onSuccess={handleFolderCreated}
        currentPath={currentPath}
      />
    </Modal>
  );
};

export default BaiduUploadLocationModal; 