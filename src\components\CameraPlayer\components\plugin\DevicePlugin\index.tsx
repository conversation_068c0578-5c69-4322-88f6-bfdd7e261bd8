import { PreloadImage } from "@/components/Image";
import styles from "./index.module.scss";
import device_default_icon from "@/Resources/player/device_default_icon.png";

export interface IDevicePlugin {
  icon?: string;
  title?: string;
  subtitle?: string;
  titleColor?: string;
  subtitleColor?: string;
}

const DevicePlugin = (props: IDevicePlugin) => {
  const { icon = device_default_icon, title, subtitle, titleColor, subtitleColor } = props;
  return (
    <div className={styles.devicePluginContainer}>
      <div className={styles.devicePluginIcon}>
        <PreloadImage className={styles.devicePluginIcon_img} src={icon} alt="device" />
      </div>
      <div className={styles.devicePluginContent}>
        <div className={styles.devicePluginContentTitle} style={{ color: titleColor }}>
          {title}
        </div>
        <div className={styles.devicePluginContentSubtitle} style={{ color: subtitleColor }}>
          {subtitle}
        </div>
      </div>
    </div>
  )
}

export default DevicePlugin;