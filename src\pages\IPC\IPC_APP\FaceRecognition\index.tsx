import { List } from "antd-mobile";
import { Route, useHistory, useRouteMatch } from "react-router-dom";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";

import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";

const FaceRecognition = (props: { children?: React.ReactNode }) => {
  const history = useHistory();
  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();

  return (
    <div className={styles.container}>
      {props.children}
      <Route exact path={path}>
        <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
        <div className={styles.title}>AI功能</div>

        <div className={styles.content}>
          <List className={styles.list}>
            <List.Item
              className={styles.item}
              arrow={true}
              onClick={() => {
                history.push(`${path}/identify`);
              }}
            >
              人脸识别
            </List.Item>
          </List>
        </div>
      </Route>
    </div>
  );
};

export default FaceRecognition;
