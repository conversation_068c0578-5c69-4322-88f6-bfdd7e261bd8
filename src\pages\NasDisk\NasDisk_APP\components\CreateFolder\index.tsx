import React, { useState } from 'react';
import { Button, Toast } from 'antd-mobile';
import styles from './index.module.scss';

interface CreateFolderProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (folderName?: string) => void;
  currentPath?: string;
}

const CreateFolder: React.FC<CreateFolderProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [folderName, setFolderName] = useState<string>('');
  const [creating, ] = useState<boolean>(false);

  // 清除输入框内容
  const handleClearInput = () => {
    setFolderName('');
  };

  // 处理弹窗确定
  const handleConfirm = () => {
    if (!folderName.trim()) {
      Toast.show({
        content: '请输入文件夹名称',
        position: 'bottom',
        duration: 2000,
      });
      return;
    }
    
    // 检查文件夹名称是否包含特殊字符
    const invalidChars = /[\\/:*?"<>|]/;
    if (invalidChars.test(folderName)) {
      Toast.show({
        content: '文件夹名称不能包含以下字符: \\ / : * ? " < > |',
        position: 'bottom',
        duration: 2000,
      });
      return;
    }
    
    // 将文件夹名称传递给父组件处理
    const trimmedFolderName = folderName.trim();
    setFolderName('');
    onSuccess(trimmedFolderName);
  };

  // 处理弹窗取消
  const handleCancel = () => {
    if (creating) return;
    
    setFolderName('');
    onCancel();
  };

  if (!visible) {
    return null;
  }

  return (
    <div className={styles.modalOverlay} onClick={handleCancel}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <div className={styles.modalTitle}>新建文件夹</div>
          <div 
            className={`${styles.modalClose} ${creating ? styles.disabled : ''}`} 
            onClick={creating ? undefined : handleCancel}
          >
            ×
          </div>
        </div>
        <div className={styles.modalBody}>
          <div className={styles.inputContainer}>
            <input
              type="text"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              className={styles.folderNameInput}
              placeholder="请输入文件夹名称"
              maxLength={255}
            />
            {folderName && (
              <div className={styles.clearButton} onClick={handleClearInput}>×</div>
            )}
          </div>
        </div>
        <div className={styles.modalFooter}>
          <Button 
            className={styles.cancelButton}
            onClick={handleCancel}
            disabled={creating}
          >
            取消
          </Button>
          <Button 
            className={styles.confirmButton}
            onClick={handleConfirm}
            disabled={creating || !folderName.trim()}
          >
            {creating ? '创建中...' : '确定'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateFolder;
