.container {
  background-color: var(--background-color);
  min-height: calc(100vh - 35px);
  margin-bottom: 24px;
  position: relative;
  padding: 0 8px;
  overflow: hidden; 
  .title {
    font-size: 32px;
    margin-bottom: 16px;
    color: var(--title-color);
    padding: 0 16px;
  }

  .filmsContainer {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 0 16px;
    overflow-y: auto; 
    max-height: calc(100vh - 217px); // 留出标题和底部删除按钮的空间
    padding-bottom: 80px; // 为底部删除按钮留出空间
  }

  .filmItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .antdCheckbox {
    --adm-color-primary: var(--primary-color);
  }

  .modalConfirmText {
    font-size: 14px;
    line-height: 1.4;
    text-align: center;
    color: var(--text-color);
    padding: 10px 0;
  }

  .deleteContainer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 6px 16px 35px;
    background-color: var(--background-color);
    text-align: center;
    z-index: 10;
    .deletdRecord {
      color: var(--text-color);
    }
  }

  .deleteButton {
    span {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.noDataTip {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100px;
  color: var(--text-secondary-color);
  font-size: 14px;
}
