import { sendToEnvironment } from '@/utils/microAppUtils';

const _LOGOUT_BD = 'baidu_logout';

export const logOutBD = (callback: (res: any) => void) => { 
  sendToEnvironment(
    { methodName: _LOGOUT_BD, params: {} },
    { params: { cmd: "unbind" } },
    (response: any) => {
      if (response.code === 0 || response.cmd === 'logoutBD') {
        callback(response);
      } else {
        console.error(`退出失败:${response.msg}`);
      }
    }
  )
}