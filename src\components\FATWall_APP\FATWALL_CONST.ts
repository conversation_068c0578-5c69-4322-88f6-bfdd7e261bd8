export interface IFilterTypeList {
  type: string;
  typeList: {
    title: string;
    key: string;
    type?: string
  }[]
}

export interface filterItemType { sort_type: 0 | 1, asc: 0 | 1 } // 0降序 1升序 0时间 1评分


export const filterTypeList: IFilterTypeList[] = [
  {
    type: 'resolution', typeList: [
      { key: '4k', title: '4K' },
      { key: 'blu-ray', title: '蓝光' },
      { key: '1080p', title: '1080P' },
      { key: '720p', title: '720P' },
      { key: 'other-resolution', title: '其他' }
    ]
  },
  {
    type: 'hdr', typeList: [
      { key: 'dolby-vision', title: '杜比视界' },
      { key: 'hdr10+', title: 'HDR10+' },
      { key: 'hdr10', title: 'HDR10' },
      { key: 'sdr', title: 'SDR' },
      { key: 'other-quality', title: '其他' }
    ]
  },
  {
    type: 'region', typeList: [
      { key: 'mainland', title: '中国大陆' },
      { key: 'hk', title: '中国香港' },
      { key: 'tw', title: '中国台湾' },
      { key: 'us', title: '美国' },
      { key: 'jp-kr', title: '日韩' },
      { key: 'southeastAsia', title: '东南亚' },
      { key: 'uk', title: '英国' },
      { key: 'france', title: '法国' },
      { key: 'spain', title: '西班牙' },
      { key: 'italy', title: '意大利' },
      { key: 'germany', title: '德国' },
      { key: 'other-region', title: '其他' }
    ]
  }, {
    type: 'year', typeList: [
      { key: '2025', title: '2025' },
      { key: '2024', title: '2024' },
      { key: '2023', title: '2023' },
      { key: '2022', title: '2022' },
      { key: '2021', title: '2021' },
      { key: '2020', title: '2020' },
      { key: '2010s', title: '10年代' },
      { key: '2000s', title: '00年代' },
      { key: '1990s', title: '90年代' },
      { key: '1980s', title: '80年代' },
      { key: 'other-year', title: '更早' },
    ]
  },
  {
    type: 'kind', typeList: [
      { key: 'drama', title: '剧情' },
      { key: 'comedy', title: '喜剧' },
      { key: 'action', title: '动作' },
      { key: 'romance', title: '爱情' },
      { key: 'sci-fi', title: '科幻' },
      { key: 'war', title: '战争' },
      { key: 'disaster', title: '灾难' },
      { key: 'wu-xia', title: '武侠' },
      { key: 'animation', title: '动画' },
      { key: 'suspense', title: '悬疑' },
      { key: 'thriller', title: '惊悚' },
      { key: 'terror', title: '恐怖' },
      { key: 'crime', title: '犯罪' },
      { key: 'music', title: '音乐' },
      { key: 'songAndDance', title: '歌舞' },
      { key: 'biography', title: '传记' },
      { key: 'records', title: '记录' },
      { key: 'history', title: '历史' },
      { key: 'west', title: '西部' },
      { key: 'fantasy', title: '奇幻' },
      { key: 'adventure', title: '冒险' },
      { key: 'other', title: '其他' },
    ]
  },
  {
    type: 'collect', typeList: [
      { key: 'collect', title: '收藏' },
      { key: 'viewed', title: '已观看' },
    ]
  }
]

export const defaultFiltersByApp: { [key: string]: string } = {
  classes: "all-classes",
  resolution: "all-resolution",
  hdr: "all-hdr",
  region: "all-region",
  year: "all-year",
  kind: "all-kind",
  collect: "all-collect",
}

export const defaultFiltersByPc: { [key: string]: string } = {
  classes: "all-classes",
  resolution: "all-resolution",
  hdr: "all-hdr",
  region: "all-region",
  year: "all-year",
  kind: "all-kind",
}

export const defaultFiltersByTV: { [key: string]: string } = {
  classes: "all-classes",
  resolution: "all-resolution",
  hdr: "all-hdr",
  region: "all-region",
  year: "all-year",
  kind: "all-kind",
  collect: "all-collect",
  sort: "latest-add"
}