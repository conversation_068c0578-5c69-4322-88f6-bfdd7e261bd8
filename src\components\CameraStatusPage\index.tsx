import React, { ReactNode } from 'react';
import { Button } from 'antd-mobile';
import styles from './index.module.scss';

export interface CameraStatusPageProps {
  // Header props
  leftHeaderContent?: ReactNode;
  rightHeaderContent?: ReactNode;
  title: string;
  
  // Content props
  icon?: ReactNode;
  mainMessage?: string;
  subMessage?: string;
  customContent?: ReactNode;
  
  // Footer props
  buttonText?: string;
  onButtonClick?: () => void;
  buttonDisabled?: boolean;
  showButton?: boolean;
  
  // Optional additional class name
  className?: string;
}

const CameraStatusPage: React.FC<CameraStatusPageProps> = ({
  leftHeaderContent,
  rightHeaderContent,
  title,
  icon,
  mainMessage,
  subMessage,
  customContent,
  buttonText,
  onButtonClick,
  buttonDisabled = false,
  showButton = true,
  className,
}) => {
  return (
    <div className={`${styles.container} ${className || ''}`}>
      <div className={styles.header}>
        {leftHeaderContent}
        {rightHeaderContent && <div className={styles.rightHeaderContent}>{rightHeaderContent}</div>}
      </div>

      <div className={styles.title}>{title}</div>

      <div className={styles.cameraStatusContainer}>
        <div className={styles.mainContent}>
          {icon && (
            <div className={styles.iconContainer}>
              {icon}
            </div>
          )}
          
          {customContent || (
            <div className={styles.textContainer}>
              {mainMessage && <div className={styles.message}>{mainMessage}</div>}
              {subMessage && <div className={styles.subMessage}>{subMessage}</div>}
            </div>
          )}
        </div>
      </div>
      
      {showButton && (
        <div className={styles.footer}>
          <Button
            block
            color="primary"
            className={styles.actionButton}
            onClick={onButtonClick}
            disabled={buttonDisabled}
          >
            {buttonText}
          </Button>
        </div>
      )}
    </div>
  );
};

export default CameraStatusPage; 