.cameraManagementContainer {
  overflow: auto;
  width: 100%;
  height: 100vh;
  margin: 0;
  // overflow-y: auto; /* 启用滚动 */
  // scrollbar-width: none; /* Firefox */
  // -ms-overflow-style: none; /* IE/Edge */

  // padding-top: 35px; // 给手机顶部留出空间
  background: var(--background-color); // 留出顶部后设置空白区背景色

  :global {
    .adm-image {
      margin-top: 0;
    }
  }
}

.top {
  width: 100%;
  height: 35px;
}

.content {
  width: 100%;
  height: calc(100% - 35px);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 16px;
  color: #999;
}

.loadingText {
  margin-top: 12px;
  font-size: 14px;
}
