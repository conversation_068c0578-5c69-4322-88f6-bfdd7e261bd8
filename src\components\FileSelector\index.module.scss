.file_selector_container {
  width: 100%;
  height: calc(100% - 60px);
  padding: 5px 28px;
}

.file_selector_content {
  width: 100%;
  margin-top: 20px;
  height: calc(100% - 50px);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: none;
  scroll-behavior: smooth;
}

.file_selector_empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 20px;
}

.file_selector_empty_img {
  height: 64px;
}

.file_selector_empty_span {
  font-family: MiSans W;
  font-weight: 400;
  font-size: 15px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  color: var(--subtitle-text-color);
}

.breadcrumb_container {
  width: 100%;
  height: 30px;
  display: flex;
}

.breadcrumb_content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.breadcrumb_next_container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 100%;
}

.breadcrumb_next_img {
  height: 12px;
}

.breadcrumb_item {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  padding: 8px 12px;
  background: rgba(140, 147, 176, 0.1);
  color: rgba(140, 147, 176, 1);

  font-family: MiSans;
  font-weight: 500;
  font-size: 11px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
}

.breadcrumb_item_active {
  color: rgba(255, 178, 29, 1);
  background: rgba(255, 178, 29, 0.1);
}

// 文件夹卡片css样式

.file_dir_component_container {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 16px 0;
}

.file_dir_component_left {
  width: 300px;
  display: flex;
  gap: 0 16px;
  cursor: pointer;
}

.file_dir_component_left_img_container {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file_dir_component_icon {
  height: 38px;
}

.file_dir_component_content {
  flex: 1;
  display: flex;
  align-items: center;

  span {
    font-family: MiSans;
    font-weight: 500;
    font-size: 17px;
    line-height: 100%;
    letter-spacing: 0px;
    color: #000;
  }
}

.file_dir_component_right {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: flex-end;
}

.file_dir_component_right_img {
  height: 26px;
  cursor: pointer;
}
