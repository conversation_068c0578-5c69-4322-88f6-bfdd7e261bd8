import { getDeviceType } from "@/utils/DeviceType";
import { Spin } from "antd";
import { useEffect } from "react";
import { useHistory } from "react-router-dom";
import styles from './index.module.scss';

const NasDisk = () => {
  const deviceType = getDeviceType();
  const history = useHistory();

  useEffect(() => {
    switch (deviceType) {
      case 0: return history.push('/baiduNetdisk_app');
      case 1: return history.push('/baiduNetdisk_pc');
    }
  }, [deviceType, history])

  return (
    <div className={styles.loadingContainer}>
      <Spin size="large" />
    </div>
  )
}

export default NasDisk;