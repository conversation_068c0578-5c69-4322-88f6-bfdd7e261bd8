import { 
  registerBaiduNetdiskDevice, 
  bindBaiduNetdiskDevice,
  DeviceRegisterParams,
  DeviceBindParams
} from '@/api/nasDisk';
import { MD5 } from 'crypto-js';

// 设备类型，实际项目中应从配置或环境变量获取
const DEVICE_TYPE = '202503071615331041';

/**
 * 获取设备唯一标识
 * 实际项目中应该获取真实的设备MAC地址或序列号
 * @returns 设备唯一标识的MD5值
 */
export const getDeviceIdentifier = (): string => {
  // 在实际项目中，这里应该获取真实的设备MAC地址或序列号
  // 例如：通过原生桥接获取设备MAC地址
  
  // 这里使用一个固定值进行模拟
  const mockDeviceMAC = '00:1A:2B:3C:4D:5E';
  
  // 返回MD5加密后的设备标识
  const deviceId = MD5(mockDeviceMAC).toString();
  console.log('生成的设备标识:', deviceId);
  return deviceId;
};

/**
 * 注册百度网盘设备
 * @returns 设备ID
 */
export const registerDevice = async (): Promise<string> => {
  try {
    const deviceAddr = getDeviceIdentifier();
    
    const params: DeviceRegisterParams = {
      method: 'register',
      device_type: DEVICE_TYPE,
      device_addr: deviceAddr
    };
    
    console.log('发送设备注册请求，参数:', params);
    
    const response = await registerBaiduNetdiskDevice(params);
    
    console.log('设备注册响应:', response);
    
    if (response.errno === 0) {
      // 注册成功，保存设备ID到本地存储
      localStorage.setItem('baiduNetdisk_device_id', response.device_id);
      console.log('设备注册成功，设备ID:', response.device_id);
      return response.device_id;
    } else {
      console.error('设备注册失败:', response.errmsg, '错误码:', response.errno);
      throw new Error(`设备注册失败: ${response.errmsg} (错误码: ${response.errno})`);
    }
  } catch (error) {
    console.error('设备注册过程中发生错误:', error);
    
    // 由于跨域或其他网络问题，可能会导致请求失败
    // 在开发环境中，我们使用模拟数据来继续流程
    if (process.env.NODE_ENV === 'development') {
      console.log('开发环境中使用模拟设备ID');
      const mockDeviceId = '104771607rs1607808';
      localStorage.setItem('baiduNetdisk_device_id', mockDeviceId);
      return mockDeviceId;
    }
    
    throw error;
  }
};

/**
 * 绑定百度网盘设备到用户账号
 * @param accessToken 用户访问令牌
 * @param deviceName 设备名称（可选）
 * @returns 绑定结果
 */
export const bindDevice = async (accessToken: string, deviceName?: string): Promise<boolean> => {
  try {
    // 从本地存储获取设备ID，如果没有则先注册设备
    let deviceId = localStorage.getItem('baiduNetdisk_device_id');
    
    if (!deviceId) {
      console.log('没有找到设备ID，开始注册设备');
      deviceId = await registerDevice();
    } else {
      console.log('使用已存在的设备ID:', deviceId);
    }
    
    const params: DeviceBindParams = {
      method: 'bind',
      access_token: accessToken,
      device_id: deviceId,
      device_name: deviceName || '网盘NAS设备'
    };
    
    console.log('发送设备绑定请求，参数:', params);
    
    const response = await bindBaiduNetdiskDevice(params);
    
    console.log('设备绑定响应:', response);
    
    if (response.errno === 0) {
      // 绑定成功，保存绑定状态到本地存储
      localStorage.setItem('baiduNetdisk_bound', 'true');
      console.log('设备绑定成功');
      return true;
    } else {
      console.error('设备绑定失败:', response.errmsg, '错误码:', response.errno);
      
      // 特殊处理：如果是超过设备绑定用户数限制的错误
      if (response.errno === 42103 && response.last_user) {
        console.warn(`设备已绑定到其他用户: ${response.last_user}`);
      }
      
      // 在开发环境中，我们模拟绑定成功
      if (process.env.NODE_ENV === 'development') {
        console.log('开发环境中模拟绑定成功');
        localStorage.setItem('baiduNetdisk_bound', 'true');
        return true;
      }
      
      return false;
    }
  } catch (error) {
    console.error('设备绑定过程中发生错误:', error);
    
    // 在开发环境中，我们模拟绑定成功
    if (process.env.NODE_ENV === 'development') {
      console.log('开发环境中模拟绑定成功');
      localStorage.setItem('baiduNetdisk_bound', 'true');
      return true;
    }
    
    return false;
  }
};

/**
 * 初始化百度网盘设备
 * 检查设备是否已注册和绑定，如果没有则进行注册和绑定
 * @param accessToken 用户访问令牌
 * @param deviceName 设备名称（可选）
 * @returns 初始化结果
 */
export const initializeBaiduNetdisk = async (accessToken: string, deviceName?: string): Promise<boolean> => {
  try {
    console.log('开始初始化百度网盘设备');
    
    // 检查是否已经绑定
    const isBound = localStorage.getItem('baiduNetdisk_bound') === 'true';
    
    if (isBound) {
      console.log('设备已绑定，无需重新初始化');
      return true;
    }
    
    console.log('设备未绑定，开始绑定过程');
    // 如果没有绑定，则进行设备绑定（会自动注册设备）
    return await bindDevice(accessToken, deviceName);
  } catch (error) {
    console.error('初始化百度网盘设备过程中发生错误:', error);
    
    // 在开发环境中，我们模拟初始化成功
    if (process.env.NODE_ENV === 'development') {
      console.log('开发环境中模拟初始化成功');
      return true;
    }
    
    return false;
  }
}; 