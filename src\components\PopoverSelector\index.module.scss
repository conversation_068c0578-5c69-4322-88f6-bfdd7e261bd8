.customPopover {
  :global {
    .adm-popover-inner {
      padding: 0;
      background-color: var(--desktop-modal-bg-color) !important;
    }
    .adm-popover-inner-content {
      padding: 0;
    }
    .adm-popover-arrow {
      display: none;
    }
  }

  --z-index: 10030;
}

.popoverContent {
  min-width: 120px;
  background: var(--desktop-modal-bg-color);
  border-radius: 8px;
  // padding: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .optionItem {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;

    &.selected,
    &:active,
    &:hover {
      background-color: var(--option-active-bg) !important;
    }

    .optionLabel {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      font-size: 16px;
      font-weight: 400;
      color: var(--text-color);
      margin: 0 10px;

      &.selected {
        color: var(--option-selected-color) !important;
      }

      .optionSubtitle {
        margin: 4px 0;
        font-family: MiSans;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        letter-spacing: 0px;
        color: var(--subtitle-text-color);

        &.selected {
          color: var(--option-selected-color) !important;
        }
      }
    }

    .checkIcon {
      width: 20px;
      height: 20px;
    }
  }
}

.optionSubtitle {
  margin: 4px 0;
  font-family: MiSans;
  font-weight: 400;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.6);
}
:global{
  .adm-popover-inner{
    background-color: var(--desktop-modal-bg-color);
  }
}
