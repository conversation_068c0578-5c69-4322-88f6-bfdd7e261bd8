// 使用通用SearchSelector组件，提供了多种类型支持:
//- 基础搜索: itemType="default" (默认)
//- 电影搜索: itemType="movie" (自带电影渲染逻辑)
                
//如需更高度自定义，可以:
//1. 通过renderItem提供自定义渲染函数
//2. 通过renderHeader自定义头部
//3. 通过renderFooter自定义底部
                
//必需属性:
//- visible: 是否显示
//- onClose: 关闭回调
//- onSearch: 搜索回调，接收关键词
//- onConfirm: 确认回调，接收选中项
//- searchStatus: 搜索状态 ('idle' | 'loading' | 'success' | 'error' | 'empty')
//- searchResults: 搜索结果数组
//- selectedItem: 当前选中项
//- onSelectItem: 选择项回调

import React, { useState, useEffect, useRef } from 'react';
import { ErrorBlock, Button, Input } from 'antd-mobile';
import { SearchOutline } from 'antd-mobile-icons';
import styles from './index.module.scss';
import content_null_img from '@/Resources/filmWall/content_null.png';
import content_null_img_dark from '@/Resources/filmWall/content_null_dark.png';
import { useTheme } from '@/utils/themeDetector';

export interface SearchItem {
  id: string | number;
  [key: string]: any;
}

// 默认的电影搜索结果项结构
export interface MovieSearchItem extends SearchItem {
  title: string;
  year?: string;
  country?: string;
  genres?: string;
  posterUrl: string;
  director?: string;
}

export type SearchStatus = 'idle' | 'loading' | 'success' | 'error' | 'empty';

export interface SearchSelectorProps<T> {
  visible: boolean;
  onClose: () => void;
  onSearch: (keyword: string) => void;
  onConfirm: (selectedItem: T | null) => void;
  searchStatus: SearchStatus;
  searchResults: T[];
  selectedItem: T | null;
  onSelectItem: (item: T) => void;
  title?: string;
  placeholder?: string;
  confirmButtonText?: string;
  retryButtonText?: string;
  emptyMessage?: string;
  errorMessage?: string;
  renderItem?: (item: T, isSelected: boolean) => React.ReactNode;
  renderHeader?: () => React.ReactNode;
  renderFooter?: () => React.ReactNode;
  initialKeyword?: string;
  itemType?: 'movie' | 'default';
  onResetStatus?: () => void;
  keyField?: string;
}

function SearchSelector<T>({
  visible,
  onClose,
  onSearch,
  onConfirm,
  searchStatus,
  searchResults,
  selectedItem,
  onSelectItem,
  title = '搜索',
  placeholder = '搜索',
  confirmButtonText = '确认',
  retryButtonText = '重试',
  emptyMessage = '没有找到相关结果',
  errorMessage = '搜索失败，请重试',
  renderItem,
  renderHeader,
  renderFooter,
  initialKeyword = '',
  itemType = 'default',
  onResetStatus,
  keyField = 'id',
}: SearchSelectorProps<T>) {
  const [keyword, setKeyword] = useState(initialKeyword);
  const inputRef = useRef<any>(null);
  const { isDarkMode } = useTheme();

  // 当组件显示时，聚焦输入框
  useEffect(() => {
    if (visible) {
      // 当组件显示时，重置输入框内容为初始值
      setKeyword(initialKeyword);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }
  }, [visible, initialKeyword]);

  // 当输入关键词时更新状态
  const handleInputChange = (value: string) => {
    setKeyword(value);
  };

  // 执行搜索
  const handleSearch = () => {
    if (!keyword.trim()) {
      return;
    }
    onSearch(keyword.trim());
  };

  // 清空搜索内容
  const handleClear = () => {
    setKeyword('');
    // 重置搜索状态为idle
    if (onResetStatus) {
      onResetStatus();
    }
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // 处理关闭
  const handleClose = () => {
    setKeyword(''); // 清空输入内容
    if (onResetStatus) {
      onResetStatus(); // 重置搜索状态
    }
    onClose(); // 调用外部关闭回调
  };

  // 处理确认
  const handleConfirm = () => {
    onConfirm(selectedItem);
    // 不在这里清理状态，让父组件决定何时清理
  };

  // 默认的电影搜索结果项渲染
  const renderMovieItem = (item: T, isSelected: boolean) => {
    const movieItem = item as any;
    return (
      <>
        <div className={styles.resultPoster}>
          <img src={Array.isArray(movieItem.poster) ? movieItem.poster[0] : (movieItem.posterUrl || '')} alt={movieItem.origin_name || movieItem.trans_name || movieItem.title || ''} />
        </div>
        <div className={styles.resultInfo}>
          <div className={styles.resultTitle}>{movieItem.origin_name || movieItem.trans_name || movieItem.title || ''}</div>
          {movieItem.year && (
            <div className={styles.resultMeta}>时间: <span>{movieItem.year}</span></div>
          )}
          {movieItem.origin_place && (
            <div className={styles.resultMeta}>国家: <span>{movieItem.origin_place}</span></div>
          )}
          {movieItem.classes && (
            <div className={styles.resultMeta}>类型: <span>{movieItem.classes}</span></div>
          )}
        </div>
        <div className={styles.resultSelect} onClick={(e) => e.stopPropagation()}>
          <div 
            className={`${styles.customRadio} ${isSelected ? styles.selected : ''}`}
            onClick={() => {
              if (isSelected) {
                onSelectItem(null as any);
              } else {
                onSelectItem(item);
              }
            }}
          >
            {isSelected && <div className={styles.innerDot}></div>}
          </div>
        </div>
      </>
    );
  };

  // 默认的普通搜索结果项渲染
  const renderDefaultItem = (item: T, isSelected: boolean) => {
    const anyItem = item as any;
    return (
      <>
        <div className={styles.resultInfo}>
          <div className={styles.resultTitle}>{anyItem.title || anyItem.name || anyItem.origin_name || anyItem.trans_name || `${anyItem.id || anyItem.media_id}`}</div>
        </div>
        <div className={styles.resultSelect} onClick={(e) => e.stopPropagation()}>
          <div 
            className={`${styles.customRadio} ${isSelected ? styles.selected : ''}`}
            onClick={() => {
              if (isSelected) {
                onSelectItem(null as any);
              } else {
                onSelectItem(item);
              }
            }}
          >
            {isSelected && <div className={styles.innerDot}></div>}
          </div>
        </div>
      </>
    );
  };

  // 搜索状态渲染
  const renderSearchState = () => {
    switch (searchStatus) {
      // case 'loading':
      //   return (
      //     <div className={styles.searchStateContainer}>
      //       <div className={styles.loadingContainer}>
      //         <DotLoading />
      //       </div>
      //     </div>
      //   );
      case 'error':
        return (
          <div className={styles.searchStateContainer}>
            <ErrorBlock
              className={styles.errorBlock}
              image={isDarkMode ? content_null_img_dark : content_null_img}
              status="default"
              title={errorMessage}
              description=""
            />
            <Button
              className={styles.retryButton}
              color="primary"
              fill="none"
              onClick={handleSearch}
            >
              {retryButtonText}
            </Button>
          </div>
        );
      case 'empty':
        return (
          <div className={styles.searchStateContainer}>
            <ErrorBlock
              className={styles.errorBlock}
              image={isDarkMode ? content_null_img_dark : content_null_img}
              status="empty"
              title={emptyMessage}
              description=""
            />
          </div>
        );
      case 'success':
        return (
          <div className={styles.searchResultList}>
            {searchResults.map((item) => {
              // 支持自定义主键字段
              const itemKey = (item as any)[keyField] ?? (item as any).id ?? (item as any).index;
              const selectedKey = selectedItem ? ((selectedItem as any)[keyField] ?? (selectedItem as any).id ?? (selectedItem as any).index) : null;
              const isSelected = selectedKey !== null && itemKey === selectedKey;
              
              return (
                <div
                  key={itemKey}
                  className={`${styles.searchResultItem} ${isSelected ? styles.selected : ''}`}
                  onClick={() => {
                    if (isSelected) {
                      onSelectItem(null as any);
                    } else {
                      onSelectItem(item);
                    }
                  }}
                >
                  {renderItem ? (
                    renderItem(item, isSelected)
                  ) : itemType === 'movie' ? (
                    renderMovieItem(item, isSelected)
                  ) : (
                    renderDefaultItem(item, isSelected)
                  )}
                </div>
              );
            })}
          </div>
        );
      default:
        return null;
    }
  };

  // 如果不可见，不渲染
  if (!visible) {
    return null;
  }

  return (
    <div className={styles.matchCorrectionOverlay}>
      {/* 自定义头部 */}
      {renderHeader ? (
        renderHeader()
      ) : (
        <div className={styles.searchContainer}>
          <div className={styles.searchInputWrapper}>
            <div className={styles.searchIconWrapper}>
              <SearchOutline className={styles.searchIcon} />
            </div>
            <Input
              ref={inputRef}
              className={styles.searchInput}
              placeholder={placeholder}
              value={keyword}
              onChange={handleInputChange}
              onEnterPress={handleSearch}
              autoFocus
              clearable
              onClear={handleClear}
            />
          </div>
          <div className={styles.cancelButton} onClick={handleClose}>
            取消
          </div>
        </div>
      )}

      {/* 搜索结果 */}
      <div className={styles.searchResults}>
        {renderSearchState()}
      </div>

      {/* 自定义底部 */}
      {renderFooter ? (
        renderFooter()
      ) : (
        searchStatus !== 'idle' && searchStatus !== 'loading' && (
          <div className={styles.confirmButtonContainer}>
            <Button
              block
              className={styles.confirmButton}
              color="primary"
              onClick={handleConfirm}
              disabled={searchStatus === 'empty' || searchStatus === 'error' || !selectedItem}
            >
              {confirmButtonText}
            </Button>
          </div>
        )
      )}
    </div>
  );
}

export default SearchSelector; 