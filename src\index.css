body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.focus_item {
  transition: all 0.1s ease-in-out;
}

.focus_item:focus {
  border: 4px solid #fff;
  transform: scale(1) !important;
  border-radius: 16px;
}

* {
  box-sizing: border-box;
}

html,
body,
#root {
  height: 100%;
  overflow: hidden;
}

:root {
  --adm-color-primary: #1677ff;
}

/* 适用于 Webkit 内核（Chrome、Safari、Edge 等）*/
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  background: transparent;
}

/*  适用于 Firefox */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/*  兼容 Firefox，需加上以下 */
*::-webkit-scrollbar {
  display: none;
}