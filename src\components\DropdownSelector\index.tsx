import React, { useRef, useState } from 'react';
import { Form, Input, Mask } from 'antd-mobile';
import './index.scss';

interface Option {
  value: string;
  label: string;
  isVipOption?: boolean;
}

interface DropdownSelectorProps {
  name: string;
  label: React.ReactNode;
  value: string;
  options: Option[];
  onChange: (value: string) => void;
  placeholder?: string;
  isVip?: boolean; // 用户是否为VIP会员
  defaultVipItems?: string[]; // 需要VIP权限的选项values
  onVipOptionClick?: (value: string) => void; // 点击VIP选项的回调
}

const DropdownSelector: React.FC<DropdownSelectorProps> = ({
  name,
  label,
  value,
  options,
  onChange,
  placeholder = '未填写',
  isVip = false,
  defaultVipItems = [],
  onVipOptionClick,
}) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [optionTop, setOptionTop] = useState(0);
  const [optionLeft, setOptionLeft] = useState(0);
  const itemRef = useRef<HTMLDivElement>(null);

  // 处理选项数据，添加VIP标识
  const processedOptions = options.map(option => ({
    ...option,
    isVipOption: option.isVipOption || defaultVipItems.includes(option.value)
  }));

  const handleClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    // 获取点击元素的位置
    if (itemRef.current) {
      const rect = itemRef.current.getBoundingClientRect();
      // 设置在表单项的下方偏右位置
      setOptionTop(rect.bottom + 5); // 底部偏移5px
      setOptionLeft(rect.right - 180); // 右对齐菜单宽度(180px)
      setVisible(true);
    }
  };

  const handleSelect = (selectedValue: string, isVipOption: boolean = false) => {
    // 如果是VIP选项但用户不是VIP，则触发VIP选项点击回调
    if (isVipOption && !isVip) {
      if (onVipOptionClick) {
        onVipOptionClick(selectedValue);
      }
      setVisible(false); // 关闭选项弹窗
      return;
    }
    onChange(selectedValue);
    setVisible(false);
  };

  return (
    <div ref={itemRef}>
      <Form.Item
        name={name}
        label={label}
        arrow
        onClick={handleClick}
      >
        <Input 
          readOnly 
          value={value} 
          placeholder={placeholder} 
          style={{ '--text-align': 'right' }} 
        />
      </Form.Item>

      {/* 选项下拉弹窗 */}
      {visible && (
        <Mask visible={visible} opacity={0.5} onMaskClick={() => setVisible(false)}>
          <div className="dropdown-selector"
            style={{
              position: 'fixed',
              top: `${optionTop}px`,
              left: `${optionLeft}px`,
              zIndex: 1002
            }}
          >
            {processedOptions.map(item => (
              <div
                key={item.value}
                className={`option-item ${value === item.value ? 'active' : ''}`}
                onClick={() => handleSelect(item.value, item.isVipOption)}
              >
                {item.label}
                {item.isVipOption && (
                  <span className="vip-tag">VIP专享</span>
                )}
              </div>
            ))}
          </div>
        </Mask>
      )}
    </div>
  );
};

export default DropdownSelector; 