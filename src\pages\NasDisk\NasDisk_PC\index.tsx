import { UserProvider, useUser } from "@/utils/UserContext";
import { Spin } from "antd";
import { FC, useEffect } from "react";
import { Route, Switch, useHistory, useRouteMatch } from "react-router-dom";
import styles from "./index.module.scss";
import TaskManager from "./TaskManager";
import DownloadNas from "./TaskManager/DownloadNas";
import UploadBD from "./TaskManager/UploadBD";
import ScheduledDownload from './ScheduledDownload'
import BDfiles from './BDfiles'
import Synchronization from './Synchronization'
import { useSideBarChange } from "@/layouts/Layout";
import Mine from './Mine'
import { useTheme } from "@/utils/themeDetector";
import useBDSideBar from "@/layouts/sideBarHooks/bd";


const DefaultLayout = () => {
  const history = useHistory();

  useEffect(() => {
    history.push('/baiduNetdisk_pc/bdfiles');
  }, [history])

  return (
    <div className={styles.loadingContainer}>
      <Spin size="large" />
    </div>
  )
}

const NasDiskDesktopContent: FC = (props) => {
  const { path } = useRouteMatch();
  const { setSidePanel } = useSideBarChange(); // 侧边栏变化
  const { bdComponents } = useBDSideBar();
  const { isDarkMode } = useTheme();
  const {  fetchUserInfo } = useUser();

  // 获取用户信息
  useEffect(() => {
    fetchUserInfo();
  }, [fetchUserInfo]);

  // 在主组件中统一设置侧边栏
  useEffect(() => {
    setSidePanel(bdComponents);
  }, [bdComponents, setSidePanel, isDarkMode]);

  return (
    <div className={styles.root_container}>
      <Switch>
        <Route exact path={`${path}/bdfiles`}>
          <BDfiles/>
        </Route>
        <Route exact path={`${path}/scheduledDownload`}>
          <ScheduledDownload />
        </Route>
        <Route exact path={`${path}/synchronization`}>
          <Synchronization />
        </Route>
        <Route exact={true} path={`${path}/taskManager`}>
          <TaskManager />
        </Route>
        <Route exact path={`${path}/taskManager/downloadNas`}>
          <DownloadNas />
        </Route>
        <Route exact path={`${path}/taskManager/uploadBD`}>
          <UploadBD />
        </Route>
        <Route exact path={`${path}/mine`}>
          <Mine />
        </Route>

        {/* 默认内容 */}
        <Route exact={true} path={path}>
          <DefaultLayout />
        </Route>
      </Switch>
    </div>
  )
}

const NasDiskDesktop: FC = (props) => {
  return (
    <UserProvider>
      <NasDiskDesktopContent {...props} />
    </UserProvider>
  )
}

export default NasDiskDesktop;
