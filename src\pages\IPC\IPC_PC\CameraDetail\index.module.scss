.container {
  width: 100%;
  height: 100%;
  display: flex;
  overflow-y: auto;
  scrollbar-width: none;
}

.left {
  width: 870px;
}

.right {
  flex: 1;
  padding: 0 8px 0 22px;
  overflow: auto;

  /* 启用垂直滚动 */
  -webkit-overflow-scrolling: touch;
  /* 启用iOS平滑滚动 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE/Edge */

  & ::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }
}

.right_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
}

.right_opt {
  display: flex;
  align-items: center;
  justify-content: center;
  > div {
    margin-left: 8px;
    cursor: pointer;
  }

  img {
    width: 20px;
    height: 20px;
  }
}

.right_opt_filter {
  width: 45px;
  height: 30px;
  cursor: pointer;
  background-color: var(--card-background-color);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.right_title {
  font-family: MiSans;
  font-weight: 700;
  font-size: 14px;
  letter-spacing: 0px;
  color: var(--text-color);
}

.eventOperate {
  width: 100%;
  height: 60px;
}

.timeAxisContainer {
  width: 100%;
  height: 120px;
  border-top: 1px solid var(--subtitle-text-color);
}

.backLive {
  color: var(--text-color);
}

.temp_video_container {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10050;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
