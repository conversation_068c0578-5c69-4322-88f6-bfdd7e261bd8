declare module 'crypto-js' {
  export function MD5(message: string): {
    toString(): string;
  };
  
  export function SHA1(message: string): {
    toString(): string;
  };
  
  export function SHA256(message: string): {
    toString(): string;
  };
  
  export function HmacSHA1(message: string, key: string): {
    toString(): string;
  };
  
  export function HmacSHA256(message: string, key: string): {
    toString(): string;
  };
  
  export function enc(): void;
  
  export namespace enc {
    export const Hex: any;
    export const Latin1: any;
    export const Utf8: any;
    export const Base64: any;
  }
  
  export function AES(): void;
  
  export namespace AES {
    export function encrypt(message: string, key: string): {
      toString(): string;
    };
    export function decrypt(ciphertext: string, key: string): {
      toString(encoder?: any): string;
    };
  }
} 