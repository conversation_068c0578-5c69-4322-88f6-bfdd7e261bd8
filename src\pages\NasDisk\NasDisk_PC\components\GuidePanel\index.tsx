import React from 'react';
import { Button } from 'antd';
import styles from './index.module.scss';

interface GuidePanelProps {
  /** 图片源 */
  imageSrc: string;
  /** 图片alt文本 */
  imageAlt?: string;
  /** 标题 */
  title: string;
  /** 描述内容 */
  description: string;
  /** 按钮文本 */
  buttonText: string;
  /** 按钮点击回调 */
  onButtonClick: () => void;
  /** 自定义按钮样式类名 */
  buttonClassName?: string;
  /** 自定义容器样式类名 */
  className?: string;
}

const GuidePanel: React.FC<GuidePanelProps> = ({
  imageSrc,
  imageAlt = '',
  title,
  description,
  buttonText,
  onButtonClick,
  buttonClassName,
  className
}) => {
  return (
    <div className={`${styles.guidePanel} ${className || ''}`}>
      <div className={styles.guideContent}>
        <div className={styles.guideIcons}>
          <img src={imageSrc} alt={imageAlt} />
        </div>
        <div className={styles.guideTitle}>{title}</div>
        <div className={styles.guideDescription}>{description}</div>
        <Button
          type="primary"
          size="large"
          onClick={onButtonClick}
          className={buttonClassName || styles.defaultButton}
        >
          {buttonText}
        </Button>
      </div>
    </div>
  );
};

export default GuidePanel;
