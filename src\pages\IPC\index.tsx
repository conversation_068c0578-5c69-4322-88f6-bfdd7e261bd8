import { getDeviceType } from "@/utils/DeviceType";
import { Spin } from "antd";
import { useEffect } from "react";
import { useHistory, useLocation } from "react-router-dom";
import styles from './index.module.scss';

const NasIPC = () => {
  const deviceType = getDeviceType();
  const history = useHistory();
  const location = useLocation();

  useEffect(() => {
    // 根据设备类型跳转到对应的IPC页面
    switch (deviceType) {
      case 0: return history.push('/cameraManagement_app');
      case 1: return history.push('/cameraManagement_pc');
    }
  }, [deviceType, history, location])

  return (
    <div className={styles.loadingContainer}>
      <Spin size="large" />
    </div>
  )
}

export default NasIPC;