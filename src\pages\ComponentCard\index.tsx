import { Route, Switch, useHistory, useRouteMatch } from "react-router-dom"
import DashboardCardNasDiskApp from "./NasDisk/APP";
import DashboardCardNasDiskPC from "./NasDisk/PC";
import { useEffect } from "react";
import { getDeviceType } from "@/utils/DeviceType";

const ComponentCard = () => {
  const history = useHistory();
  useEffect(() => {
    const deviceType = getDeviceType();
    switch (deviceType) {
      case 0: history.push('/componentCard/nasdisk_app'); break;
      case 1: history.push('/componentCard/nasdisk_pc'); break;
      default: history.push('/componentCard/nasdisk_app'); break;
    }
  }, [history])
  const { path } = useRouteMatch();

  return (
    <Switch>
      <Route exact path={`${path}/nasdisk_app`} component={DashboardCardNasDiskApp} />
      <Route exact path={`${path}/nasdisk_pc`} component={DashboardCardNasDiskPC} />
      <Route exact path={path}>
        <DashboardCardNasDiskApp />
      </Route>
    </Switch>
  )
}

export default ComponentCard;
