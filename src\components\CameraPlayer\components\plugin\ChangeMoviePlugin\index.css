.change_movie_plugin_container {
  /* position: absolute; */
  width: 100%;
  height: 28px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  /* left: 50%;
  transform: translateX(-50%); */

  span {
    font-family: MiSans W;
    font-weight: 500;
    font-size: 8px;
    line-height: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgba(231, 243, 255, 1);
  }
}

.change_movie_plugin_container_img {
  width: 10px;
  height: 10px;
}

.change_movie_plugin_disabled {
  /* 禁用交互 */
  pointer-events: none;
  cursor: not-allowed;
  touch-action: none;

  /* 视觉降级 */
  opacity: 0.65;
  filter: grayscale(50%) contrast(0.8);

  /* 动态效果禁用 */
  transition: none !important;
  animation: none !important;

  /* 覆盖浏览器默认样式 */
  background-image: none !important;
  box-shadow: none !important;
  text-shadow: none !important;
}