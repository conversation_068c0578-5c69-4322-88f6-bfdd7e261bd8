:global {
  .ant-collapse-arrow {
    width: 20px !important;
    height: 20px !important;
  }
}

.container {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.header {
  font-family: MiSans;
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0px;
  color: var(--text-color);
  padding: 7px;
  margin: 15px 20px;
}

.content {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.footer {
  width: 100%;
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
  padding: 32px 20px;
  margin: 0 auto;

  span {
    padding: 13.5px;
    border-radius: 16px;
    color: var(--text-color);
    background-color: var(--cancel-btn-background-color);
    cursor: pointer;
  }
}

.monitorPlayer {
  padding-bottom: 6px;
}
