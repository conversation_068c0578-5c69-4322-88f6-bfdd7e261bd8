.container {
  padding: 10px;
  height: 100vh;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px 14px;
  }

  .content {
    padding: 16px 0;
    .listBox {
      --border-inner: 0;
      --border-bottom: 0;
      --border-top: 0;
      --padding-left: 0;
      --padding-right: 0;
      margin-top: 32px;
    }

    .settingList {
      --border-inner: 0;
      --border-bottom: 0;
      --border-top: 0;
      --padding-left: 0;
      --padding-right: 0;
      :global {
        .adm-list-body {
          border-radius: 12px;
          background-color: var(--background-color);
        }
      }

      .listAll {
        padding: 0 16px;
        border-radius: 30px;
      }

      .settingItem {
        padding: 16px 0;
        background-color: var(--background-color);

        .valueContainer {
          display: flex;
          align-items: center;
          .arrow {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
    .listBox {
      padding: 24px 16px;
      border-radius: 12px;
      background-color: var(--background-color);
      :global {
        a.adm-list-item:active:not(.adm-list-item-disabled) {
          background-color: transparent;
        }
        .adm-list-item ,.adm-list-body{
          background-color: var(--background-color);

        }
      }
    }
    .label {
      font-size: 16px;
      color: var(--text-color);
      font-weight: 500;
    }
    .value {
      font-size: 13px;
      color: var(--list-value-text-color);
    }
  }
  .thinLine {
    position: relative;
    margin: 0 16px;
  }

  .thinLine::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background: var(--thinLine-background-color);
    transform: scaleY(0.5);
    transform-origin: 0 100%;
  }
}

// 重复模式选择弹窗样式
.repeatPopup {
  padding: 16px;
  background-color: var(--modal-content-background-color);

  .popupHeader {
    text-align: center;
    margin-bottom: 24px;

    .popupTitle {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
    }
  }

  .repeatOptions {
    margin-bottom: 24px;
  }

  .repeatOption {
    padding: 16px 0;
    font-size: 16px;
    color: var(--text-color);
    font-weight: 500;
    background-color: var(--modal-content-background-color);
    margin-top: 10px;
    border-radius: 16px;

    &:last-child {
      border-bottom: none;
    }
    &[data-selected="true"] {
      background-color: var(--primary-list-color);
      .optionText {
        color: var(--primary-color);
      }
    }
  }

  .optionContent {
    display: flex;
    align-items: center;
    padding: 0 16px;
  }

  .iconContainer {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    position: relative;
  }

  .checkedIcon {
    width: 20px;
    height: 20px;
    position: absolute;
    left: 0;
    top: 0;
  }

  .optionText {
    flex: 1;
  }

  .popupFooter {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    padding: 0 10px;
  }

  .popupButton {
    flex: 1;
    text-align: center;
    padding: 12px 24px;
    font-size: 16px;
    background-color: var(--cancel-btn-background-color);
    color: var(--title-color);
    border-radius: 16px;

    &.primary {
      background-color: var(--primary-color);
      font-weight: 500;
      color: #fff;
    }
  }
}

:global {
  .adm-picker-header-button {
    font-size: 16px;
    color: var(--primary-color);
  }

  .adm-picker-view-column-item {
    font-size: 20px;
  }

  .adm-picker-view-column-item-label {
    font-size: 20px;
  }
}
