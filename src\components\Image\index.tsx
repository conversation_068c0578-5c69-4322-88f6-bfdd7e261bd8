import { ComponentType, FC, useEffect, useMemo, useRef, useState } from "react";
import { getWebDavInfo } from "@/utils/DeviceType";
import errorFile from "@/Resources/camMgmtImg/null-page.png";

interface BaseImageProps {
  src: string;
  alt?: string;
  placeholder?: React.ReactNode;
  onClick?: (v?: any) => void
  style?: React.CSSProperties;
  className?: string;
  needHeader?: boolean
}

interface PreloadStatus {
  isLoading: boolean;
  hasError: boolean;
}

type PreloadImageProps = BaseImageProps & PreloadStatus;

const withImagePreloader = <P extends BaseImageProps>(
  WrappedComponent: ComponentType<P>
): FC<Omit<P, keyof PreloadStatus> & BaseImageProps> => {
  return (props: Omit<P, keyof PreloadStatus> & BaseImageProps) => {
    const [loadStatus, setLoadStatus] = useState({
      isLoading: true,
      hasError: false,
    });

    const [displayUrl, setDisplayUrl] = useState<string | null>(null);
    const timeKey = useRef<NodeJS.Timeout | null>(null);
    const activeBlobUrl = useRef<string | null>(null); // 用于跟踪当前的 Blob URL

    const webDAVParams = getWebDavInfo();
    const headersParams = useMemo(() => {
      return {
        'Depth': '1',
        'Authorization': `Basic ${btoa(`${webDAVParams.username}:${webDAVParams.password}`)}`,
        'Content-Type': 'application/xml',
      }
    }, [webDAVParams.password, webDAVParams.username])

    useEffect(() => {
      let isActive = true;

      // 取消任何进行中的请求或超时
      if (timeKey.current) clearTimeout(timeKey.current);

      const loadImage = async () => {
        const imageUrl = props.src;

        // 清理之前的 Blob URL
        if (activeBlobUrl.current) {
          URL.revokeObjectURL(activeBlobUrl.current);
          activeBlobUrl.current = null;
        }

        try {
          // 仅当需要自定义请求头时才使用 fetch
          if (props.needHeader) {
            const response = await fetch(imageUrl, {
              headers: headersParams,
              mode: 'cors'
            });

            if (!response.ok) {
              throw new Error(`HTTP ${response.status} - Image load failed`);
            }

            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob); // 创建blob url
            activeBlobUrl.current = blobUrl;

            const img = new Image();
            img.src = blobUrl;
            img.onload = () => {
              if (img.naturalWidth === 0 || img.naturalHeight === 0) {
                throw new Error('Image loaded but dimensions are zero');
              }

              if (isActive) {
                setDisplayUrl(blobUrl);
                setLoadStatus({ isLoading: false, hasError: false });
              }
            };

            img.onerror = () => {
              if (isActive) {
                setLoadStatus(prev => ({
                  ...prev,
                  hasError: true,
                  isLoading: false
                }));
              }
            }
          } else {
            const img = new Image();
            img.src = imageUrl;

            // 不需要请求头时直接使用原始 URL
            img.onload = () => {
              if (img.naturalWidth === 0 || img.naturalHeight === 0) {
                throw new Error('Image loaded but dimensions are zero');
              }
              if (isActive) {
                setDisplayUrl(imageUrl);
                setLoadStatus({ isLoading: false, hasError: false });
              }
            };
            img.onerror = () => {
              if (isActive) {
                setLoadStatus(prev => ({
                  ...prev,
                  hasError: true,
                  isLoading: false
                }));
              }
            };

          }
        } catch (error) {
          if (isActive) {
            setLoadStatus(prev => ({
              ...prev,
              hasError: true,
              isLoading: false
            }));
          }
        }
      };

      // 初始化加载
      setLoadStatus({ isLoading: true, hasError: false });
      loadImage();

      return () => {
        isActive = false;
        // 保留 Blob URL 用于显示
      };
    }, [props.src, props.needHeader, headersParams]);

    // 清理函数
    useEffect(() => {
      return () => {
        // 组件卸载时清理 Blob URL
        if (activeBlobUrl.current) {
          URL.revokeObjectURL(activeBlobUrl.current);
        }
      };
    }, []);

    // 传递增强后的props
    return <WrappedComponent
      {...(props as P)}
      {...loadStatus}
      src={displayUrl || props.src}
    />
  };
};

// Error 组件
const ErrorImage: FC<PreloadImageProps> = ({ className, style, onClick }) => (
  <img src={errorFile} alt="error" onClick={onClick} className={className}
    style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      ...style
    }}
  />
)

const ImageComponent = (props: PreloadImageProps) => {
  const { src, alt, isLoading, hasError, placeholder, onClick, style, className } = props;
  if (hasError) return <ErrorImage {...props} />
  return isLoading ? <div>{placeholder}</div> : <img className={className} onClick={onClick} src={src} alt={alt} style={{ ...style, opacity: isLoading ? 0 : 1 }} />
}

export const PreloadImage = withImagePreloader(ImageComponent);