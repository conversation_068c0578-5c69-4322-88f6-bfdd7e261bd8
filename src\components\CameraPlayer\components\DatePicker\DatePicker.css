.date-picker {
  width: 100%;
  height: 100%;
  user-select: none;
  overflow: hidden;
  z-index: 10001;
}

.header {
  font-size: 18px;
  color: var(--text-color);
  font-weight: 500;
  text-align: center;
  height: 70px;
}

.header_date_select {
  display: flex;
  align-items: center;
  justify-content: center;
}

.header_date_select_left {
  width: 42px;
  height: 42px;
}

.header_date_select_center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 22px;
    height: 22px;
    margin: 16px 30px;
  }
}

.header_date_select_right {
  width: 42px;
  height: 42px;
  color: var(--primary-color);
  background-color: rgba(52, 130, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 24px;
  cursor: pointer;
}


.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 10px;
  color: var(--list-value-text-color);
}

.weekday {
  text-align: center;
}

.dates-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  transition: transform 0.01s ease-out;
  will-change: transform;
}

.date-cell {
  position: relative;
  width: 42px;
  height: 42px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 21px;
  transition: all 0.2s;
  color: var(--text-color);

  /* &:hover,
  &:active,
  &:focus {
    background: var(--primary-color);
    color: #FFF !important;
    opacity: 1;
  } */
}

.date-number {
  font-size: 18px;
  font-family: "MI Lan Pro";
}

.date-number.notVideo {
  opacity: 0.4;
}

.other-month {
  color: #ccc;
}

.selected .date-number {
  color: white;
  background: var(--primary-color);
  width: 42px;
  height: 42px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer {
  margin-top: 15px;
  text-align: center;
}

.cancel-btn {
  width: 300px;
  height: 45px;
  padding: 6px 20px;
  background: #f5f5f5;
  border: none;
  border-radius: 45px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background: #eee;
}

.date-cell.empty {
  visibility: hidden;
  pointer-events: none;
}