import React, { useState, useEffect } from "react";
import { Image, Switch, Checkbox } from "antd-mobile";
import styles from "./index.module.scss";
import listNull from "@/Resources/camMgmtImg/list-null.png";
import listNullDark from "@/Resources/camMgmtImg/list-null-dark.png";
import add from "@/Resources/camMgmtImg/add.png";
import deleteIcon from "@/Resources/camMgmtImg/delete.png";
import deleteDark from "@/Resources/camMgmtImg/deletes-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { ICollapsePanel } from "@/layouts/Layout";
import { ICameraDetail } from "@/pages/IPC/IPC_APP/CameraDetail";
import { IDeviceDetail } from "../../..";
import { TimeSlot } from "..";
import ShutDown from "../ShutDown";
import { px2rem } from "@/utils/setRootFontSize";
import Modal from "@/components/Modal";

// 中英文星期映射
const dayMapping: Record<string, string> = {
  Mon: "周一",
  Tue: "周二",
  Wed: "周三",
  Thu: "周四",
  Fri: "周五",
  Sat: "周六",
  Sun: "周日",
};

const RecordCustomize = (props: {
  camera: (ICollapsePanel & ICameraDetail & IDeviceDetail),
  isEditing: boolean, setIsEditing: (v: any) => void,
  slots: TimeSlot[],
  setSlots: (v: any) => void,
  originalConfig: any,
  setOriginalConfig: (v: any) => void,
  callback: (value: any, key: any, paramName: any) => void
}) => {
  const { isDarkMode } = useTheme();
  const { camera, isEditing, setIsEditing, slots, setSlots, callback } = props;

  useEffect(() => {
    console.log(camera.data.recordConfig.recordPlan);
    if (
      camera.data.recordConfig.recordPlan &&
      camera.data.recordConfig.recordPlan.schedule &&
      camera.data.recordConfig.recordPlan.schedule.length > 0
    ) {
      const mappedSlots = camera.data.recordConfig.recordPlan.schedule.map(
        (item: any, index: number) => ({
          id: index + 1,
          start: item.start,
          end: item.end,
          repeat_policy: item.repeat_policy,
          customized: item.customized || [],
          enabled: item.enabled !== undefined ? item.enabled : true,
          selected: false,
        })
      );

      setSlots(mappedSlots);
    }
  }, [camera, setSlots])

  // 开关时间段
  const handleSwitchChange = (id: number, checked: boolean) => {
    // 更新本地状态
    const updatedSlots = slots.map((slot) =>
      slot.id === id ? { ...slot, enabled: checked } : slot
    );
    setSlots(updatedSlots);

    if (!camera?.did) {
      return;
    }

    // 准备录制计划
    const scheduleItems = updatedSlots.map((slot) => ({
      start: slot.start,
      end: slot.end,
      repeat_policy: slot.repeat_policy,
      customized: slot.customized,
      enabled: slot.enabled,
    }));

    callback({
      type: "customized",
      schedule: scheduleItems,
    }, 'recordPlan', 'record_schedule')
  };

  const toggleItemSelection = (id: number) => {
    setSlots(slots.map(slot =>
      slot.id === id ? { ...slot, selected: !slot.selected } : slot
    ));
  };

  const deleteSelectedSlots = () => {
    const hasSelectedItems = slots.some(slot => slot.selected);

    if (!hasSelectedItems) {
      return;
    }

    const updatedSlots = slots.filter((slot) => !slot.selected);
    setSlots(updatedSlots);

    // 准备录制计划
    const scheduleItems = updatedSlots.map((slot) => ({
      start: slot.start,
      end: slot.end,
      repeat_policy: slot.repeat_policy,
      customized: slot.customized,
      enabled: slot.enabled,
    }));

    callback({
      type: "customized",
      schedule: scheduleItems,
    }, 'recordPlan', 'record_schedule')

    // 当删除最后一个时间段时，退出编辑模式
    if (updatedSlots.length === 0) {
      setIsEditing(false);
    }
  };

  // 转换英文日期为中文显示
  const formatDays = (days: string[]) => {
    return days.map((day) => dayMapping[day] || day);
  };

  // 是否处于加载状态
  // const isLoading = configLoading || saveLoading;

  const [shutDownModalVisible, setShutDownModalVisible] = useState(false);

  // const hasSelectedItems = slots.some(slot => slot.selected);

  return (
    <div className={styles.container}>
      {/* <div className={styles.title}>{isEditing ? "编辑" : "自定义"}</div> */}

      {slots.length > 0 ? (
        <div className={styles.timeSlotList}>
          {slots.map((slot) => (
            <div
              key={slot.id}
              className={styles.timeSlotItem}
              onClick={() => isEditing && toggleItemSelection(slot.id)}
            >
              <div className={styles.timeSlotContent}>
                <div>
                  <div
                    className={styles.timeRange}
                  >{`${slot.start}-${slot.end}`}</div>
                  <div className={styles.daysRow}>
                    <span className={styles.shutdownTag}>关机</span>
                    {slot.repeat_policy === "once" && (
                      <span className={styles.dayTag}>仅一次</span>
                    )}
                    {slot.repeat_policy === "everyday" && (
                      <span className={styles.dayTag}>每天</span>
                    )}
                    {slot.repeat_policy === "workday" && (
                      <span className={styles.dayTag}>工作日</span>
                    )}
                    {slot.repeat_policy === "holiday" && (
                      <span className={styles.dayTag}>节假日</span>
                    )}
                    {slot.repeat_policy === "customized" &&
                      formatDays(slot.customized).map((day, dayIndex) => (
                        <span key={dayIndex} className={styles.dayTag}>
                          {day}
                        </span>
                      ))}
                  </div>
                </div>
                {isEditing ? (
                  <Checkbox
                    checked={slot.selected}
                    className={styles.checkbox}
                  />
                ) : (
                  <Switch
                    checked={slot.enabled}
                    onChange={(checked) => handleSwitchChange(slot.id, checked)}
                    className={styles.switch}
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className={styles.emptyState}>
          <Image
            src={isDarkMode ? listNullDark : listNull}
            className={styles.emptyIcon}
          />
          <div className={styles.emptyText}>列表为空</div>
        </div>
      )}

      <div className={styles.addButtonWrapper}>
        {(!isEditing || slots.length === 0) && (
          <Image
            className={styles.addButton}
            src={add}
            onClick={() => setShutDownModalVisible(true)}
          />
        )}
      </div>

      {isEditing && (
        <div className={styles.deleteContainer}>
          <div className={styles.deleteButton} onClick={deleteSelectedSlots}>
            <Image
              src={isDarkMode ? deleteDark : deleteIcon}
              className={styles.deleteIcon}
            />
            <div className={styles.deleteText}>删除</div>
          </div>
        </div>
      )}

      <Modal isShow={shutDownModalVisible} onCancel={() => setShutDownModalVisible(false)} content={(
        <ShutDown
          onClose={() => setShutDownModalVisible(false)}
          onFinish={() => {
            setShutDownModalVisible(false);
          }}
          camera={camera}
          callback={callback}
        />
      )} footer={null} contentStyle={{ width: px2rem('500px'), height: px2rem('500px') }} />
    </div>
  );
};

export default RecordCustomize;