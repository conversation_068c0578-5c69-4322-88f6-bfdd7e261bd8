import { createContext, useContext } from 'react';

interface StorageContextType {
  threshold: number;
  setThreshold: React.Dispatch<React.SetStateAction<number>>;
  detectEvents: {
    motionDetect: boolean;
    humanDetect: boolean;
    fireDetect: boolean;
    petDetect: boolean;
    soundDetect: boolean;
  };
  setDetectEvents: React.Dispatch<
    React.SetStateAction<{
      motionDetect: boolean;
      humanDetect: boolean;
      fireDetect: boolean;
      petDetect: boolean;
      soundDetect: boolean;
    }>
  >;
}

export const StorageContext = createContext<StorageContextType>({
  threshold: 95,
  setThreshold: () => {},
  detectEvents: {
    motionDetect: true,
    humanDetect: true,
    fireDetect: true,
    petDetect: true,
    soundDetect: true,
  },
  setDetectEvents: () => {},
});

export const useStorage = () => useContext(StorageContext);