.container {
  display: flex;
  flex-direction: column;
}

.header {
  padding: 0 10px;
  .backIcon {
    width: 40px;
    height: 40px;
  }
}
.title {
  font-size: 32px;
  font-weight: 400;
  color: var(--title-color);
  padding: 10px 16px;
}

.description {
  font-size: 14px;
  color: var(--list-value-text-color);
  line-height: 1.4;
  padding: 24px 16px 20px 16px;
}

.content {
  flex: 1;

  :global {
    .adm-list-body {
      border-radius: 10px;
      background-color: var(--background-color);
      overflow: hidden;
    }
    .adm-list-item {
      background-color: var(--background-color);
    }
    .adm-list-item-content-main {
      color: var(--text-color);
    }
  }
}

.list {
  --border-inner: 1px solid #f4f4f4;
  --border-top: 0;
  --border-bottom: 0;
  :global{
    a.adm-list-item:active:not(.adm-list-item-disabled){
      background-color: var(--background-color);
    }
  }
}

.switchItem {
  padding-left: 16px;
}

.item {
  padding-left: 16px;
  :global {
    .adm-list-item-content {
      border-top: none;
    }
  }
}

.extraText {
  font-size: 14px;
  color: var(--list-value-text-color);
}
