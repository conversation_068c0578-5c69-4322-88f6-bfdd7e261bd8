import React, { useState } from "react";
import { Modal, Input, Button, message } from "antd";
import { CloseCircleFilled, CloseOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import { createBaiduNetdiskFolder } from "@/api/nasDisk";

interface CreateBdFolderProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  currentPath: string;
}

const CreateBdFolder: React.FC<CreateBdFolderProps> = ({
  visible,
  onCancel,
  onSuccess,
  currentPath,
}) => {
  const [folderName, setFolderName] = useState<string>("");
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const [isComposing, setIsComposing] = useState<boolean>(false);
  const [compositionValue, setCompositionValue] = useState<string>("");

  // 处理中文输入法开始
  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  // 处理中文输入法更新
  const handleCompositionUpdate = (e: React.CompositionEvent<HTMLInputElement>) => {
    // 在组合期间更新临时值用于显示
    setCompositionValue(e.currentTarget.value);
  };

  // 处理中文输入法结束
  const handleCompositionEnd = (e: React.CompositionEvent<HTMLInputElement>) => {
    setIsComposing(false);
    setCompositionValue("");
    // 组合结束时，更新最终值
    setFolderName(e.currentTarget.value);
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    if (isComposing) {
      // 在组合状态下，更新临时显示值
      setCompositionValue(value);
    } else {
      // 非组合状态下，直接更新最终值（处理英文、数字等）
      setFolderName(value);
    }
  };

  // 清除输入
  const handleClearInput = () => {
    setFolderName("");
    setCompositionValue("");
    setIsComposing(false);
  };

  // 处理按键事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && folderName.trim() && !isComposing) {
      handleConfirm();
    }
  };

  // 创建文件夹
  const handleConfirm = async () => {
    if (!folderName.trim()) {
      message.error("请输入文件夹名称");
      return;
    }

    // 检查文件夹名称是否包含特殊字符
    const invalidChars = /[\\/:*?"<>|]/;
    if (invalidChars.test(folderName)) {
      message.error('文件夹名称不能包含以下字符: \\ / : * ? " < > |');
      return;
    }

    setIsCreating(true);

    try {
      // 构建完整路径
      let folderPath = currentPath;
      if (folderPath === '/') {
        folderPath = `/${folderName.trim()}`;
      } else {
        folderPath = `${currentPath}/${folderName.trim()}`;
      }
      
      console.log("准备创建百度网盘文件夹:", folderPath);

      // 调用百度网盘创建文件夹接口
      const response = await createBaiduNetdiskFolder({
        action: "createdir",
        path: folderPath
      });

      console.log("百度网盘创建文件夹响应:", response);

      if (response.code === 0) {
        message.success("文件夹创建成功");
        setFolderName("");
        onSuccess();
      } else {
        // 根据errno显示不同的错误信息
        const errorMessages: Record<number, string> = {
          2: "参数错误",
          9: "文件或目录不存在",
          111: "有其他异步任务正在执行",
          112: "页面已过期，请刷新后重试",
          117: "网络连接问题，请稍后重试",
          121: "文件夹已存在",
          130: "文件夹数量超出限制",
          9003: "用户IP非法",
        };
        
        const errorMessage = errorMessages[response.errno] || response.errmsg || "创建文件夹失败";
        message.error(errorMessage);
      }
    } catch (error) {
      console.error("创建百度网盘文件夹失败:", error);
      message.error("创建文件夹失败，请检查网络连接");
    } finally {
      setIsCreating(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setFolderName("");
    onCancel();
  };

  return (
    <Modal
      title="新建文件夹"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={400}
      centered
      maskClosable={!isCreating}
      closeIcon={<CloseOutlined style={{ color: "var(--title-color)" }} />}
      className={styles.createFolderModal}
    >
      <div className={styles.inputWrapper}>
        <Input
          value={isComposing ? compositionValue : folderName}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onCompositionStart={handleCompositionStart}
          onCompositionUpdate={handleCompositionUpdate}
          onCompositionEnd={handleCompositionEnd}
          placeholder="请输入文件夹名称"
          autoFocus
          disabled={isCreating}
          maxLength={255}
          suffix={
            folderName ? (
              <CloseCircleFilled
                className={styles.clearIcon}
                onClick={handleClearInput}
              />
            ) : null
          }
        />
      </div>
      <div className={styles.buttonGroup}>
        <Button
          className={styles.cancelButton}
          onClick={handleCancel}
          disabled={isCreating}
        >
          取消
        </Button>
        <Button
          type="primary"
          className={styles.confirmButton}
          onClick={handleConfirm}
          loading={isCreating}
          disabled={!folderName.trim() || isCreating}
        >
          确定
        </Button>
      </div>
    </Modal>
  );
};

export default CreateBdFolder; 