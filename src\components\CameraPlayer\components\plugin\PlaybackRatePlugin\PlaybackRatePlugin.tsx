import { useCallback, useEffect, useMemo, useState } from 'react';
import rateIcon2x from '@/Resources/player/speed2x.png';
import rateIcon1x from '@/Resources/player/speed1x.png';
import rateIcon4x from '@/Resources/player/speed4x.png';
import "./PlaybackRatePlugin.css";
import Player, { Events } from 'xgplayer/es/player';
import { PreloadImage } from '@/components/Image';
import { IMonitorPlayerOptions } from '../../MonitorPlayer/MonitorPlayer';
import { getSystemType } from '@/utils/DeviceType';

interface IPlaybackRatePlugin {
  rateList: {
    label: string,
    rate: number
  }[],
  initRate?: number
  deviceType: 'pc' | 'mobile'
  player: Player | null
  isFull: boolean // 是否全屏播放
  isDashboard?: boolean
  options?: IMonitorPlayerOptions
}

const PlaybackRatePlugin = (props: IPlaybackRatePlugin) => {
  const { initRate, rateList, deviceType, player, isFull, isDashboard, options } = props;
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [rate, setRate] = useState<number>(initRate ? initRate : 1); //默认播放速度 1x
  const isAndroid = getSystemType() === 'android';
  // const isIos = getSystemType() === 'ios';

  const rateIcon: string = useMemo(() => {
    switch (rate) {
      case 1: return rateIcon1x;
      case 2: return rateIcon2x;
      case 4: return rateIcon4x;
      default: return rateIcon1x;
    }
  }, [rate])

  const selectRate = useCallback((rateNumber: number) => {
    setRate(rateNumber);
    if (player) {
      player.playbackRate = rateNumber;
    }
    setIsHovered(false);
  }, [player])

  useEffect(() => {
    if (player) {
      player.on(Events.RATE_CHANGE, () => {
        setRate(player.playbackRate);
      })
    }
  }, [player])

  return (
    <div className="rate-container" onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      <PreloadImage className={`iconBtn_container_img ${isFull ? 'full' : 'notFull'} ${options?.type}  ${isAndroid ? 'android' : ''} ${deviceType} ${isDashboard && !isFull ? 'dashboard' : ''}`} src={rateIcon} alt="rate" />

      {isHovered && (
        <div className={`rate-select-container ${deviceType === 'mobile' ? 'mobile' : ''}`}>
          {
            rateList.map((it) => {
              return <span key={it.rate} className='rate-select-span' onClick={() => selectRate(it.rate)}>{it.label}</span>
            })
          }
        </div>
      )}
    </div>
  );
};

export default PlaybackRatePlugin;