import { Button } from "antd-mobile";
import "./index.scss";
import { CloseOutline, HistogramOutline, PlayOutline, SendOutline, UnorderedListOutline } from 'antd-mobile-icons'
const TaskCard = () => {

const action = () => {
    console.log("立即更新");
}
    return (
        <>
            <div className="task-card">
                <div className="top">
                    <div className="top-left">任务1</div>
                    <div className="top-right">
                    <SendOutline />
                    <PlayOutline />     
                    <CloseOutline />
                    <UnorderedListOutline />
                    </div>
                </div>
                <div className="middle">
                    <div className="middle-left">
                        <div><span>百度网盘</span><span>百度网盘</span></div>
                        <div className="middle-left-icon"><HistogramOutline /></div>
                        <div><span>暂时缓存</span><span>存储文件备份</span></div>
                    </div>
                    <div className="middle-right">
                        <Button onClick={action}>立即更新</Button>
                    </div>
                </div>
                <div className="bottom">
                    <span>最近一次同步：2024/12/31 21：34</span>
                </div>
            </div>
        </>
    );
};

export default TaskCard;