.task_manager_container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

.nav_bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 60px;
  background-color: var(--modal-content-background-color);
  border-bottom: 1px solid var(--border-color);
}

.nav_items {
  display: flex;
  gap: 24px;
}

.nav_item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: MiSans W;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  opacity: 0.7;
}

.nav_item:hover {
  background-color: var(--hover-background-color);
  opacity: 1;
}

.nav_item.active {
  background-color: var(--primary-color);
  color: #fff;
  opacity: 1;
}

.content_area {
  flex: 1;
  overflow: hidden;
}

.loading_container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: MiSans W;
  font-size: 16px;
  color: var(--text-color);
  opacity: 0.6;
}