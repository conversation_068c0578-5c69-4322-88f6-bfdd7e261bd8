import React, { useEffect } from "react";
import { Route, useRouteMatch } from "react-router-dom";
import { UserProvider, useUser } from "@/utils/UserContext";
import Home from "./Home";
import styles from "./index.module.scss";

const NasDiskAppContent: React.FC = (props: { children?: React.ReactNode }) => {
  const { path } = useRouteMatch();
  const {  fetchUserInfo } = useUser();

  // 获取用户信息
  useEffect(() => {
    fetchUserInfo();
  }, [fetchUserInfo]);

  return (
    <div className={styles.nasDiskContainer}>
      {props.children}
      <Route exact path={path} component={Home} />
    </div>
  );
};

const NasDisk_APP: React.FC = (props: { children?: React.ReactNode }) => {
  return (
    <UserProvider>
      <NasDiskAppContent {...props} />
    </UserProvider>
  );
};

export default NasDisk_APP;
