.statusDialog {
  background-color: var(--desktop-modal-bg-color);
  border-radius: 32px;
  :global {
    .ant-modal-content {
      border-radius: 32px;
      overflow: hidden;
      background-color: var(--desktop-modal-bg-color);
    }

    .ant-modal-header {
      margin-bottom: 0;
      background-color: var(--desktop-modal-bg-color);
    }

    .ant-modal-body {
      padding: 0;
      min-height: 300px;
      display: flex;
      flex-direction: column;
    }

    .ant-modal-footer {
      padding: 16px 64px;
      border-top: none;
      text-align: center;
      margin-top: auto;
    }
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .closeButton {
    width: 40px;
    height: 40px;
    cursor: pointer;
  }

  .modalTitle {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    flex: 1;
    color: var(--title-color);
  }

  .headerIconContainer {
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.statusContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  width: 100%;
  text-align: center;
}

.statusContainerLoading {
  height: 526px;
}

.statusContainerOther {
  height: 450px;
}

.iconContainer {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  &.loadingIcon {
    animation: spin 1.5s linear infinite;
  }

  &.circleIcon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(64, 134, 255, 0.1);
  }

  &.errorIcon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 77, 79, 0.1);
  }
}

.searchingText {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #1890ff;
}

.blueIndicator {
  border-radius: 50%;
  width: 22px;
  height: 22px;
  margin-right: 8px;
}

.statusIcon {
  max-width: 120px;
  max-height: 80px;
  object-fit: contain;
}

.statusTitle {
  font-size: 16px;
  color: var(--text-color);
  font-weight: 500;
}

.statusSubTitle {
  font-size: 14px;
  color: var(--list-value-text-color);
  margin-top: 8px;
}

.actionButton {
  width: 100%;
  height: 44px;
  font-size: 16px;
  border-radius: 22px;
  background-color: var(--primary-color);
  border: none;
  font-weight: normal;

  &:hover,
  &:focus {
    background-color: var(--primary-color);
  }

  &:disabled {
    background-color: #91bbff;
    color: #fff;
    cursor: not-allowed;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
