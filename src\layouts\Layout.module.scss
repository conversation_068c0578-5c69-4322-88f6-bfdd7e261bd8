.layout_container {
  display: flex;
  height: 100%;
  :global {
    // 确保div#root能够作为定位容器
    div#root {
      position: relative !important;
      height: 100% !important;
    }

    .ant-layout {
      background-color: var(--background-color);
      width: 100% !important;
      // width: 1234px;
    }
    .ant-layout-sider {
      background-color: var(--side-bar-background-color);
      min-width: 0 !important;
      transition: all 0.1s;
    }
    .ant-divider-horizontal {
      min-width: 50%;
      width: 80%;
      margin: 24px auto;
    }
    .ant-collapse-borderless {
      background-color: transparent !important;
      img {
        height: 26px;
      }
    }
    .ant-collapse-header {
      align-items: center !important;
      padding: 8px 20px !important;
    }
    .ant-collapse-header-text {
      font-family: MiSans;
      font-weight: 400;
      font-size: 12px;
      line-height: 100%;
      letter-spacing: 0px;
      color: var(--subtitle-text-color);
    }
  }
}

.layout_sider_header {
  display: flex;
  align-items: center;
  padding: 7px;
  margin: 15px 20px;

  img {
    height: 26px;
    cursor: pointer;
  }
}

.layout_content_container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 0;
  .header {
    height: 68px;
    background-color: var(--background-color);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: MiSans;
    font-weight: 500;
    font-size: 20px;
    letter-spacing: 0px;
    color: var(--text-color);
    padding: 22px 27px 22px 27px;
    transition: visibility 0.5s opacity 0.5s;

    img {
      height: 26px;
      cursor: pointer;
      opacity: 0;
      visibility: hidden;
    }
  }
}

.layout_content {
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
  margin: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.retract {
  aside {
    opacity: 0;
    width: 0 !important;
    max-width: 0 !important;
  }
}

.retractHeader {
  img {
    margin-right: 40px;
    opacity: 1 !important;
    visibility: visible !important;
  }
}

// 当在div#root内时的全屏样式
.fullscreenInContainer {
  position: absolute !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  // z-index: 9999;
  background-color: var(--background-color);
}

.fullscreenContent {
  margin-left: 0 !important;
  height: 100% !important;

  .header {
    display: none !important;
  }
}

.fullscreenLayoutContent {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  overflow: hidden !important;
}

.layout_sider_menus {
  width: 100%;
  padding: 0 14px;

  .layout_sider_menus_item {
    display: flex;
    align-items: center;
    font-family: MiSans;
    font-weight: 500;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0px;
    vertical-align: middle;
    padding: 11px 6px;
    border-radius: 10px;
    margin: 10px 0;
    color: var(--text-color);

    &:active,
    &:hover,
    &:focus {
      cursor: pointer;
      background-color: var(--cancel-btn-background-color);
    }

    img {
      height: 26px;
      margin-right: 12px;
    }
  }
}

.focus {
  background-color: var(--cancel-btn-background-color);
}

.collapse_content {
  width: 100%;
  height: 100%;

  .collapse_content_item {
    display: flex;
    align-items: center;
    padding: 11px 6px;
    cursor: pointer;
    border-radius: 10px;

    &:active,
    &:hover,
    &:focus {
      cursor: pointer;
      background-color: var(--cancel-btn-background-color);
    }

    img {
      margin-right: 12px;
      width: 26px !important;
      height: 26px !important;
    }

    span {
      width: 170px;
      font-family: MiSans;
      font-weight: 500;
      font-size: 15.65px;
      line-height: 100%;
      letter-spacing: 0%;
      vertical-align: middle;
      white-space: nowrap; /* 禁止文本换行 */
      overflow: hidden; /* 隐藏溢出内容 */
      text-overflow: ellipsis; /* 显示省略符号 */
      color: var(--text-color);
    }
  }
}

.modal_left {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.selectIcon {
  height: 40px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 30px;
    height: 30px;
  }
}

.modal_right {
  img {
    cursor: pointer;
  }
}

.not_icon_third_menu {
  margin-left: 42px;
}

.refresh_btn {
  visibility: visible !important;
}

.layout_sider_container {
  height: 100%;
  position: relative;
  :global {
    .ant-layout-sider-children {
      overflow-y: auto;
      scrollbar-width: none;
      padding-bottom: 80px; // 为底部用户信息预留空间
    }
  }
}

.fat_side_bar_container {
  width: 100%;

  .fat_side_bar_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 20px;

    span {
      font-family: MiSans;
      font-weight: 400;
      font-size: 12px;
      line-height: 100%;
      letter-spacing: 0px;
      color: var(--subtitle-text-color);
    }

    img {
      height: 26px;
      cursor: pointer;
    }
  }

  .fat_side_bar_content {
    width: 100%;
    padding: 16px;
    padding-top: 4px;
    overflow-y: auto;

    .fat_side_bar_content_item {
      display: flex;
      align-items: center;
      display: flex;
      align-items: center;
      padding: 11px 6px;
      cursor: pointer;
      border-radius: 10px;

      &:active,
      &:hover,
      &:focus {
        cursor: pointer;
        background-color: var(--cancel-btn-background-color);
      }

      img {
        margin-right: 12px;
      }

      span {
        width: 170px;
        font-family: MiSans;
        font-weight: 500;
        font-size: 15.65px;
        line-height: 100%;
        letter-spacing: 0%;
        vertical-align: middle;
        white-space: nowrap; /* 禁止文本换行 */
        overflow: hidden; /* 隐藏溢出内容 */
        text-overflow: ellipsis; /* 显示省略符号 */
        color: var(--text-color);
      }
    }

    img {
      height: 26px;
    }
  }
}

// 网盘侧边栏样式
.bdSidebarBottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background-color: var(--side-bar-background-color);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

// 用户信息容器样式
.userInfoContainer {
  display: flex;
  align-items: center;
  padding: 8px 6px;
  cursor: pointer;
  border-radius: 10px;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--cancel-btn-background-color);
  }
}

// 用户头像样式
.userAvatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 用户详情样式
.userDetails {
  flex: 1;
  min-width: 0;

  .userName {
    font-family: MiSans;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.2;
    color: var(--text-color);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .memberStatus {
    display: flex;
    align-items: center;
    font-family: MiSans;
    font-weight: 400;
    font-size: 12px;
    line-height: 1.2;
    color: #bf751f;
    flex-wrap: nowrap;
    gap: 4px;

    .vipBadge {
      margin-left: 6px;
      padding: 2px 6px;
      font-size: 10px;
      font-weight: 500;
      color: #e2ae1e;
      background-color: #402c00;
      border-radius: 4px;
      line-height: 1;
      white-space: nowrap;
    }

    .memberBadge {
      margin-left: 4px;
      display: flex;
      align-items: center;

      img {
        width: 12px;
        height: 12px;
        opacity: 0.6;
      }
    }
  }
}

.menu_label_container {
  display: flex;
  align-items: center;
  justify-content: start;
  flex: 1;
}

.menu_label_container_label {
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
}
