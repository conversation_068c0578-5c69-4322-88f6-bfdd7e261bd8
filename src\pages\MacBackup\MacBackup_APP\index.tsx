import { FC, useCallback, useMemo, useRef, useState } from 'react';
import styles from './index.module.scss';
import NavigatorBar from '@/components/NavBar';
import { PreloadImage } from '@/components/Image';
import finish from '@/Resources/camMgmtImg/finish.png';
import finish_dark from '@/Resources/camMgmtImg/finish-dark.png';
import { useTheme } from '@/utils/themeDetector';

import mac_show_img from '@/Resources/macBackup/mac_show_pic.png';
import nas_show_img from '@/Resources/macBackup/nas_show_pic.png';
import next from "@/Resources/modal/next.png";
import next_dark from "@/Resources/modal/next_dark.png";

import List, { IListData, modalShow } from '@/components/List';
import { Form, Switch } from 'antd';
import { Toast } from '@/components/Toast/manager';
import FloatPanel from '@/components/FloatPanel';
import FileSelectorUtil, { IFileDirBaseProps } from '@/components/FileSelector';
import { useRequest, useUpdateEffect } from 'ahooks';
import { FileItem, getPoolInfo, listDirectory, StoragePool } from '@/api/fatWall';
import { WebDavInfo } from '@/utils/DeviceType';
import NumberInput from './numberInput';

export interface IMacBackupConfig {
  isBackup: boolean;
  path: string;
  sizeLimit: number;
  unit: string;
}

const MacBackup_App_Index_Page: FC = () => {

  const { isDarkMode } = useTheme();
  const [form] = Form.useForm();

  // mac备份配置
  const [macBackupConfig, setMacBackupConfig] = useState<IMacBackupConfig>({
    isBackup: false,
    path: '',
    sizeLimit: 0,
    unit: 'GB'
  });

  // 存储路径是否展开面板的开关状态
  const [storagePathOpenPlane, setStoragePathOpenPlane] = useState<boolean>(false);

  // 原始存储池信息
  const [poolData, setPoolData] = useState<StoragePool[]>([]);

  // 当前文件夹信息
  const [currentFileDirList, setCurrentFileDirList] = useState<FileItem[]>([]);

  // 当前所在的路径配置
  const [currentFileDirConfig, setCurrentFileDirConfig] = useState<{ dir_name: string; data_dir: string }>({ dir_name: '', data_dir: '' });

  // 查询文件夹的分页配置
  const page_config_ref = useRef<{ size: number, token: string }>({ size: 20, token: "" });

  // 当前选择的文件夹路径
  const [selectedValue, setSelectedValue] = useState<string>('');

  // webDav配置
  const [currentWebDav, setCurrentWebDav] = useState<WebDavInfo>(
    {
      alias_root: "/home/<USER>",
      password: "password",
      port: 5000,
      uri: "/",
      username: "user0"
    }
  );

  // 文件夹列表信息配置
  const file_dir_list: IFileDirBaseProps[] = useMemo(() => {
    if (currentFileDirList.length === 0 && currentFileDirConfig.dir_name === '' && currentFileDirConfig.data_dir === '') {
      return poolData.map(item => {
        return {
          id: `${item.data_dir}`,
          dir_name: item.name,
          data_dir: item.data_dir,
          isDirectory: true
        }
      })
    }

    return currentFileDirList.map(item => {
      return {
        id: `${item.parent}${item.name}/`,
        dir_name: item.name,
        data_dir: `${item.parent}${item.name}/`,
        isDirectory: item.xattr?.directory || false
      }
    })
  }, [currentFileDirConfig.data_dir, currentFileDirConfig.dir_name, currentFileDirList, poolData])

  // 当选择的是顶部时使用存储池的数据
  const initData = useCallback(() => {
    setCurrentFileDirList([]);
    setCurrentFileDirConfig({ dir_name: '', data_dir: '' });
  }, [])

  // 获取存储池信息
  const { runAsync: fetchPoolInfo } = useRequest(getPoolInfo, { manual: true });

  // 获取子文件夹信息
  const { runAsync: fetchDirectoryList } = useRequest(listDirectory, { manual: true });

  const getDirectoryList = useCallback(async (path, dir_name, callback: (d: FileItem[]) => void) => {
    console.log(`开始获取文件夹列表: path:${path},dir_name: ${dir_name}`);

    const res = await fetchDirectoryList({
      page: page_config_ref.current, path: { parent: path, recursion: false }
    }).catch((e) => console.log('获取文件夹列表失败: ', e));

    if (res && res.code === 0) {
      callback(res.data.files);
      setCurrentFileDirConfig({ dir_name: dir_name || '', data_dir: path || '' });

      page_config_ref.current = { size: page_config_ref.current.size, token: res.data.page.token };
    }

    return res;

  }, [fetchDirectoryList])

  const getPoolInfoData = useCallback(async () => {
    const res = await fetchPoolInfo({}).catch((e) => console.log('获取存储池信息失败: ', e));
    if (res && res.code === 0) {
      if (res.data.internal_pool.length > 0) {
        setPoolData(res.data.internal_pool);
        setCurrentFileDirConfig({ dir_name: '', data_dir: '' });
        if (res.data.webDAV) setCurrentWebDav(res.data.webDAV);
      }
    }
  }, [fetchPoolInfo])

  useUpdateEffect(() => {
    if (storagePathOpenPlane) {
      getPoolInfoData();
    }
  }, [storagePathOpenPlane])

  const finishCallback = useCallback(() => {
    if (macBackupConfig.isBackup && macBackupConfig.path === '') {
      Toast.show('请设置存储位置');
      return;
    }

    const configNotChange = macBackupConfig.path === '' && macBackupConfig.sizeLimit === 0 && macBackupConfig.isBackup === false;

    if (configNotChange || macBackupConfig.path !== '') {
      console.log(`保存成功,配置信息: ${JSON.stringify(macBackupConfig)}`);
    }
  }, [macBackupConfig])

  // 保存配置右侧icon
  const right = useMemo(() => {
    const configNotChange = macBackupConfig.path === '' && macBackupConfig.sizeLimit === 0 && macBackupConfig.isBackup === false;

    const disabled =
      !configNotChange &&
      ((macBackupConfig.isBackup && macBackupConfig.path === '') ||
        (macBackupConfig.isBackup === false && macBackupConfig.sizeLimit !== 0 && macBackupConfig.path === ''));

    const opacity = configNotChange ? 1
      : macBackupConfig.isBackup && macBackupConfig.path !== '' ? 1
        : macBackupConfig.isBackup && macBackupConfig.path === '' ? 0.2
          : macBackupConfig.isBackup === false && macBackupConfig.sizeLimit !== 0 && macBackupConfig.path === '' ? 0.2 : 1;
    return (
      <div style={{ opacity: opacity, pointerEvents: disabled ? 'none' : 'auto' }}><PreloadImage onClick={finishCallback} src={!isDarkMode ? finish : finish_dark} alt='submit' /></div>
    )
  }, [finishCallback, isDarkMode, macBackupConfig.isBackup, macBackupConfig.path, macBackupConfig.sizeLimit])

  // 浮动面板选中后回调
  const planeFinish = useCallback(() => {
    console.log('浮动面板选中', selectedValue, '回调并关闭浮动面板');
    setMacBackupConfig(p => ({ ...p, path: selectedValue }));
    setStoragePathOpenPlane(false);
    setCurrentFileDirList([]);
    setCurrentFileDirConfig({ data_dir: '', dir_name: '' })
  }, [selectedValue])

  // 弹窗navBar的右侧icon
  const floatNavRight = useMemo(() => {
    const opacity = selectedValue === '' ? 0.2 : 1; // 判断是否选中路径，未选中的不显示提交按钮
    return (
      <div style={{ opacity: opacity, pointerEvents: selectedValue === '' ? 'none' : 'auto' }}><PreloadImage onClick={planeFinish} src={!isDarkMode ? finish : finish_dark} alt='submit' /></div>
    )
  }, [isDarkMode, planeFinish, selectedValue])

  const isBackupOnChange = useCallback((checked: boolean) => {
    if (!checked) {
      // 取消
      setMacBackupConfig(prev => ({ ...prev, isBackup: checked }));
      return;
    }

    const agreementIsOpen = false; // 确认是否已开启协议 todo

    if (!agreementIsOpen) {
      modalShow('开启SMB/AFP网络共享服务', <span>若要Time Machine功能正确发现局域网内的智能存储设备，需要开启SMB/AFP网络共享服务。确定开启？</span>, (m) => {
        // 确定开启 todo
        console.log('确认开启该服务协议')

        m.destroy();
        setMacBackupConfig(prev => ({ ...prev, isBackup: checked }));
        console.log('成功开启，备份助手功能开启')
      }, () => null, false, { position: 'bottom' });
    }
  }, [])

  useUpdateEffect(() => {
    form.setFieldsValue({ size: macBackupConfig.sizeLimit, unit: macBackupConfig.unit });
  }, [macBackupConfig.sizeLimit, macBackupConfig.unit]);

  // 打开容量限制弹窗
  const openSizeLimitModal = useCallback(() => {

    modalShow('容量限制', <NumberInput form={form} />, (m) => {
      form.validateFields().then(value => {
        setMacBackupConfig(prev => ({ ...prev, sizeLimit: value.size, unit: value.unit }));
      })
      m.destroy();
    }, () => null, false, { position: 'center' });
  }, [form])

  const optList: IListData[] = useMemo(() => {
    return [
      {
        key: 'open_mac_backup', type: 'switch', label: '开启Mac备份助手功能', render: () => {
          return <Switch value={macBackupConfig.isBackup} onChange={isBackupOnChange} />
        }
      },
      {
        key: 'back_path', type: 'text', 'label': '存储位置', render: () => {
          return <span className={`${styles.list_right_container} ${macBackupConfig.isBackup === false ? styles.list_right_container_disabled : ''}`} onClick={() => setStoragePathOpenPlane(true)}>
            <span className={styles.list_right_container_span}>{`${macBackupConfig.path === '' ? '未设置' : macBackupConfig.path}`}</span>
            <PreloadImage className={styles.list_right_container_img} src={isDarkMode ? next_dark : next} alt="next" />
          </span>
        }
      },
      {
        key: 'back_size_limit', type: 'input', 'label': '容量限制', render: () => {
          return <span className={`${styles.list_right_container} ${macBackupConfig.isBackup === false ? styles.list_right_container_disabled : ''}`} onClick={openSizeLimitModal}>
            <span className={styles.list_right_container_span}>{`${macBackupConfig.sizeLimit === 0 ? '未设置' : `${macBackupConfig.sizeLimit}${macBackupConfig.unit}`}`}</span>
            <PreloadImage className={styles.list_right_container_img} src={isDarkMode ? next_dark : next} alt="next" />
          </span>
        }
      }
    ]
  }, [isBackupOnChange, isDarkMode, macBackupConfig.isBackup, macBackupConfig.path, macBackupConfig.sizeLimit, macBackupConfig.unit, openSizeLimitModal])

  const back2Index = useCallback(() => {
    const configNotChange = macBackupConfig.path === '' && macBackupConfig.sizeLimit === 0 && macBackupConfig.isBackup === false;

    const back = () => {
      console.log('退出设置');
    }

    if (!configNotChange) {
      modalShow('Mac备份助手未完成设置', <span>本页面有尚未保存的设置。确定退出？退出后将清空本页面内未保存的全部设置。</span>, (m) => {
        m.destroy();
        console.log('继续设置')
      }, () => {
        back();
      }, false, { position: 'bottom', cancelBtnText: '关闭退出', okBtnText: '继续设置', okBtnStyle: { background: 'var(--primary-color)', color: '#fff' } });
      return;
    }

    back();
  }, [macBackupConfig.isBackup, macBackupConfig.path, macBackupConfig.sizeLimit])

  return (
    <div className={styles.root_container}>
      <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} right={right} onBack={back2Index} />
      <div className={styles.content}>
        <div className={styles.header}>
          <span className={styles.header_span}>Mac备份助手</span>
        </div>
        <div className={styles.body}>
          <div className={styles.show_backup_machine_container}>
            <PreloadImage className={styles.backup_machine_img} src={mac_show_img} alt='mac' />
            <PreloadImage className={styles.backup_machine_img} src={nas_show_img} alt='nas' />
          </div>
          <div className={styles.body_span_container}>
            <span className={styles.body_span}>开启MAC备份助手功能后，可通过MAC电脑-Time Machine功能，发现本机指定的磁盘路径，并将其设置为外部备份磁盘</span>
          </div>
        </div>
        <div className={styles.footer}>
          <List dataSource={optList} />
        </div>
      </div>

      <FloatPanel showFloatPanel={storagePathOpenPlane} setShowFloatPanel={setStoragePathOpenPlane} className={styles.storage_plane} anchors={[0, 800]}>
        <div className={styles.float_panel_container}>
          <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} title='设置存储位置' right={floatNavRight}
            onBack={() => { setStoragePathOpenPlane(false); setCurrentFileDirList([]); setCurrentFileDirConfig({ data_dir: '', dir_name: '' }) }} />
          <FileSelectorUtil onChange={setSelectedValue} setCurrentFileDirList={setCurrentFileDirList} data={{
            currentFileDirConfig: currentFileDirConfig,
            current_file_dir_list: file_dir_list,
            web_alias_root: currentWebDav.alias_root
          }} dirOnClick={getDirectoryList} initData={initData} requestPageOpt={page_config_ref} />
        </div>
      </FloatPanel>
    </div>
  )
}

export default MacBackup_App_Index_Page;