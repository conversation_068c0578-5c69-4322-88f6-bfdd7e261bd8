import React, { FC, useEffect } from "react";
import { Popover } from "antd-mobile";
import styles from "./index.module.scss";
import { PreloadImage } from "../Image";
import { getDeviceType } from "@/utils/DeviceType";

export interface Option {
  label: string;
  value: any;
  icon?: string;
  subtitle?: string; // 副标题（可选)
  color?: string; // 字体颜色
}

interface PopoverSelectorProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  value?: string;
  options?: Option[];
  onChange: (value: string) => void;
  children: React.ReactElement;
  onClose?: () => void;
  theme?: {
    selectedColor?: string;
    active?: string;
  }
  onlySelect?: boolean; // 仅选择并回调，不显示勾选、active
  placement?: 'top' | 'top-start' | 'top-end' | 'right' | 'right-start' | 'right-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'left' | 'left-start' | 'left-end'
}

const PopoverSelector: FC<PopoverSelectorProps> = ({
  visible,
  onVisibleChange,
  value,
  options = [],
  onChange,
  children,
  onClose,
  theme,
  onlySelect,
  placement = 'bottom-end'
}) => {
  // 当options变化时，如果没有value且options有值，默认选中第一个
  useEffect(() => {
    if (!onlySelect && options.length > 0 && !value) {
      onChange(options[0].value);
    }
  }, [options, value, onChange, onlySelect]);

  const deviceType = getDeviceType();

  const itemStyle = {
    "--option-selected-color": theme?.selectedColor || deviceType ? "var(--primary-color)" : "var(--primary-color)",
    "--option-active-bg": theme?.active || deviceType ? "var(--primary-btn-background-color)" : "var(--primary-btn-background-color)",
  } as React.CSSProperties;

  // 如果没有options，返回null不渲染
  if (!options || options.length === 0) {
    return null;
  }

  return (
    <Popover
      visible={visible}
      onVisibleChange={(v) => {
        onVisibleChange(v)
      }}
      content={
        <div className={styles.popoverContent}>
          {options.map((option) => (
            <div
              style={onlySelect ? {} : itemStyle}
              key={option.value}
              className={`${styles.optionItem} ${value === option.value ? styles.selected : ""
                }`}
              onClick={() => {
                onChange(option.value);
                onVisibleChange(false);
                if (onClose) onClose();
              }}
            >
              {option.icon && <PreloadImage className={styles.checkIcon} src={option.icon} alt="icon" />}
              <div className={`${styles.optionLabel} ${value === option.value && !onlySelect ? styles.selected : ""}`}>
                <span style={{ color: option.color }}>{option.label}</span>
                {option.subtitle && <span style={{ color: option.color }} className={`${styles.optionSubtitle}  ${value === option.value && !onlySelect ? styles.selected : ""}`}>{option.subtitle}</span>}
              </div>
              {value === option.value && !onlySelect && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width="20"
                  height="20"
                  aria-label="确认标记"
                  role="img"
                >
                  <path
                    d="M8.5 13.4L5.3 10.2 4 11.5l4.5 4.5L18 5.3 16.7 4 8.5 13.4z"
                    strokeWidth="1"
                    stroke={theme ? theme.selectedColor : deviceType ? "var(--primary-color)" : "var(--primary-color)"}
                    fill={theme ? theme.selectedColor : deviceType ? "var(--primary-btn-background-color)" : "var(--primary-btn-background-color)"}
                    strokeLinejoin="miter"
                    strokeMiterlimit="4"
                    vectorEffect="non-scaling-stroke"
                  />
                </svg>
              )}
            </div>
          ))}
        </div>
      }
      trigger="click"
      placement={placement}
      className={styles.customPopover}
    >
      {children}
    </Popover>
  );
};

export default PopoverSelector;