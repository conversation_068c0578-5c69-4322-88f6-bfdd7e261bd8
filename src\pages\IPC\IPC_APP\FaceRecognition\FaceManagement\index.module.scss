.container {
  min-height: calc(100vh - 35px);
  display: flex;
  flex-direction: column;
}

.header {
  padding: 0 10px;
  .backIcon {
    width: 40px;
    height: 40px;
  }
}

.title {
  font-size: 28px;
  font-weight: 400;
  color: var(--title-color);
  padding: 10px 16px;
}

.section {
  padding: 0 16px;
  margin-bottom: 24px;
}

.sectionTitle {
  font-size: 12px;
  color: #8c93b0;
  margin: 24px 0;
}

.faceGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.faceItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.avatarWrapper {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 8px;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.addButton {
  background-color: #f5f5f5;
}

.faceName {
  font-size: 12px;
  color: var(--text-color);
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.thresholdPopup {
  padding: 16px 30px;
  background-color: var(--modal-content-background-color);
  border-radius: 16px;

  .popupHeader {
    margin-bottom: 24px;

    .popupTitle {
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      color: var(--text-color);
    }
  }

  .popupBody {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .thresholdInput {
      width: 100%;
      height: 44px;
      border-radius: 16px;
      box-sizing: border-box;
      padding: 0 16px;
      font-size: 16px;
      text-align: center;
      background-color: var(--cancel-btn-background-color);
      :global {
        .adm-input-element {
          color: var(--text-color);
        }
      }

      &:focus {
        border-color: #1677ff;
        outline: none;
      }
    }

    .popupButtons {
      display: flex;
      gap: 12px;
      padding: 12px;

      .cancelButton {
        flex: 1;
        border-radius: 30px;
        background-color: var(--cancel-btn-background-color);
        color: var(--title-color);
        min-height: 40px;
        border: none;
      }

      .confirmButton {
        flex: 1;
        background-color: var(--primary-color);
        border-radius: 30px;
        color: #fff;
        border: none;
        min-height: 40px;
      }
    }
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80px 20px;
  text-align: center;
  min-height: 400px;
  flex: 1;
}

.emptyIcon {
  img {
    width: 120px;
    height: 120px;
    object-fit: contain;
  }
}

.emptyText {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 8px;
}
