import { useTheme } from "@/utils/themeDetector";
import { useMemo } from "react";
import { useRouteMatch } from "react-router-dom";
import routers from ".";

export interface IMenus {
  key: string,
  label: string,
  icon: string,
  path: string,
  right_icon?: string
}

const useMenus = () => {
  const router = useMemo(() => [...routers], []);
  const { isDarkMode } = useTheme();
  const { path } = useRouteMatch();

  const menus: IMenus[] = useMemo(() => {
    const curMenuItem = router.find((it) => it.path === path);
    const menuList: IMenus[] = [];
    if (curMenuItem && curMenuItem.children) {
      curMenuItem.children.forEach((item, index) => {
        if (item.show !== false) {
          menuList.push({
            key: item.key || index.toString(),
            label: item.label || '',
            icon: item.icon ? isDarkMode ? item.icon.dark : item.icon.light : '',
            path: `${path}${item.path}`, // 拼接当前菜单后的路径，router里的path需要带/
            right_icon: item.right_icon
          })
        }
        
        if (item.children) {
          item.children.forEach((it) => {
            if (it.show) menuList.push({
              key: it.key || index.toString(),
              label: it.label || '',
              icon: it.icon ? isDarkMode ? it.icon.dark : it.icon.light : '',
              path: `${path}${item.path}${it.path}`,
              right_icon: it.right_icon
            })
          })
        }
      })
    }
    return menuList;
  }, [isDarkMode, path, router])

  return menus;
}

export default useMenus;