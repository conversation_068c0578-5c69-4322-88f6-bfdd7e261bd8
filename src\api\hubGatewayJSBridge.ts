import { sendToEnvironment } from '@/utils/microAppUtils';

const gotoHelp = 'gateway_gotoHelpCenter';

export const gotoHelpCenter = (callback: (res: any) => void) => { 
  sendToEnvironment(
    { methodName: gotoHelp, params: {associatedFunction:'hubGateway'} },
    { params: { cmd: "hubGateway" } },
    (response: any) => {
      if (response.code === 0 || response.cmd === 'hubGateway') {
        callback(response);
      } else {
        console.error(`打开失败:${response.msg}`);
      }
    }
  )
}