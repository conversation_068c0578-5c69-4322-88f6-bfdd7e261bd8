.container{
  padding: 0 28px;
  height: 100%;
  background-color: var(--background-color);
  // overflow-y: auto;
  
  .tabsHeader_span{
    font-weight: 400;
    color:var(--library-title-color);
    font-size: 30px;
  }
}
.right{
  padding:0 10px
}
.library_title {
  display: block;
  width: 100%;
  padding:24px 0px;
  font-family: MiSans W;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: var(--system-text-color);
}
.content{
  overflow-y: auto;
  height: 100%;
  padding-bottom: 100px;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE和Edge */
}
.library_content_container {
  margin: 0 0 26px 0;
}
.divider_container{
  :global{
    .adm-divider-horizontal{
      border-color: var(--thinLine-background-color);
    }
  }
}