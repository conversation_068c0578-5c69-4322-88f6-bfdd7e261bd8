import { sendDataToApp, sendDataToPc, sendToEnvironment } from "@/utils/microAppUtils"

const _DOWNLOAD_To_ALBUM = 'normal_saveFileToAlbum';
const _DOWNLOAD = 'normal_download';
const _SHARE = 'IPC_shareFile';
const _GO_BACK = 'normal_goback';
const _OPEN_CAMERA = 'IPC_getCameraDetail';
const _DELETE_TO_ALBUM = 'normal_deleteDownloadFile';
const _SET_USER_ICON = 'IPC_setUserIcon';

/**
 * 从给定URL下载文件
 *
 * @param url 文件下载链接
 * @param callback 回调函数，用于处理下载结果
 * @returns 无返回值
 */
export const downloadAndSaveWithPlayer = async (urls: string[], callback: (res: any) => void): Promise<void> => {
  if (urls.length < 1) return Promise.reject();
  return new Promise((rs, rj) => {
    sendToEnvironment(
      { methodName: _DOWNLOAD, params: { pathList: urls } }, // app只支持单文件下载
      { params: { cmd: "download", spath: urls } },
      (response: any) => {
        if (response.code === 0 || response.cmd === 'download') {
          callback(response)
          rs();
        } else {
          rj(new Error(`下载失败:${response.msg}`))
        }
      }
    )
  })
}

/**
 * app调用下载并保存到相册
 * 
 * @param urls 
 * @param callback 
 * @returns 
 */
export const downloadToAlbumWithPlayerByApp = async (type: "1" | "2", callback: (res: any) => void, base64?: string, path?: string, name?: string): Promise<void> => {

  return new Promise((rs, rj) => {
    sendDataToApp(_DOWNLOAD_To_ALBUM, { type, base64, path, name }, (response: any) => {
      if (response.code === 0) {
        callback(response);
        rs();
        console.log('下载并保存到相册成功', response);
      } else {
        rj(new Error(`下载失败:${response.msg}`))
      }
    })
  })
}

/**
 * app调用从相册内删除
 * 
 * @param urls 
 * @param callback 
 * @returns 
 */
export const deleteToAlbumWithPlayerByApp = async (name: string, callback: (res: any) => void): Promise<void> => {

  return new Promise((rs, rj) => {
    sendDataToApp(_DELETE_TO_ALBUM, { name }, (response: any) => {
      if (response.code === 0) {
        callback(response);
        rs();
        console.log('从相册删除成功', response);
      } else {
        rj(new Error(`从相册删除失败:${response.msg}`))
      }
    })
  })
}

/**
 * pc调用下载后调用任务中心
 *
 * 调用任务中心以执行某些操作。这是一个异步函数，返回一个Promise对象，该对象在任务中心被调用后解决（resolve）。
 *
 * @returns 一个空的Promise对象，当任务中心被成功调用后，该Promise对象将被解决（resolve）。
 */
export const callTaskCenter = async (): Promise<void> => {
  return new Promise((rs, rj) => {
    sendDataToPc({ cmd: 'openTaskList' }, (resp: any) => {
      if (resp.cmd === 'openTaskList') {
        rs();
      } else {
        rj(new Error(`'调起任务中心失败,'${resp.msg}`));
      }
    })
  })
}

/**
 * 分享文件
 *
 * @param url 需要下载并分享的文件路径
 * @param callback 回调函数，用于处理分享结果
 * @returns 一个 Promise 对象，用于处理异步操作
 */
export const downloadAndShareWithPlayer = (url: string, callback: (res: any) => void): Promise<void> => {
  return new Promise((rs, rj) => {
    sendDataToApp(
      _SHARE, { path: JSON.stringify(url) },
      (response: any) => {
        if (response.code === 0) {
          callback(response)
          rs();
        } else {
          rj(new Error(`app分享失败:${response.msg}`))
        }
      }
    )
  })
}

/**
 * 退出web客户端
 *
 * 调用此方法可以退出当前web客户端
 *
 * @param callback 回调函数，用于处理退出结果
 * @returns 一个 Promise 对象，用于处理异步操作
 */
export const exitWebClient = (callback?: (res: any) => void): Promise<void> => {
  return new Promise((rs, rj) => {
    sendToEnvironment(
      { methodName: _GO_BACK, params: {} },
      { params: { cmd: "goback" } },
      (response: any) => {
        if (response.code === 0 || response.cmd === 'goback') {
          if (callback) callback(response);
          rs();
        } else {
          rj(new Error(`退出失败:${response.msg}`));
        }
      }
    )
  })
}


export const needOpenCamera = (callback?: (res: any) => void): Promise<void> => {
  return new Promise((rs, rj) => {
    sendToEnvironment(
      { methodName: _OPEN_CAMERA, params: {} },
      { params: { cmd: "getCameraInfo" } },
      (response: any) => {
        if (response.code === 0 || response.cmd === 'getCameraInfo') {
          if (callback) callback(response);
          rs();
        } else {
          rj(new Error(`获取cameraInfo失败:${response.msg}`));
        }
      }
    )
  })
}

/**
 * 设置IPC头像
 *
 * @param callback 回调函数，用于处理设置结果
 * @returns 一个 Promise 对象，用于处理异步操作
 */
export const setIPCUserIcon = (callback?: (res: any) => void): Promise<void> => {
  return new Promise((rs, rj) => {
    sendDataToApp(
      _SET_USER_ICON,
      {},
      (response: any) => {
        if (response.code === 0) {
          if (callback) callback(response);
          rs();
          console.log('设置IPC头像成功', response);
        } else {
          rj(new Error(`设置IPC头像失败:${response.msg}`));
        }
      }
    )
  })
}