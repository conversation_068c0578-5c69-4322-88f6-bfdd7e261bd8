import React, { useState } from 'react';
import { Button } from 'antd';
import { Toast } from 'antd-mobile';
import { useUser } from '@/utils/UserContext';
import styles from './index.module.scss';
import { modalShow } from '@/components/List';
import { unbindBaiduNetdisk } from '@/api/nasDisk';
import { logOutBD } from '@/api/netDiskJSBridge';

import Counter from '../components/Counter';

interface IPrivilegeItem {
  feature: string;
  nasVip: string | React.ReactNode;
  svipVip: string | React.ReactNode;
  freeUser: string | React.ReactNode;
}

const Mine: React.FC = () => {
  const { userInfo: globalUserInfo , isLoading } = useUser();

  // 格式化容量大小
  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化存储容量显示
  const getStorageInfo = (): string => {
    if (globalUserInfo && globalUserInfo.used_space !== undefined && globalUserInfo.total_space !== undefined) {
      const used = formatSize(globalUserInfo.used_space);
      const total = formatSize(globalUserInfo.total_space);
      return `${used}/${total}`;
    }
    return '1.2TB/2TB'; // 默认值
  };

  // 获取用户名
  const getUserName = (): string => {
    if (globalUserInfo) {
      return globalUserInfo.netdisk_name || globalUserInfo.baidu_name || '郭德钢';
    }
    return '郭德钢'; // 默认值
  };

  // 获取用户头像
  const getUserAvatar = (): string => {
    if (globalUserInfo) {
      return globalUserInfo.avatar_url ;
    }
    return ''; // 默认值
  };

  // 获取百度账号信息
  const getBaiduAccount = (): string => {
    if (globalUserInfo && globalUserInfo.uk) {
      return globalUserInfo.uk.toString();
    }
    return '1234567'; // 默认值
  };

  // 判断是否是NAS VIP
  const isNasVip = (): boolean => {
    return globalUserInfo?.nas_vip === 1;
  };

  // 判断是否是SVIP
  const isSvip = (): boolean => {
    return globalUserInfo ? globalUserInfo.vip_type > 0 : false;
  };

  // 收银台状态
  const [showCounter, setShowCounter] = useState(false);

  // 特权对比数据
  const privilegeData: IPrivilegeItem[] = [
    {
      feature: '手动上传',
      nasVip: '上传至任一文件夹',
      svipVip: '上传至限制指定文件夹',
      freeUser: '上传至限制指定文件夹'
    },
    {
      feature: '自动上传下载',
      nasVip: <span className={styles.checkIcon}>✓</span>,
      svipVip: <span className={styles.dashIcon}>——</span>,
      freeUser: <span className={styles.dashIcon}>——</span>
    },
    {
      feature: '极速下载',
      nasVip: <span className={styles.checkIcon}>✓</span>,
      svipVip: <span className={styles.checkIcon}>✓</span>,
      freeUser: <span className={styles.dashIcon}>——</span>
    },
    {
      feature: '并行下载数量',
      nasVip: '3',
      svipVip: '1',
      freeUser: '1'
    },
    {
      feature: '单个文件上限',
      nasVip: '2048G',
      svipVip: '300G',
      freeUser: '4G'
    }
  ];

  const handleUnbindAccount = () => {
    modalShow(
      "解绑账号",
      "解除绑定后，将无法使用网盘相关服务，且已配置的自动同步任务和下载中任务也将清空。确定解绑？",
      async (m) => {
        try {
          // 先调用解绑接口
          const unbindResult = await unbindBaiduNetdisk({ action: "unbind" });

          if (unbindResult.code === 0) {
            // 解绑成功后调用logOutBD接口
            await logOutBD((res) => {
              if (res.code === 0) {
                Toast.show({
                  content: '解绑成功，退出登录',
                  duration: 2000,
                  position: 'bottom'
                });
              } 
            });
          } else {
            Toast.show({
              content: `解绑失败: ${unbindResult.result}`,
              duration: 2000,
              position: 'bottom'
            });
          }
        } catch (error) {
          console.error('解绑失败:', error);
          Toast.show({
            content: '解绑失败，请稍后重试',
            duration: 2000,
            position: 'bottom'
          });
        }
        m.destroy();
      },
      () => {
        // 取消操作
        console.log('用户取消解绑');
      },
      false,
      {
        position: 'center',
        okBtnText: '确定',
        cancelBtnText: '取消',
        okBtnStyle: { backgroundColor: '#1890ff', color: '#fff' },
        cancelBtnStyle: { backgroundColor: '#f5f5f5', color: '#000' }
      }
    );
  };

  const handleUpgradeVip = () => {
    console.log('开通网盘NAS超级会员');
    setShowCounter(true);
  };

  // 支付相关回调
  const handlePaymentSuccess = () => {
    console.log('支付成功');
    // 这里可以添加支付成功后的逻辑，比如刷新用户信息
  };

  const handlePaymentError = (error: string) => {
    console.log('支付失败:', error);
    // 这里可以添加支付失败后的逻辑
  };

  const handlePaymentCancel = () => {
    console.log('支付取消');
    // 这里可以添加支付取消后的逻辑
  };

  const handleCloseCounter = () => {
    setShowCounter(false);
  };

  return (
    <div className={styles.minePage}>
      {/* 用户信息区域 */}
      <div className={styles.userInfoSection}>
        <div className={styles.userProfile}>
          <img src={getUserAvatar()} alt="用户头像" className={styles.avatar} />
          <div className={styles.userDetails}>
            <h2 className={styles.userName}>
              {isLoading ? '加载中...' : getUserName()}
            </h2>
            <p className={styles.accountInfo}>
              百度账号：{isLoading ? '加载中...' : getBaiduAccount()}
            </p>
            <p className={styles.storageInfo}>
              网盘容量：{isLoading ? '加载中...' : getStorageInfo()}
            </p>
          </div>
          <Button 
            type="link" 
            className={styles.unbindBtn}
            onClick={handleUnbindAccount}
          >
            解绑账号
          </Button>
        </div>
      </div>

      {/* 会员状态区域 */}
      <div className={styles.membershipSection}>
        <div className={styles.membershipStatus}>
          <div className={styles.statusItem}>
            <span className={styles.statusIcon}>💎</span>
            <span className={`${styles.statusText} ${isSvip() ? styles.svipActive : styles.memberInactive}`}>
              {isLoading ? '加载中...' : (isSvip() ? '已开通SVIP会员' : 'SVIP会员暂未开通')}
            </span>
          </div>
          <div className={styles.statusItem}>
            <span className={styles.statusIcon}>💎</span>
            <span className={`${styles.statusText} ${isNasVip() ? styles.nasVipActive : styles.memberInactive}`}>
              {isLoading ? '加载中...' : (isNasVip() ? '已开通网盘NAS会员' : '网盘NAS会员暂未开通')}
            </span>
          </div>

            <Button
              type="primary"
              size="large"
              className={styles.upgradeBtn}
              onClick={handleUpgradeVip}
            >
              {globalUserInfo?.nas_vip===1?'续费网盘NAS会员':'开通网盘NAS会员'}
            </Button>
        </div>
      </div>

      {/* 特权对比表格 */}
      <div className={styles.privilegeSection}>
        <h3 className={styles.privilegeTitle}>特权对比</h3>
        <div className={styles.privilegeTable}>
          <div className={styles.tableHeader}>
            <div className={styles.headerCell}>特权</div>
            <div className={`${styles.headerCell} ${styles.nasVipHeader}`}>网盘NAS会员</div>
            <div className={`${styles.headerCell} ${styles.sVipHeader}`}>SVIP会员</div>
            <div className={styles.headerCell}>免费用户</div>
          </div>
          {privilegeData.map((item, index) => (
            <div key={index} className={styles.tableRow}>
              <div className={styles.cell}>{item.feature}</div>
              <div className={`${styles.cell} ${styles.nasVipCell}`}>{item.nasVip}</div>
              <div className={`${styles.cell} ${styles.sVipCell}`}>{item.svipVip}</div>
              <div className={styles.cell}>{item.freeUser}</div>
            </div>
          ))}
        </div>
      </div>

      {/* 底部客服信息 */}
      <div className={styles.footer}>
        <p className={styles.serviceInfo}>百度网盘客服电话：************</p>
      </div>

      {/* 收银台组件 */}
      <Counter
        isVisible={showCounter}
        onClose={handleCloseCounter}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
        onPaymentCancel={handlePaymentCancel}
        // isPC=true
        nasId="55"
        accessToken={globalUserInfo?.token}
        title="开通网盘NAS超级会员"
      />
    </div>
  );
};

export default Mine;
