import { NavBar, Button, Avatar, Divider, Toast } from "antd-mobile";
import { Route, Switch, useHistory, useRouteMatch } from "react-router-dom";
import { RightOutline } from "antd-mobile-icons";
import { modalShow } from "@/components/List";
import { useState } from "react";
import { useUser } from "@/utils/UserContext";
import styles from "./index.module.scss";
import Counter from "./Counter";
import CommonUtils from '@/utils/CommonUtils';
import { unbindBaiduNetdisk } from "@/api/nasDisk";
import { logOutBD } from "@/api/netDiskJSBridge";

const Members = () => {
    const { path } = useRouteMatch();
    const history = useHistory();
    const { userInfo } = useUser();
    const [, setShowCounter] = useState(false);

    const handleBack = () => {
        history.goBack();
    };

    // 开通百度网盘NAS会员
    const handleOpenMember = () => {
        history.push('/baiduNetdisk_app/members/counter')
    };

    // 关闭收银台
    const handleCloseCounter = () => {
        setShowCounter(false);
    };

    // 支付成功回调
    const handlePaymentSuccess = () => {
        // 这里可以添加刷新会员状态的逻辑
        console.log('支付成功，刷新会员状态');
    };

    // 支付失败回调
    const handlePaymentError = (error: string) => {
        console.log('支付失败:', error);
    };

    // 支付取消回调
    const handlePaymentCancel = () => {
        console.log('支付取消');
    };

    const handleUnbindAccount = () => {
        modalShow(
            "解绑账号",
            "解除绑定后，将无法使用网盘相关服务，且已配置的自动同步任务和下载中任务也将清空。确定解绑？",
            async (m) => {
                try {
                    // 先调用解绑接口
                    const unbindResult = await unbindBaiduNetdisk({ action: "unbind" });
                    
                    if (unbindResult.code === 0) {
                        // 解绑成功后调用logOutBD接口
                         logOutBD((res) => {                      
                            alert(res.msg)
                        });
                    } else {
                        Toast.show({
                            content: `解绑失败: ${unbindResult.result}`,
                            duration: 2000,
                            position: 'bottom'
                        });
                    }
                } catch (error) {
                    console.error('解绑失败:', error);
                    Toast.show({
                        content: '解绑失败，请稍后重试',
                        duration: 2000,
                        position: 'bottom'
                    });
                }
                m.destroy();
            },
            () => {
                // 取消操作
                console.log('用户取消解绑');
            },
            false,
            {
                position: 'bottom',
                okBtnText: '确定',
                cancelBtnText: '取消',
                okBtnStyle: { backgroundColor: '#34BBBF', color: '#fff' },
                cancelBtnStyle: { backgroundColor: '#f5f5f5', color: '#666' }
            }
        );
    };

    // 格式化存储空间
    const formatBytes = (bytes: number): string => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // 获取VIP状态显示文本
    const getVipStatusText = (vip_type: number): string => {
        return vip_type > 0 ? '已开通' : '未开通';
    };

    // 获取NAS VIP状态显示文本
    const getNasVipStatusText = (nas_vip: number): string => {
        return nas_vip === 1 ? '已开通' : '未开通';
    };

    // 如果没有用户信息，显示加载状态或默认信息
    if (!userInfo) {
        return (
            <div className={styles.membersContainer}>
                <div className={styles.navBar}>
                    <NavBar onBack={handleBack}>我的会员</NavBar>
                </div>
                <div className={styles.content}>
                    <div className={styles.userSection}>
                        <div className={styles.avatar}>
                            <Avatar
                                src=""
                                style={{ '--size': '56px', borderRadius: '12px' }}
                            />
                        </div>
                        <span className={styles.username}>加载中...</span>
                    </div>
                </div>
            </div>
        );
    }


    return (
        <>
            <div className={styles.membersContainer}>
                <Switch>
                    <Route exact path={path}>
                        <div className={styles.navBar}>
                            <NavBar onBack={handleBack}>我的会员</NavBar>
                        </div>

                        <div className={styles.content}>
                            {/* 用户信息区域 */}
                            <div className={styles.userSection}>
                                <div className={styles.avatar}>
                                    <Avatar
                                        src={userInfo.avatar_url || ""}
                                        style={{ '--size': '56px', borderRadius: '12px' }}
                                    />
                                </div>
                                <span className={styles.username}>
                                    {userInfo.baidu_name || userInfo.netdisk_name || '未知用户'}
                                </span>
                            </div>

                            {/* 账号信息区域 */}
                            <div className={styles.infoSection}>
                                <div className={styles.infoItem}>
                                    <span className={styles.infoLabel}>百度账号</span>
                                    <span className={styles.infoValue}>
                                        {userInfo.uk || userInfo.baidu_name || '未知'}
                                    </span>
                                </div>

                                <div className={styles.infoItem}>
                                    <span className={styles.infoLabel}>网盘容量</span>
                                    <span className={styles.infoValue}>
                                        {formatBytes(userInfo.used_space || 0)}/{formatBytes(userInfo.total_space || 0)}
                                    </span>
                                </div>
                            </div>
                            <Divider />
                            {/* 会员状态区域 */}
                            <div className={styles.infoSection}>
                                <div className={styles.infoItem}>
                                    <span className={styles.infoLabel}>SVIP会员</span>
                                    <span className={styles.memberStatus}>
                                        {getVipStatusText(userInfo.vip_type || 0)}
                                    </span>
                                </div>

                                <div className={styles.infoItem}>
                                    <span className={styles.infoLabel}>网盘NAS会员</span>
                                    <span className={styles.memberStatus}>
                                        {getNasVipStatusText(userInfo.nas_vip || 0)}
                                    </span>
                                </div>
                                {userInfo.nas_vip === 1 && (
                                    <div className={styles.infoItem}>
                                        <span className={styles.infoLabel}>到期时间</span>
                                        <span className={styles.memberStatus}>
                                            {CommonUtils.formatTimestamp(userInfo.end_time, 'YYYY年MM月DD日')}
                                        </span>
                                    </div>
                                )}
                                <div className={styles.infoItem} onClick={handleUnbindAccount}>
                                    <span className={styles.infoLabel}>解绑账号</span>
                                    <RightOutline className={styles.unbindArrow} />
                                </div>
                            </div>
                            {/* 开通会员按钮 */}
                            <div className={styles.buttonContainer}>
                                <Button
                                    color="warning"
                                    block
                                    size="large"
                                    onClick={handleOpenMember}
                                    className={styles.memberButton}
                                >
                                    {userInfo.nas_vip === 1 ? '续费百度网盘NAS会员' : '开通百度网盘NAS会员'}
                                </Button>
                            </div>
                        </div>
                    </Route>
                    <Route exact path={`/baiduNetdisk_app/members/counter`}>
                        <Counter
                            isVisible={true}
                            onClose={handleCloseCounter}
                            onPaymentSuccess={handlePaymentSuccess}
                            onPaymentError={handlePaymentError}
                            onPaymentCancel={handlePaymentCancel}
                            showHeader={false}
                            accessToken={userInfo.token}
                        />
                    </Route>
                </Switch>
            </div>
        </>
    );
};

export default Members;
