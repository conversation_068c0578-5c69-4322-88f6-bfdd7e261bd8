.sidebar_container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 16px 20px 16px;
  user-select: none;
}

.sidebar_line {
  width: 200px;
  height: 20px;
  text-align: center;
  margin: 20px 0;

  img {
    width: 100%;
  }
}

.sidebar_item_container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sidebar_opt {
  width: 100%;
  height: 90px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 40px;

  span {
    color: rgba(255, 255, 255, 0.6);
    font-family: "MI Lan Pro";
    font-size: 30px;
    font-style: normal;
    font-weight: 340;
    line-height: normal;
  }

  img {
    height: 30px;
    margin-right: 20px;
  }

  &:focus {
    background-color: #fff;
    border-radius: 45px;

    span {
      color: #000 !important;
      font-family: MiSans;
      font-size: 32px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
  }
}

// .selected {
//   background-color: #fff;
//   border-radius: 45px;

//   span {
//     color: #000 !important;
//     font-family: MiSans;
//     font-size: 32px;
//     font-style: normal;
//     font-weight: 600;
//     line-height: normal;
//   }
// }
.user_avatar {
  width: 54px;
  height: 54px;
  border-radius: 50% !important;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 100%;
    height: 100%;
  }
}
