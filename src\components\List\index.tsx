import { List as <PERSON>t<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Form, Input, Modal as AntdModal, Switch, InputNumber, Select } from "antd";
import { useCallback, useEffect, useState } from "react";
import styles from "./index.module.scss";
import { FormInstance, useForm } from "antd/es/form/Form";
import { PreloadImage } from "../Image";
import next from "@/Resources/modal/next.png";
import next_dark from "@/Resources/modal/next_dark.png";
import backIcon from '@/Resources/modal/backIcon_light.png'
import backIcon_dark from '@/Resources/modal/backIcon_dark.png'
import collapse from "@/Resources/modal/collapse.png";
import collapse_dark from "@/Resources/modal/collapse_dark.png";
import { Rule } from "antd/es/form";
import Modal, { IModal } from "../Modal";
import PopoverSelector, { Option } from "@/components/PopoverSelector"
import CommonUtils from "@/utils/CommonUtils";
import { useTheme } from "@/utils/themeDetector";

export interface IListData {
  key: string;
  type: 'text' | 'link' | 'modal' | 'input' | 'switch' | 'select';
  label: string;
  value?: any;
  onCallback?: (value: any) => void;
  onForm?: (form: FormInstance<any>) => void;
  render?: (type: string, value: any, form: FormInstance<any>) => React.ReactNode;
  options?: {
    textColor?: string
    inputOptions?: {
      inputRules?: Rule[]
      isNumber?: boolean
      units?: {
        label: string
        value: string
      }[]
      max?: number
    }
    modalOptions?: Omit<IModal, 'isShow' | 'onConfirm' | 'onCancel'>
    selectOptions?: {
      options: Option[],
    }
  }
}

export interface IListItem {
  data: IListData,
  form: FormInstance<any>
}

const DefaultModalContent = (props: {
  content: React.ReactNode,
  onCancel: () => void,
  onFinish: () => void,
  onlyShow?: boolean,
  options?: {
    okBtnText?: string,
    cancelBtnText?: string
    okBtnStyle?: React.CSSProperties
    cancelBtnStyle?: React.CSSProperties
    contentHeight?: string
  }
}) => {
  const { options, onlyShow } = props;
  const okBtnText = options?.okBtnText || '确定';
  const cancelBtnText = options?.cancelBtnText || '取消';
  return (
    <>
      <div className={styles.modalContent} style={{ height: options?.contentHeight }}>
        {
          props.content
        }
      </div>
      {
        onlyShow ? <></> :
          <div className={styles.modalFooter}>
            <Button className={styles.modalCancelBtn} style={{ ...options?.cancelBtnStyle }} onClick={props.onCancel}>{cancelBtnText}</Button>
            <Button className={styles.modalConfirmBtn} style={{ ...options?.okBtnStyle }} onClick={props.onFinish}>{okBtnText}</Button>
          </div>}
    </>
  )
}

export const modalShow = (label: string, content: React.ReactNode, onFinish: (m: { destroy: () => void }) => void, onCancel?: () => void, onlyShow?: boolean, options?: {
  okBtnText?: string,
  cancelBtnText?: string,
  okBtnStyle?: React.CSSProperties,
  cancelBtnStyle?: React.CSSProperties
  contentHeight?: string,
  position?: 'top' | 'bottom' | 'center',
  backTheme?: 'dark' | 'light',
  top?: string | number, // 调整弹窗位置
  bottom?: string | number // 调整弹窗在底部时的距离
  zIndex?: number
}, extra?: React.ReactNode) => {
  const m = AntdModal.confirm({
    title: <div className={styles.modalShowTitle}>
      {onlyShow ? <PreloadImage style={{ position: 'absolute', left: "18px", cursor: "pointer" }} onClick={() => m.destroy()} src={options?.backTheme === 'dark' ? backIcon_dark : backIcon} alt="close" /> : <></>}
      {label}
      {extra && <div className={styles.modalShowExtra}>{extra}</div>}
    </div>,
    content: (
      <DefaultModalContent onlyShow={onlyShow} onCancel={() => { onCancel && onCancel(); m.destroy(); }} onFinish={() => onFinish(m)} content={content} options={options} />
    ),
    className: `${styles.defaultModalWrapper} ${styles[options?.position || 'top']}`,
    footer: null,
    icon: null,
    destroyOnClose: true,
    maskClosable: true,
    zIndex: options?.zIndex,
    style: options?.position === 'bottom' && options?.bottom !== undefined ?
      { bottom: options.bottom } :
      options?.top !== undefined ? { top: options.top } : undefined
  })
  return m;
}

// 纯文本Item
const TextItem = (props: IListItem) => {
  const { data } = props;

  return (
    <Form.Item name={data.key} noStyle>
      <div className={styles.listItemContainer}>
        <span className={styles.listItem_label}>{data.label}</span>
        <span className={styles.listItem_value} style={{ color: data.options?.textColor }}>{data.value}</span>
      </div>
    </Form.Item>
  )
}

// 选项Item
const SelectItem = (props: IListItem) => {
  const { data, form } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [curValue, setCurValue] = useState<any>(data.value);
  const { isDarkMode } = useTheme();

  const optionsOnChange = useCallback((value) => {
    setCurValue(value);
    form.setFieldValue(data.key, value);
    data.onCallback && data.onCallback(value);
  }, [data, form])

  return (
    <Form.Item name={data.key} noStyle>
      <div className={styles.listItemContainer}>
        <span className={styles.listItem_label}>{data.label}</span>
        <span className={styles.listItem_value} style={{ color: data.options?.textColor }}>
          <PopoverSelector visible={visible} onVisibleChange={setVisible} onChange={optionsOnChange} value={curValue} options={data.options?.selectOptions?.options}>
            <div className={`${styles.listItem_value_container} ${styles.onClick}`}>
              {data.options?.selectOptions?.options.find((item) => item.value === data.value)?.label}
              <PreloadImage src={isDarkMode ? collapse_dark : collapse} alt="select" />
            </div>
          </PopoverSelector>
        </span>
      </div>
    </Form.Item>
  )
}

// 链接跳转Item
const LinkItem = (props: IListItem) => {
  const { data } = props;
  const { isDarkMode } = useTheme();

  return (
    <Form.Item name={data.key} noStyle>
      <div className={styles.listItemContainer}>
        <span className={styles.listItem_label}>{data.label}</span>
        <span className={styles.listItem_value} style={{ color: data.options?.textColor }}>
          <div className={`${styles.listItem_value_container} ${styles.onClick}`}>
            {data.value}
            <PreloadImage src={isDarkMode ? next_dark : next} alt="next" />
          </div>
        </span>
      </div>
    </Form.Item>
  )
}

// 输入框Item
const InputItem = (props: IListItem) => {
  const { data, form } = props;
  const [form1] = useForm();
  const { isDarkMode } = useTheme();

  const onShow = useCallback(() => {
    let numberAndUnit;
    if (data.options?.inputOptions?.isNumber) {
      numberAndUnit = CommonUtils.splitNumberAndUnit(data.value);
    }

    modalShow(data.label, (
      <Form form={form1} initialValues={{ input: data.options?.inputOptions?.isNumber ? numberAndUnit ? numberAndUnit.number : 0 : data.value, unit: numberAndUnit ? numberAndUnit.unit : '' }}>
        <div className={styles.modalInputContainer}>
          {data.options?.inputOptions?.isNumber ?
            <>
              <Form.Item name={'input'} rules={data.options?.inputOptions?.inputRules}>
                <InputNumber max={data.options.inputOptions.max} autoComplete="off" />
              </Form.Item>
              {
                data.options.inputOptions.units ? <>
                  <Form.Item name={'unit'} rules={[{ required: true }]}>
                    <Select options={data.options.inputOptions.units} />
                  </Form.Item>
                </> : <></>
              }
            </>
            :
            <Form.Item name={'input'} rules={data.options?.inputOptions?.inputRules}>
              <Input autoComplete="off" allowClear />
            </Form.Item>
          }
        </div>
      </Form>
    ), (m) => {
      form1.validateFields().then((value) => {
        if (data.options?.inputOptions?.isNumber) {
          data.onCallback && data.onCallback(value);
          form.setFieldValue('unit', value.unit);
          m.destroy()
          return;
        }
        data.onCallback && data.onCallback(value.input);
        form.setFieldValue(data.key, value.input);
        m.destroy();
      }).catch(() => null)
    }, () => { form1.resetFields() }, false, { backTheme: isDarkMode ? 'dark' : 'light' })
  }, [data, form, form1, isDarkMode])

  return (
    <Form.Item name={data.key} noStyle>
      <div className={styles.listItemContainer}>
        <span className={styles.listItem_label}>{data.label}</span>
        <span className={styles.listItem_value} style={{ color: data.options?.textColor }}>
          <div className={`${styles.listItem_value_container} ${styles.onClick}`} onClick={onShow}>
            {data.value}
            <PreloadImage src={isDarkMode ? next_dark : next} />
          </div>
        </span>
      </div>
    </Form.Item>
  )
}

// 弹窗Item
const ModalItem = (props: IListItem) => {
  const { data } = props;
  const { options } = data;
  const [modalIsShow, setModalIsShow] = useState<boolean>(false);
  const { isDarkMode } = useTheme();

  return (
    <>
      <Form.Item name={data.key} noStyle>
        <div className={styles.listItemContainer}>
          <span className={styles.listItem_label}>{data.label}</span>
          <span className={`${styles.listItem_value} ${styles.onClick}`} onClick={() => setModalIsShow(true)}>
            <PreloadImage src={isDarkMode ? next_dark : next} />
          </span>
        </div>
      </Form.Item>
      <Modal isShow={modalIsShow} onCancel={() => setModalIsShow(false)} content={options?.modalOptions?.content} {...options?.modalOptions} />
    </>
  )
}

// 开关类型Item
const SwitchItem = (props: IListItem) => {
  const { data, form } = props;
  const [value, setValue] = useState<boolean>(data.value ? true : false);

  const onChange = useCallback((checked: boolean) => {
    data.onCallback && data.onCallback(checked);
    setValue(checked);
    form.setFieldValue(data.key, checked);
  }, [data, form])

  useEffect(() => {
    if (data.onForm) {
      data.onForm(form);
    }
  }, [data, form])

  return (
    <Form.Item name={data.key} noStyle>
      <div className={styles.listItemContainer}>
        <span className={styles.listItem_label}>{data.label}</span>
        <Switch value={value} onChange={onChange} />
      </div>
    </Form.Item>
  )
}

interface IList {
  dataSource: IListData[],
  split?: boolean
  size?: 'default' | 'large' | 'small',
}

const List = (props: IList) => {
  const { dataSource, split = false, size } = props;
  const [form] = useForm();

  const renderItem = useCallback((item: IListData, index) => {
    if (item.render) {
      return (<AntdList.Item key={index}><Form.Item name={item.key} noStyle>
        <div className={styles.listItemContainer}>
          <span className={styles.listItem_label}>{item.label}</span>
          <span className={`${styles.listItem_value} ${styles.onClick}`}>
            {item.render(item.type, item.value, form)}
          </span>
        </div>
      </Form.Item></AntdList.Item>)
    }
    switch (item.type) {
      case 'switch':
        return (<AntdList.Item key={index}><SwitchItem data={item} form={form} /></AntdList.Item>)
      case 'text':
        return (<AntdList.Item key={index}><TextItem data={item} form={form} /></AntdList.Item>)
      case 'link':
        return (<AntdList.Item key={index}><LinkItem data={item} form={form} /></AntdList.Item>)
      case 'modal':
        return (<AntdList.Item key={index}><ModalItem data={item} form={form} /></AntdList.Item>)
      case 'input':
        return (<AntdList.Item key={index}><InputItem data={item} form={form} /></AntdList.Item>)
      case 'select':
        return (<AntdList.Item key={index}><SelectItem data={item} form={form} /></AntdList.Item>)
      default: return (
        <AntdList.Item key={index}><TextItem data={item} form={form} /></AntdList.Item>
      )
    }
  }, [form])

  return (
    <Form form={form}>
      <AntdList dataSource={dataSource} renderItem={renderItem} split={split} size={size} />
    </Form>
  )
}

export default List;