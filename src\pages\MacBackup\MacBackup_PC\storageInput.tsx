import { Form, FormInstance, Input, InputNumber, Select } from "antd";
import styles from './index.module.scss';
import { PreloadImage } from "@/components/Image";
import { useTheme } from "@/utils/themeDetector";
import { InputNumberRef } from "rc-input-number";
import { useEffect, useRef } from "react";

import clear_icon from '@/Resources/camMgmtImg/close.png';
import clear_icon_dark from '@/Resources/camMgmtImg/close-dark.png';

const StorageInput = (props: { form: FormInstance<any>, openEdit: () => void }) => {
  const { form, openEdit } = props;
  const { isDarkMode } = useTheme();

  const ref = useRef<InputNumberRef>(null);

  useEffect(() => {
    if (ref.current) {
      ref.current.focus({ cursor: 'all' });
    }
  }, [])

  return (
    <Form form={form} layout="vertical" className={styles.form_container} initialValues={{ size: 0, unit: 'GB' }}>
      <div className={styles.input_container}>
        <Form.Item label={'存储位置'} rules={[{ required: true }]} name={'location'}>
          <Input readOnly />
        </Form.Item>
        <span onClick={openEdit}>更改</span>
      </div>
      <Form.Item label={'容量限制'} className={styles.size_limit_container}>
        <Form.Item name={'size'} className={styles.number_input_container}>
          <InputNumber max={9999} ref={ref} autoComplete="off" type="tel" pattern="[0-9]*" inputMode="numeric" autoFocus={true}
            suffix={!isDarkMode ? <PreloadImage className={styles.number_input_img} src={clear_icon} alt="clear" />
              : <PreloadImage className={styles.number_input_img} src={clear_icon_dark} alt="clear" />} />
        </Form.Item>
        <Form.Item name={'unit'} className={styles.select_container}>
          <Select>
            <Select.Option value="GB">GB</Select.Option>
            <Select.Option value="TB">TB</Select.Option>
            {/* <Select.Option value="MB">MB</Select.Option> */}
          </Select>
        </Form.Item>
      </Form.Item>
    </Form>
  )
}

export default StorageInput;