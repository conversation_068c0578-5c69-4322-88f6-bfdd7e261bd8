.empty_state_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 40px;
  min-height: 400px;
}

.empty_state_icon {
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
}

.empty_state_icon img {
  width: 150px;
  height: 150px;
  opacity: 0.6;
}

.empty_state_title{
  font-family: MiSans W;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.5;
  color:#999999;
  margin: 0 0 12px 0;

}
.empty_state_content {
  max-width: 400px;
}

.empty_state_description {
  font-family: MiSans W;
  font-weight: 300;
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-color);
  margin: 0 0 25px 0;
  opacity: 0.6;
}

.empty_state_actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}
