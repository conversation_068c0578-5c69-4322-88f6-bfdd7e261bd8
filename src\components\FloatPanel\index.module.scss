.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, var(--overlay-opacity));
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-out;

  :global {
    .adm-floating-panel-header {
      background-color: var(--modal-content-background-color) !important;
    }
    .adm-floating-panel-bar {
      width: 60px !important;
    }
    .adm-floating-panel-content {
      width: 100%;
      height: calc(100% - var(--header-height)) !important;
      overflow-y: auto;
    }
  }
}

.overlay.fancy_exit {
  animation: fadeOut 0.3s ease-in forwards;
}

.overlay_content {
  width: 100%;
  height: 100%;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
