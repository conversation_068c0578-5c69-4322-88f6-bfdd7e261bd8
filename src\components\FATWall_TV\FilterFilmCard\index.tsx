import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import like_icon from '@/Resources/filmWall/like.png';
import placeholder‌_poster from '@/Resources/filmWall/placeholder‌_row.png';

interface IFilterFilmCard {
  title: string;
  subtitle: string;
  core: string;
  cover: string;
  isLike: boolean;
  isDrama?: boolean; // 是否为电视剧
  episodes?: number; // 总集数
  season_num?: string; // 季数
  onCardClick?: () => void;
}

const FilterFilmCard = (props: IFilterFilmCard) => {
  const { title, subtitle, core, cover, episodes, onCardClick, isLike } = props;
  return (
    <div className={styles.card_container} onClick={onCardClick}>
      <div className={styles.card_content}>
        <PreloadImage src={cover || placeholder‌_poster} alt='cover' />
        <div className={styles.card_core}>{core}</div>
        {
          isLike && (
            <div className={styles.card_isLike}>
              <PreloadImage src={like_icon} alt='like' />
            </div>
          )
        }
      </div>
      <div className={styles.card_footer}>
        <span className={styles.card_text}>{title}</span>
        <div className={styles.card_info}>
          {<span className={styles.episodes}>共{episodes}集·</span>}
          {subtitle && <span className={styles.episodes}>{subtitle}</span>}
        </div>
      </div>
    </div>
  )
}

export default FilterFilmCard;