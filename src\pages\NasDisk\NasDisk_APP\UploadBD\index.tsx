import React, { useState, useEffect, useCallback } from 'react';
import { Button, Toast, Loading } from 'antd-mobile';
import { useRequest } from 'ahooks';
import styles from './index.module.scss';
import NavigatorBar from '@/components/NavBar';
import { useHistory, useLocation } from 'react-router-dom';
import CreateFolder from '@/pages/NasDisk/NasDisk_APP/components/CreateFolder';
import { getBaiduNetdiskFileList, createBaiduNetdiskFolder } from '@/api/nasDisk';


// 内部使用的文件项接口
interface FileItem {
  fs_id: number;
  name: string;
  path: string;
  type: 'folder' | 'file';
  time: string;
  itemCount?: number;
  size: number;
  isdir: boolean;
}

interface LocationState {
  selectedFolders?: string[];
  existingTaskCount?: number;
  existingFolderPaths?: string[];
}

const UploadBD: React.FC = () => {
  const history = useHistory();
  const location = useLocation<LocationState>();
  
  // 文件列表
  const [fileList, setFileList] = useState<FileItem[]>([]);
  
  // 选中的文件夹路径
  const [, setSelectedFolder] = useState<string>('');
  
  // 当前路径面包屑
  const [breadcrumbPath, setBreadcrumbPath] = useState<string[]>(['百度网盘']);
  
  // 当前路径
  const [currentPath, setCurrentPath] = useState<string>('/');
  
  // 新建文件夹弹窗状态
  const [showCreateFolderModal, setShowCreateFolderModal] = useState<boolean>(false);
  
  // 使用 useRequest 获取文件列表
  const { run: fetchFileList, loading } = useRequest(
    (params: { path: string }) => {
      // 调用百度网盘接口
      return getBaiduNetdiskFileList({
        action: "remotelist",
        path: params.path,
        order: 'name',
        desc: 1
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response && response.errno === 0) {
          // 只显示文件夹
          const folders: FileItem[] = response.list
            .filter((item: any) => item.isdir === 1)
            .map((item: any) => ({
              fs_id: item.fs_id,
              name: item.server_filename,
              path: item.path,
              type: "folder" as const,
              time: formatTime(item.server_mtime),
              itemCount: Math.floor(Math.random() * 50) + 1, // 模拟项目数量
              size: item.size,
              isdir: true,
            }));

          setFileList(folders);
        } else {
          console.error("获取文件列表失败:", response);
          setFileList([]);
          Toast.show({
            content: '获取文件列表失败，请重试',
            duration: 2000,
          });
        }
      },
      onError: (error) => {
        console.error("加载文件列表出错:", error);
        setFileList([]);
        Toast.show({
          content: '获取文件列表失败，请重试',
          duration: 2000,
        });
      },
    }
  );

  // 创建文件夹请求
  const { run: runCreateFolder } = useRequest(
    (params: { path: string }) => {
      return createBaiduNetdiskFolder({
        action: "createdir",
        path: params.path
      });
      },
      {
      manual: true,
      onSuccess: (response) => {
        if (response.errno === 0 || response.code === 0) {
          Toast.show({
            content: "创建文件夹成功",
            duration: 2000,
          });
          handleCreateFolderSuccess();
        } else {
          Toast.show({
            content: response.errmsg || "创建文件夹失败，请重试",
            duration: 2000,
          });
        }
      },
      onError: (error) => {
        console.error("创建文件夹失败：", error);
        Toast.show({
          content: "创建文件夹失败，请重试",
          duration: 2000,
        });
      }
    }
  );

  // 格式化时间戳
  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/\//g, '/');
  };

  // 初始化时获取根目录
  useEffect(() => {
    fetchFileList({ path: '/' });
  }, [fetchFileList]);

  // 处理文件夹点击（直接进入文件夹）
  const handleFolderClick = useCallback((folder: FileItem) => {
    // 构建新路径
    const newPath = currentPath === '/' ? `/${folder.name}` : `${currentPath}/${folder.name}`;
    
    // 更新导航和路径
    setCurrentPath(newPath);
    setBreadcrumbPath([...breadcrumbPath, folder.name]);
    
    // 清空选中状态
    setSelectedFolder('');
    
    // 获取子文件夹内容
    fetchFileList({ path: newPath });
  }, [currentPath, breadcrumbPath, fetchFileList]);

  // 处理面包屑导航点击
  const handleBreadcrumbClick = useCallback((index: number) => {
    if (index === 0) {
      // 点击根目录
      setCurrentPath('/');
      setBreadcrumbPath(['百度网盘']);
      setSelectedFolder('');
      fetchFileList({ path: '/' });
    } else {
      // 点击中间路径
      const newPath = breadcrumbPath.slice(0, index + 1);
      setBreadcrumbPath(newPath);
      
      // 构建路径
      const pathToFetch = '/' + newPath.slice(1).join('/');
      setCurrentPath(pathToFetch);
      setSelectedFolder('');
      
      // 获取该路径下的内容
      fetchFileList({ path: pathToFetch });
    }
  }, [breadcrumbPath, fetchFileList]);

  // 处理返回
  const handleBack = () => {
    history.goBack();
  };

  // 处理新建文件夹
  const handleCreateFolder = () => {
    setShowCreateFolderModal(true);
  };

  // 处理创建文件夹成功
  const handleCreateFolderSuccess = () => {
    setShowCreateFolderModal(false);
    // 刷新当前目录
    fetchFileList({ path: currentPath });
  };

  // 处理创建文件夹取消
  const handleCreateFolderCancel = () => {
    setShowCreateFolderModal(false);
  };

  // 创建新文件夹
  const createFolder = (folderName?: string) => {
    if (!folderName) {
      Toast.show({
        content: "请输入文件夹名称",
        duration: 2000,
      });
      return;
    }
    
    // 构建完整路径
    const newFolderPath = currentPath === '/' 
      ? `/${folderName}` 
      : `${currentPath}/${folderName}`;
    
    // 调用创建文件夹接口
    runCreateFolder({ path: newFolderPath });
  };

  // 处理确定按钮
  const handleConfirm = () => {
    // 使用当前路径作为选择路径
    const uploadPath = currentPath;
    const displayPath = breadcrumbPath.join(' > ');
    
    // 返回到Synchronization页面，传递选择的路径信息和之前的选中状态
    history.push({
      pathname: '/baiduNetdisk_app/synchronization',
      state: {
        selectedUploadPath: uploadPath,
        selectedDisplayPath: displayPath,
        selectedFolders: location.state?.selectedFolders || [],
        existingTaskCount: location.state?.existingTaskCount || 0,
        existingFolderPaths: location.state?.existingFolderPaths || []
      }
    });
  };

  return (
    <div className={styles.uploadBDContainer}>
      <div className={styles.fixedHeader}>
        <NavigatorBar onBack={handleBack} />
        <div className={styles.title}>更改自动下载位置</div>
        {/* 面包屑导航 */}
        <div className={styles.breadcrumb}>
          {breadcrumbPath.map((item, index) => (
            <React.Fragment key={index}>
              <span 
                className={`${styles.breadcrumbItem} ${index === breadcrumbPath.length - 1 ? styles.active : ''}`}
                onClick={() => handleBreadcrumbClick(index)}
              >
                {item}
              </span>
              {index < breadcrumbPath.length - 1 && (
                <span className={styles.breadcrumbSeparator}>&gt;</span>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      <div className={styles.scrollableContent}>
        {/* 加载状态 */}
        {loading && (
          <div className={styles.loadingContainer}>
            <Loading />
            <span>加载中...</span>
          </div>
        )}
        
        {/* 文件列表 */}
        <div className={styles.fileList}>
          {fileList.map(folder => (
            <div 
              key={folder.fs_id}
              className={styles.fileItem}
              onClick={() => handleFolderClick(folder)}
            >
              <div className={styles.fileIcon}>
                <div className={styles.folderIcon} />
              </div>
              <div className={styles.fileInfo}>
                <div className={styles.fileName}>
                  {folder.name}
                </div>
                <div className={styles.fileDetails}>
                  {folder.time} | {folder.itemCount}项
                </div>
              </div>
            </div>
          ))}
          
          {!loading && fileList.length === 0 && (
            <div className={styles.emptyState}>
              <span>该目录下没有文件夹</span>
            </div>
          )}
        </div>
      </div>

      {/* 底部按钮 */}
      <div className={styles.footer}>
        <Button 
          className={styles.leftBut}
          onClick={handleCreateFolder}
        >
          + 新建文件夹
        </Button>
        <Button 
          className={styles.rightBut}
          onClick={handleConfirm}
        >
          确定
        </Button>
      </div>

      {/* 新建文件夹弹窗 */}
      <CreateFolder
        visible={showCreateFolderModal}
        onCancel={handleCreateFolderCancel}
        onSuccess={createFolder}
        currentPath={currentPath}
      />
    </div>
  );
};

export default UploadBD;
