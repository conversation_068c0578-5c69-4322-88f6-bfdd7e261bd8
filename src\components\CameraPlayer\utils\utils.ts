import { useCallback, useEffect, useRef, useState } from "react";
import { useDebounceFn } from "ahooks";

type DebounceOptions = {
  leading?: boolean;      // 是否立即执行首次调用
  trailing?: boolean;     // 是否在延迟结束后执行
  maxWait?: number;       // 最大等待时间（类似节流）
};

export const useDebounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number = 150,
  options?: DebounceOptions
) => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastCallTime = useRef<number>();
  const lastArgsRef = useRef<Parameters<T>>();

  const debounced = useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    const isLeading = options?.leading && !timeoutRef.current;
    const maxWait = options?.maxWait ?? 0;

    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }

    // 处理最大等待时间
    if (maxWait > 0 && lastCallTime.current && now - lastCallTime.current >= maxWait) {
      lastCallTime.current = now;
      func(...args);
      return;
    }

    // 保存参数
    lastArgsRef.current = args;

    if (isLeading) {
      func(...args);
    }

    timeoutRef.current = setTimeout(() => {
      if (options?.trailing !== false && lastArgsRef.current) {
        func(...lastArgsRef.current);
      }
      timeoutRef.current = undefined;
      lastArgsRef.current = undefined;
    }, wait);

    lastCallTime.current = now;
  }, [func, wait, options]);

  // 组件卸载时清理
  const cancel = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
    lastArgsRef.current = undefined;
  }, []);

  return [debounced, cancel] as const;
};

export const useContainerWidth = (options?: {
  debounce?: number;      // 防抖延迟时间
  maxWait?: number;      // 最大等待时间
  pause?: boolean
}) => {
  const [width, setWidth] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<ResizeObserver>();
  const isMounted = useRef(true);

  // 防抖更新宽度函数
  const { run, cancel } = useDebounceFn((newWidth: number) => {
    if (isMounted.current) {
      setWidth(newWidth);
    }
  }, { maxWait: options?.maxWait, wait: options?.debounce || 1000 });

  const handleResize = useCallback((entries: ResizeObserverEntry[]) => {
    const entry = entries[0];
    if (!entry?.contentRect) return;

    // 直接获取元素最新尺寸（避免使用已过时的 entry）
    const currentWidth = containerRef.current?.offsetWidth || 0;
    run(currentWidth);
  }, [run]);

  // 新增强制暂停方法
  const forcePause = useCallback(() => {
    if (containerRef.current) {
      if (observerRef.current) {
        observerRef.current?.unobserve(containerRef.current);
        observerRef.current?.disconnect();
      }
    }
    cancel();
    return;
  }, [cancel])

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
      cancel();
    };
  }, [cancel]);

  useEffect(() => {
    if (options?.pause) {
      if (containerRef.current) {
        if (observerRef.current) observerRef.current?.unobserve(containerRef.current);
      }
      cancel();
      return;
    }
    const container = containerRef.current;
    if (!container) return;
    
    try {
      // 立即获取初始宽度
      const initialWidth = container.offsetWidth;
      setWidth(initialWidth);

      // 初始化观察器
      observerRef.current = new ResizeObserver(handleResize);
      observerRef.current.observe(container);
    } catch (e) {
      console.log(e);
    }

    return () => {
      observerRef.current?.unobserve(container);
      cancel();
    };
  }, [cancel, handleResize, options?.pause]);

  return {
    containerRef,
    width,
    cancelResizeObserver: cancel,
    forcePause
  };
};