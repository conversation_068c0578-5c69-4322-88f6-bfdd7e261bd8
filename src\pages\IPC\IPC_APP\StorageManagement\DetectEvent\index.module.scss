.container {
  background-color: var(--home-background-color);
  // padding: 10px;
  min-height: calc(100vh - 35px);

  :global {
    .adm-list {
      --border-inner: none;
      --border-top: none;
      .adm-list-item-content-main {
        padding: 0;
      }
    }
    .adm-list-default .adm-list-body {
      border-bottom: none;
    }
    .adm-list-body {
      background-color: transparent;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
  }

  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px 24px;
  }

  .rish {
    font-size: 12px;
    color: #8c93b0;
    padding: 10px 26px;
  }

  .eventList {
    padding: 0 16px;
    :global {
      .adm-switch.adm-switch-checked .adm-switch-checkbox {
        background-color: var(--primary-color);
      }
      a.adm-list-item:active:not(.adm-list-item-disabled){
        background-color: var(--card-background-color);
      }
    }
  }

  .eventItemWrapper {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: var(--card-background-color);
    border-radius: 16px;
    margin-bottom: 12px;
  }

  .eventIcon {
    width: 40px;
    height: 40px;
    margin-right: 14px;
  }

  .eventContent {
    flex: 1;
  }

  .sectionTitle {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-color);
  }

  .eventDesc {
    font-size: 13px;
    color: var(--list-value-text-color);
  }

  .switch {
    margin-left: 16px;
  }

  .eventItem {
    --padding-left: 0;
    --padding-right: 0;
    margin-bottom: 10px;
    border-radius: 0 0 16px 16px;
    background-color: var(--card-background-color);


    .itemContent {
      display: flex;
      justify-content: space-between;
      padding: 16px 36px 16px 16px;

      .itemBox {
        display: flex;
        align-items: center;
      }

      .itemLabel {
        font-size: 16px;
        color: var(--text-color);
      }

      .itemValue {
        font-size: 13px;
        color: var(--list-value-text-color);
        margin-right: 8px;
      }
    }

    .arrow {
      width: 18px;
      height: 18px;
    }
  }
}
.popoverContent {
  max-width: 240px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px 0;
  background-color: var(--home-background-color);

  border-radius: 8px;

  .optionItem {
    display: flex;
    align-items: center;
    padding: 12px 16px;

    &:active {
      background-color: #f5f5f5;
    }

    .optionRadio {
      --icon-size: 18px;
      --font-size: 16px;
      --gap: 12px;
    }

    .optionLabel {
      font-size: 16px;
      color: var(--text-color);
      margin-left: 12px;
    }
  }
}
