/**
 * common utils part,please add more with mark details
 */

export default class CommonUtils {

  /**
   * 是否为空
   * @param text
   * @returns {boolean}
   */
  static isTextEmpty(text) {
    // text 有可能不是支付串，所以加了try
    try {
      if (text === undefined || text === null || text.trim() === "") {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /**
   * obj是否为空
   * @param object
   * @returns {boolean}
   */
  static isNull(object) {
    if (object === undefined || object === null) {
      return true;
    }
    return false;
  }

  static arrayIsEmpty(array) {
    if (this.isNull(array) || !Array.isArray(array) || array.length === 0) {
      return true;
    }
    return false;
  }

  static arrayPushWithoutEmpty(item, array) {
    if (!this.isTextEmpty(item)) {
      array.push(item);
    }
    return array;
  }

  /**
   * get array[] position
   * //注意: 此方法返回提供的数据在数组中的位置，如果不存在返回位置为 -1 ，不可以修改默认返回最后一位；
   * 由于Wi-Fi/QS等模块早期开发首次建立此方法并大量使用了此方法，后面开发请勿直接更改此方法逻辑，可以从新定义或者合理扩展。
   * 已将此方法修改还原：（不支持默认返回最后一个位置了），已定义新方法件见：getArrayPositionDefaultBackLast,Mesh自定义位置已重新引用此方法。
   * @param array[]
   * @returns {int}
   */
  static getArrayPosition(array, arr) {
    let position = -1;
    if (this.arrayIsEmpty(array)) {
      position = -1;
    }
    for (let i = 0; i < array.length; i++) {
      if (array[i] == arr) {
        position = i;
      }
    }
    return position;
  }

  /**
     * get array[] position
     * @param array[]
     * @returns {int}
     */
  static getArrayPositionDefaultBackLast(array, arr) {
    let position = -1;
    if (this.arrayIsEmpty(array)) {
      position = -1;
    }
    let find = false;
    for (let i = 0; i < array.length; i++) {
      if (array[i] == arr) {
        position = i;
        find = true;
      }
    }
    if (!find) {
      position = array.length - 1;
    }
    return position;
  }


  /**
   * 例如:2019-11-08 10:48:46转成date类,
   * 可把- replace成/
   * @param dateString
   * @return Date
   */
  static parserDateString(dateString) {
    if (dateString) {
      let regEx = new RegExp("\\-", "gi");
      let validDateStr = dateString.replace(regEx, "/");
      let milliseconds = Date.parse(validDateStr);
      return new Date(milliseconds);
    }
  }

  static formatToString = (timestamp, formater) => {
    let date = new Date();
    date.setTime(parseInt(timestamp));
    formater = (formater != null) ? formater : 'yyyy-MM-dd hh:mm';
    Date.prototype.Format = function(fmt) {
      let o = {
        "M+": this.getMonth() + 1,
        "d+": this.getDate(),
        "h+": this.getHours(),
        "m+": this.getMinutes(),
        "s+": this.getSeconds(), // 秒
        "q+": Math.floor((this.getMonth() + 3) / 3),
        "S": this.getMilliseconds()
      };

      if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (`${ this.getFullYear() }`).substr(4 - RegExp.$1.length));
      for (let k in o) {
        if (new RegExp(`(${ k })`).test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ?
          (o[k]) : ((`00${ o[k] }`).substr((`${ o[k] }`).length)));
      }
      return fmt;
    };
    return date.Format(formater);
  };

  /**
 * 例如:2019-11-08 10:48:46转成date类,
 * 可把- replace成/
 * @param dateString
 * @return Date
 */
  static parserDateString(dateString) {
    if (dateString) {
      let regEx = new RegExp("\\-", "gi");
      let validDateStr = dateString.replace(regEx, "/");
      let milliseconds = Date.parse(validDateStr);
      return new Date(milliseconds);
    }
  }

  /**
   * 获取当前时间的字符串
   * @param {时间戳}
   */
  static getCurrentTimeStr() {
    let date = new Date();
    let y = date.getFullYear();
    let m = date.getMonth() + 1;
    m = m < 10 ? (`0${ m }`) : m;
    let d = date.getDate();
    d = d < 10 ? (`0${ d }`) : d;
    let h = date.getHours();
    h = h < 10 ? (`0${ h }`) : h;
    let minute = date.getMinutes();
    let second = date.getSeconds();
    minute = minute < 10 ? (`0${ minute }`) : minute;
    second = second < 10 ? (`0${ second }`) : second;
    return `${ y }${ m }${ d }${ h }${ minute }${ second }`;
  }

  /**
   * 判断是否是表情符号
   * @param {value值}
   */
  static isEmojiCharacterInString(substring) {
    for (let i = 0; i < substring.length; i++) {
      let hs = substring.charCodeAt(i);
      if (0xd800 <= hs && hs <= 0xdbff) {
        if (substring.length > 1) {
          var ls = substring.charCodeAt(i + 1);
          let uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
          if (0x1d000 <= uc && uc <= 0x1f77f) {
            return true;
          }
        }
      } else if (substring.length > 1) {
        var ls = substring.charCodeAt(i + 1);
        if (ls == 0x20e3) {
          return true;
        }
      } else {
        if (0x2100 <= hs && hs <= 0x27ff) {
          return true;
        } else if (0x2B05 <= hs && hs <= 0x2b07) {
          return true;
        } else if (0x2934 <= hs && hs <= 0x2935) {
          return true;
        } else if (0x3297 <= hs && hs <= 0x3299) {
          return true;
        } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d || hs == 0x3030
          || hs == 0x2b55 || hs == 0x2b1c || hs == 0x2b1b
          || hs == 0x2b50) {
          return true;
        }
      }
    }
  }

  /**
   * 判断是否是合法的ip
   * @param {ip}
   * @returns {boolean}
   */
  static isValidIp(ip) {
    let reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    return reg.test(ip);
  }

  /**
   * show toast
   * @param string bottom
   *
   */
  
  /**
   * 判断object是否是数组
   * @param {object}
   * @returns {boolean}
   */
  static isObjectKindofArray(object) {
    return Object.prototype.toString.call(object) === '[object Array]';
  }

  static transformSpeedData(speed) {
    let response = {
      transformedSpeed: '0',
      transformedUnit: 'KB/s'
    };
    if (speed >= 0 && speed < 1000000) {
      response.transformedSpeed = (speed / 1000).toFixed(0);
      response.transformedUnit = 'KB/S';
    }
    if (speed >= 1000000) {
      response.transformedSpeed = (speed / 1000000).toFixed(0);
      response.transformedUnit = 'MB/S';
    }
    return response;
  }

  static formatBytes(bytes) {
    if (bytes === 0) return '0 B/s';
    let k = 1000, // or 1024
      sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
      i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${ (bytes / Math.pow(k, i)).toPrecision(3) } ${ sizes[i] }`;
  }
 
  /**
   * 获取浮点数，四舍五入
   * @param {*} num 要转换的数
   * @param {*} n 保留几位小数（不够0补位）
   */
  static getFloat(num, n) {
    n = n ? parseInt(n) : 0;
    if (n <= 0) {
      return Math.round(num);
    }
    num = Math.round(num * Math.pow(10, n)) / Math.pow(10, n);
    num = Number(num).toFixed(n);
    return num;
  }

  static isNonPositiveBand(band) {
    return band < 0.01;
  }

  static isOutofMaxRouterBand(band) {
    return band > 10000;
  }

  static isOutofMaxDeviceBand(band) {
    return band > 1000 * 1000;
  }

  static isOutofMaxDownloadBand(band) {
    return band > 10000;
  }

  static isNonPositiveDownloadBand(band) {
    return band < 1 << 3;
  }

  
  static isValidDomain(url) {
    const reg = /^[A-Za-z0-9]+(.[A-Za-z0-9-:;\?#_]+)+$/;
    return reg.test(url);
  }

  static removeContainString(itemString, containDtring) {

    if (itemString.indexOf(containDtring) > -1) {
      return itemString.split(containDtring).join("");
    } else {
      return itemString;
    }
  }

  static isMathForNumber(value) {
    let reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if (reg.test(value)) {
      return true;
    } else {
      return false;
    }
  }

  static isContainSomeString(value, target) {
    if (typeof value != 'string') {
      value = String(value);
    }
    return value.indexOf(target) != -1;
  }

  static isValidMac(mac) {
    let expre = /[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}/;
    let regexp = new RegExp(expre);
    if (!regexp.test(mac.toUpperCase())) {
      return false;
    }
    return true;
  }

static formatFileSize = (fileSize) => {
  if (fileSize < 1024) {
    return `${ fileSize }B`;
  } else if (fileSize < (1024 * 1024)) {
    var temp = fileSize / 1024;
    temp = temp.toFixed(2);
    return `${ temp }KB`;
  } else if (fileSize < (1024 * 1024 * 1024)) {
    var temp = fileSize / (1024 * 1024);
    temp = temp.toFixed(2);
    return `${ temp }MB`;
  } else {
    var temp = fileSize / (1024 * 1024 * 1024);
    temp = temp.toFixed(2);
    return `${ temp }GB`;
  }
}

static isContainSomeString(value, target) {
  if (typeof value != 'string') {
    value = String(value);
  }
  return value.indexOf(target) != -1;
}

static isValidMac(mac) {
  let expre = /[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}/;
  let regexp = new RegExp(expre);
  if (!regexp.test(mac.toUpperCase())) {
    return false;
  }
  return true;
}


static isUrl(str) {
  let v = new RegExp('^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$', 'i');
  return v.test(str);
}
// 磁力链接校验
static isMagnet(str) {
  let v = new RegExp(/^(magnet:\?xt=urn:btih:)[0-9a-fA-F]{40}.*$/);
  return v.test(str);
}

static isValidIPv6Address = (ip) => {
  const ipv6Regex = /^((?!(ff02::|fe80::)))(([a-fA-F0-9]{1,4}(:[a-fA-F0-9]{1,4}){7})|([a-fA-F0-9]{1,4}(:[a-fA-F0-9]{1,4}){0,7}::[a-fA-F0-9]{0,4}(:[a-fA-F0-9]{1,4}){0,7}))/;
  return ipv6Regex.test(ip);
}

static isValidDomainRegex = (v) => {
  const domainRegex = /^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)*\.[a-zA-Z]{2,6}$/;
  return domainRegex.test(v);
  }

//分割字符串+单位，例如50GB=>50 GB  
static splitNumberAndUnit(str) {
    const match = str.match(/^(\d+\.?\d*)(\D+)$/);
    if (!match) {
      throw new Error("无效的格式");
    }
    return {
      number: parseFloat(match[1]), // 转换为浮点数
      unit: match[2]
    };
  }

  /**
   * 格式化文件路径，将系统路径转换为用户友好的显示格式
   * @param {string} filePath - 原始文件路径
   * @returns {string} 格式化后的路径
   */
static formatFilePath(filePath) {
    if (!filePath) {
      return '未知路径';
    }
    
    // 使用正则表达式匹配 /home/<USER>/pool0/data 部分
    const pattern = /\/home\/u\d+\/pool0\/data/i;
    const match = filePath.match(pattern);
    
    if (match) {
      // 找到匹配的部分并替换为"存储空间"
      return filePath.replace(match[0], '存储空间');
    }
    
    return filePath;
}


/**
   * 格式化时间戳为指定格式的日期字符串
   * @param {number} timestamp - 时间戳（毫秒）
   * @param {string} [format='YYYY-MM-DD'] - 日期格式
   * @returns {string} 格式化后的日期字符串
   */
  static formatTimestamp(timestamp, format = 'YYYY-MM-DD') {
    if (!timestamp) return '无效时间';
    
    // 自动检测时间戳是秒还是毫秒（10位秒级，13位毫秒级）
    const adjustedTimestamp = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp;
    
    const date = new Date(adjustedTimestamp);
    
    if (isNaN(date.getTime())) return '无效时间';
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return format
      .replace(/YYYY/g, year)
      .replace(/MM/g, month)
      .replace(/DD/g, day)
      .replace(/HH/g, hours)
      .replace(/mm/g, minutes)
      .replace(/ss/g, seconds);
  }
}
