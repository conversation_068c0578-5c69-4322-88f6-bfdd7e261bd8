// 媒体库管理页面样式

.container {
  width: 100%;
  padding: 20px 30px;
  background-color: var(--background-color);
  box-sizing: border-box;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color, #000);
}

.addButton {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--library-switch-bg);
  width: 140px;
  height: 40px;
  background-color: var(--background-color);
  padding: 8px 16px;
  border-radius: 8px;

  &:hover {
    opacity: 0.9;
  }


  .addIcon {
    color: #000000;
    font-size: 14px;
    margin-right: 8px;
  }

  .addText {
    color: var(--add-lib-text-color);
    font-size: 14px;
    font-weight: 500;
  }
}

.refreshBtn {
  height: 40px;
  width: 40px;

  img {
    width: 40px;
    height: 40px;
  }
}

.section {
  margin-bottom: 40px;
}

.sectionTitle {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  color: var(--text-color, #000);
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color, #eee);
}

// 表格样式
.tableContainer {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  background-color: var(--card-background-color);
}

.tableHeader {
  display: flex;
  background-color: var(--background-color);
  padding: 12px 16px;
  font-weight: 500;
}

.headerCell {
  flex: 1;
  text-align: center;
  color: #8C93B0;
  overflow: hidden;

  &:first-child {
    flex: 0 0 120px;
    text-align: left;
  }
}

.tableBody {
  display: flex;
  flex-direction: column;
}

.tableRow {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.3s;
  background-color: var(--background-color);

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: var(--table-hover-bg);
  }
}

// 媒体库列
.mediaCell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--user-selector-checkbox-bg);
  border-radius: 10px;
  position: relative;
}

// 修改媒体库列的图片展示样式
.coverGroup {
  display: flex;
  width: 100%;
  height: 90px;
  position: relative;
  margin: 0 auto;
}

// 空状态
.coverGroupEmpty {
  justify-content: center;
  align-items: center;
}

// 单图状态
.coverGroupSingle {
  justify-content: center;

  .coverItem {
    position: static !important;
    transform: none !important;
    width: 120px !important;
    margin: 0 auto;
  }
}

// 双图状态
.coverGroupDouble {
  justify-content: center;

  .coverItem1 {
    position: static !important;
    transform: none !important;
    margin-right: 5px;
    width: 80px !important;
  }

  .coverItem2 {
    position: static !important;
    transform: none !important;
    margin-left: 5px;
    width: 80px !important;
  }
}

// 三图状态
.coverGroupTriple {
  .coverItem {
    position: absolute;
    width: 75px !important;
  }

  .coverItem1 {
    left: 0;
    z-index: 3;
  }

  .coverItem2 {
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
  }

  .coverItem3 {
    right: 0;
    z-index: 1;
  }
}

.coverItem {
  height: 90px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.noCover {
  width: 120px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #eee;
  color: #999;
  font-size: 14px;
  border-radius: 4px;
}

.mediaTitle {
  font-weight: 400;
  white-space: nowrap;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-color)
}

// 来源列
.sourceCell {
  flex: 1;
  font-size: 12px;
  color: var(--text-color);
  font-weight: 400;
  overflow: hidden;
  text-align: center;
}

.pathsList {
  display: flex;
  flex-direction: column;
}

.pathItem {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 更新时间列
.updateTimeCell {
  flex: 1;
  text-align: center;
  color: var(--list-value-text-color);
  font-weight: 400;
  font-size: 12px;
}

// 操作列
.actionCell {
  flex: 1;
  display: flex;
  justify-content: center;
}

.actionButtons {
  display: flex;
  gap: 10px;
}

.actionBtn {
  width: 32px;
  height: 32px;
  background-color: var(--card-secondary-background-color, rgba(0, 0, 0, 0.03));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;

  img {
    width: 16px;
    height: 16px;
  }

  &:hover {
    background-color: var(--primary-color, #4F65F6);

    img {
      filter: brightness(10);
    }
  }
}

// 创建媒体库弹窗
.modal {

  :global(.ant-modal-content) {
    border-radius: 32px;
    width: 546px;
    overflow: hidden;
    background-color: var(--desktop-modal-bg-color);
    height: 636px;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  :global(.ant-modal-header) {
    text-align: center;
    background-color: var(--desktop-modal-bg-color);
  }

  :global(.ant-modal-body) {
    position: relative;
    height: 80%;
  }

  :global(.ant-modal-close) {
    left: 15px;
  }

  :global(.ant-modal-title) {
    color: var(--text-color);
  }

  :global(.ant-btn) {
    position: relative;
    color: #ffffff;
    background-color: var(--primary-color);
    border: none;
  }

  :global(.ant-divider-horizontal) {
    background-color: var(--card-background-color);
  }

  :global(.ant-btn-variant-outlined:not(:disabled):hover) {
    opacity: 0.7;
    background:var(--primary-color);
  }
}

// CreateLibraryModal 布局样式
.modalContainer {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.scrollableFormContent {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

.fixedFooter {
  background-color: var(--desktop-modal-bg-color);
  display: flex;
  margin-top: -20px;
  margin-bottom: 40px;
  // height: 60px;
  justify-content: center;
  padding: 0px 0px 0px 0px;
}

.switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-color);
  background-color: var(--desktop-modal-bg-color);
  // padding: 20px 20px;
  border-radius: 10px;

  :global {
    .ant-switch {
      background-color: var(--library-switch-bg);
    }

    .ant-switch-checked {
      background-color: var(--primary-color);
    }
  }
}

// 选中用户样式
.selectedUser {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0px;
  border-radius: 8px;
  background-color: var(--desktop-modal-bg-color);
}

.userInfo {
  display: flex;
  align-items: center;
  background-color: var(--desktop-modal-bg-color);
  flex: 1;
}

.avatar {
  margin-right: 12px;
  border-radius: 50%;
}

.userDetails {
  flex: 1;
}

.userName {
  font-size: 17px;
  font-weight: 500;
  color: var(--text-color);
}

.userPosition {
  font-size: 12px;
  color: var(--library-title-color);
  font-weight: 400;
}

.removeButton {
  color: #ff4d4f;

  &:hover {
    color: #ff7875;
    background-color: transparent;
  }
}


.commitBtn {
  display: flex;
  margin: 0 auto;
  width: 336px;
  height: 50px;
  border-radius: 16px;
  margin-top: 50px;

  &:disabled {
    opacity: 0.5;
  }

  :global {
    .ant-btn {
      &:hover {
        background: #000;
      }
    }
  }
}

// 删除确认弹窗样式
.deleteModal {
  :global(.ant-modal-content) {

    border-radius: 32px;
    overflow: hidden;
    height: 206px;
    width: 368px;
    background-color: var(--background-color);
  }

  :global(.ant-modal-header) {
    text-align: center;
    background-color: var(--background-color);


    :global(.ant-modal-title) {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-color);
    }
  }

  :global(.ant-modal-close) {
    display: none;
  }
}

.deleteModalContent {
  padding: 0px 30px 24px;
}

.deleteWarning {
  font-size: 14px;
  color: var(--library-title-color);
  margin-bottom: 30px;
  line-height: 1.5;
}

.deleteModalButtons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.cancelBtn {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  border: none;
  background-color: var(--user-selector-checkbox-bg);
  color: var(--text-color);
  font-size: 17px;

  &:hover {
    border-color: #40a9ff;
    color: #40a9ff;
  }
}

.confirmBtn {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  border: none;
  background-color: #3482FF;
  color: #fff;
  font-size: 17px;

  &:hover {
    background-color: #3651E6;
  }
}

// 扫描进度遮罩层样式
.scanOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  z-index: 10;
}

.progressContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #fff;
}

.progressText {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.progressBar {
  width: 100px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background-color: #32BAC0;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.limitModal {
  // border-radius: 32px;

  :global(.ant-modal-content) {
    border-radius: 32px;
    background-color: var(--background-color);
  }
}

.createLibraryButton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--desktop-modal-bg-color);
  font-family: MiSans;
  font-weight: 500;
  border-radius: 16px;
  padding: 12px 0px;
  margin-bottom: 12px;
  color: var(--text-color);
  font-size: 17px;
}

.selectedSource {
  padding: 8px 0px;
  border-radius: 14px;
  margin-top: 8px;
  display: flex;

  color: var(--text-color);
  justify-content: space-between;
  align-items: center;
  background-color: var(--desktop-modal-bg-color);
}