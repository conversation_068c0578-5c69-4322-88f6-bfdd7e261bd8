import React, { MutableRefObject, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import './TimeAxis.css';
import leftSeek from "@/Resources/timeAxis/timeAxisLeftSeek.png";
import leftSeek_dark from "@/Resources/timeAxis/timeAxisLeftSeek_dark.png";
import rightSeek from "@/Resources/timeAxis/timeAxisRightSeek.png";
import rightSeek_dark from "@/Resources/timeAxis/timeAxisRightSeek_dark.png";
import { DeviceType, getDeviceType, getSystemType } from '@/utils/DeviceType';
import { PreloadImage } from '@/components/Image';
import { addDays, set, subDays } from 'date-fns';
import Player from 'xgplayer/es/player';
import { IEventVideo } from '@/pages/IPC/IPC_APP/CameraDetail';
import { startLiveWithCamera } from '@/api/ipc';
import { splitURL } from '../MonitorPlayer/MonitorPlayer';
import { Toast } from '@/components/Toast/manager';


export const px2rem = (px: number) => {
  const deviceType = getDeviceType();
  const rootValue = 39.2;
  const clientWidth = document.documentElement.clientWidth;
  let designWidth = 0;
  switch (deviceType) {
    case DeviceType.Mobile:
      designWidth = 392; // 手机设计稿宽度
      break;
    case DeviceType.Desktop:
      designWidth = 1234; // PC设计稿宽度
      break;
    case DeviceType.TV:
      designWidth = 1920; // TV设计稿宽度
      break;
    default:
      designWidth = 392; // 默认手机设计稿宽度
      break;
  }
  const rootFontSize = (rootValue * (clientWidth / designWidth));

  if (deviceType === DeviceType.Desktop) {
    // 如果是PC端，锁定字体大小，防止缩放
    return `${px / rootValue}rem`;
  }

  return (px / rootFontSize) + "rem";
}

const totalHours = 24;
const marksSecond = 1800;

// 秒的步阶为180 / (secondsPerPixel * viewGrid)
const secondsPerPixel = 10; // 控制时间轴密度
const viewGrid = 9 // viewGrid和secondsPerPixel相乘为半小时的间距，相当于 视窗 / viewGrid * secondsPerPixel = 半小时个数

// 半小时对应的秒数为1800s,secondsPerPixel*viewGrid为半点的间距,而viewGrid = 1800 / secondsPerPixel * secondsPerPixel
const pow = marksSecond / Math.pow(secondsPerPixel, 2) / viewGrid;

const genTimeMarkers = (baseDate: Date) => {
  // 创建基准时间零点（保留日期，时间归零）
  const base = new Date(baseDate);
  base.setHours(0, 0, 0, 0);

  return Array.from({ length: totalHours * 2 }, (_, i) => {
    const hours = Math.floor(i / 2);
    const date = new Date(baseDate);
    date.setHours(hours, (i % 2) * 30);
    return {
      time: date,
      formatted: `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`,
      isMajor: date.getMinutes() === 0
    };
  });
};

const blockWidth = totalHours * 2 * viewGrid * secondsPerPixel;
const isIos = getSystemType() === 'ios';

export interface IEventBlock { camera_lens: string, event_name: string, day: number, start: number, end: number, color: string, file: string, time: string, id: string };
interface ITimeAxis {
  deviceType: 'pc' | 'mobile';
  isFull: boolean
  type: 'Live' | 'Movie';
  setCurrent?: (v: any) => void;
  current?: Date
  currentWidth?: number;
  currentHeight?: number;
  events: { // 事件配置
    [key: string]: IEventBlock[]
  }
  eventData?: IEventVideo[];
  playerOpt: { // 播放器配置,用于切换主副摄
    player: {
      main: React.MutableRefObject<Player | null> | null;
      secondary?: React.MutableRefObject<Player | null> | null;
      third?: React.MutableRefObject<Player | null> | null;
    }
    url: {
      main: string;
      secondary?: string;
      third?: string;
    }
  }
  recordOpt?: { // 选中事件相关配置
    isRecord: boolean,
    recordTime: number,
    event: IEventVideo | undefined;
  };
  setRecordEvent?: (v: any) => void; // 设置选中事件
  setConfig?: (v: any) => void;
  setCurTime?: (v: Date) => void; // 获取时间轴当前时间
  isPause: boolean // 当前视频是否暂停中
  onChange?: (v: string) => void;
}

// 时间轴核心组件
const TimeAxis = (props: ITimeAxis) => {
  const { deviceType, isFull, type, setCurrent, current, currentWidth, currentHeight, events, playerOpt, recordOpt, setRecordEvent, isPause, setCurTime, onChange } = props; // 按钮调动
  const containerRef = useRef<HTMLDivElement>(null); // 容器
  const [scrollX, setScrollX] = useState<number>(0); // 滚动的长度
  const [isDragging, setIsDragging] = useState<boolean>(false); // 是否拖拽
  const [startX, setStartX] = useState<number>(0); // 每一次开始滚动的x
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  const indicatorValue = useRef<string>(''); // 记录当前指示器的value，format在move中改
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [currentEventKey, setCurrentEventKey] = useState<string>('');
  const [timeBlocks, setTimeBlocks] = useState<Date[]>(() => {
    const base = new Date();
    return [
      new Date(base.getTime() - 86400000),
      new Date(base),
      new Date(base.getTime() + 86400000)
    ];
  });
  const timer: MutableRefObject<NodeJS.Timeout | null> = useRef(null);

  const moveBySecond = useCallback((second: number) => {
    if (isIos && isFull) {
      const viewWidth = currentHeight || window.innerHeight / 2; //ios用的是css全屏，要选择
      const x = -(second / 1800 * viewGrid * secondsPerPixel) + viewWidth; //当前时间*时间轴密度，因为是向左拖拽所以要+上指示器的位置才能让它指向当前时间
      setScrollX(x);
      setStartX(x);
      return;
    }

    const viewWidth = currentWidth || window.innerWidth / 2; //当前容器宽度，找到指示器的位置
    const x = -(second / 1800 * viewGrid * secondsPerPixel) + viewWidth; //当前时间*时间轴密度，因为是向左拖拽所以要+上指示器的位置才能让它指向当前时间
    setScrollX(x);
    setStartX(x);
  }, [currentHeight, currentWidth, isFull])

  // 获取当前日期
  const getCurrent = useCallback(() => {
    if (!current) return;
    setTimeBlocks([
      new Date(current.getTime() - 86400000),
      new Date(current),
      new Date(current.getTime() + 86400000)
    ])

    const second = current.getHours() * 3600 + current.getMinutes() * 60 + current.getSeconds();
    moveBySecond(second);
  }, [current, moveBySecond])

  const currentSecond = useMemo(() => currentTime.getHours() * 3600 + currentTime.getMinutes() * 60 + currentTime.getSeconds(), [currentTime])

  const isFindCurrentDay = useCallback((day: number) => {
    return timeBlocks.findIndex((item) => item.getDate() === day) > 0 ? true : false
  }, [timeBlocks])

  // 动态计算时间块位置
  const getBlockPosition = (index: number) => {
    return (index - 1) * blockWidth
  };

  // 拖拽逻辑
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    setStartX(e.clientX - scrollX);
  }, [scrollX]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    setIsDragging(true);
    if (isFull && isIos) {
      setStartX(touch.clientY - scrollX);
      return;
    }
    setStartX(touch.clientX - scrollX);
  }, [isFull, scrollX]);

  useEffect(() => {
    getCurrent()
  }, [getCurrent])

  const countIndicator = useCallback((x: number) => {
    let pixelOffset = currentWidth ? (-x + currentWidth) * pow : (-x + window.innerWidth / 2) * pow;
    if (isFull && isIos) {
      pixelOffset = currentHeight ? (-x + currentHeight) * pow : (-x + window.innerHeight / 2) * pow;
    }
    const totalSeconds = pixelOffset * secondsPerPixel;

    let hours = Math.floor(totalSeconds / 3600);
    let minutes = Math.floor(totalSeconds / 60 % 60);
    let seconds = Math.floor(totalSeconds % 60);
    hours = hours === 24 ? 0 : hours < 0 ? (24 + hours) % 24 : hours % 24;
    minutes = minutes === 60 ? 0 : minutes < 0 ? (60 + minutes) : minutes;
    seconds = seconds === 60 ? 0 : seconds < 0 ? (60 + seconds) : seconds;
    return { hours, seconds, minutes, totalSeconds };
  }, [currentHeight, currentWidth, isFull])

  // 返回当前时间轴所指时间
  useEffect(() => {
    if (setCurTime) {
      const { hours, minutes, seconds } = countIndicator(scrollX);
      const date = new Date();
      date.setHours(hours);
      date.setMinutes(minutes);
      date.setSeconds(seconds);
      setCurTime(date);
    }
  }, [countIndicator, scrollX, setCurTime])

  const countIndicatorValue = useCallback((x: number) => {
    const { hours, seconds, minutes } = countIndicator(x);
    // 显示格式改为 HH:MM:SS
    indicatorValue.current = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }, [countIndicator])

  const moveLogic = useCallback((x: number) => {
    // 左边界，视图改变[v1][currentW][v2]=>[v0][v1][currentW]
    if (x > blockWidth) {
      const newBlocks = [...timeBlocks];
      const firstDate = new Date(newBlocks[0]);
      firstDate.setDate(firstDate.getDate() - 1);
      newBlocks.pop();
      newBlocks.unshift(firstDate);
      setTimeBlocks(newBlocks);
      setScrollX(x - blockWidth); //因为指示器在视图中间
      setIsDragging(false);
      indicatorValue.current = '';
      return;
    }


    // 右边界为currentTime,且当到达右边界时,播放器切换为live模式,保留v2视图但右边界不再切换
    let viewWidth = currentWidth || window.innerWidth / 2; //当前容器宽度，找到指示器的位置
    if (isFull && isIos) viewWidth = currentHeight || window.innerHeight / 2;

    // 查询当前时间与今天是否相同
    if (isFindCurrentDay(currentTime.getDate())) {
      const currentBlockPosition = getBlockPosition(timeBlocks.findIndex((item) => item.getDate() === currentTime.getDate()));
      const currentX = -(currentSecond / 1800 * viewGrid * secondsPerPixel) + viewWidth - currentBlockPosition;
      if (x < currentX) {
        setIsDragging(false);
        setScrollX(currentX);
        indicatorValue.current = '';
        //设置为live模式
        if (playerOpt && playerOpt.player && playerOpt.url) {
          console.log('现在是Live模式');

          if (playerOpt.player.main && playerOpt.player.main.current) {
            console.log('主摄改变为原路径：', splitURL(playerOpt.url.main));
            playerOpt.player.main.current.config.url = splitURL(playerOpt.url.main);
            playerOpt.player.main.current.switchURL(splitURL(playerOpt.url.main))
          }

          if (playerOpt.player.secondary && playerOpt.player.secondary.current) {
            console.log('副摄改变为原路径：', playerOpt.url.secondary && splitURL(playerOpt.url.secondary));
            playerOpt.player.secondary.current.config.url = playerOpt.url.secondary && splitURL(playerOpt.url.secondary)
            playerOpt.player.secondary.current.switchURL(playerOpt.url.secondary ? splitURL(playerOpt.url.secondary) : '')
          }
        }
        return;
      }
    }

    // 右边界，视图改变[v1][currentW][v2]=>[currentW][v2][v3]
    if (x < -blockWidth) {
      const newBlocks = [...timeBlocks];
      const lastDate = new Date(newBlocks[newBlocks.length - 1]);
      lastDate.setDate(lastDate.getDate() + 1);
      newBlocks.shift();
      newBlocks.push(lastDate);
      setTimeBlocks(newBlocks);
      setScrollX(x + blockWidth);
      setIsDragging(false);
      indicatorValue.current = '';
      return;
    }

    // 根据用户指示器移动位置决定是否切换天数，通过更改current来更改timeBlock和更新指示器位置
    // if (!isFull) {
    let pixelOffset = (-x + viewWidth) * pow;
    if (pixelOffset < 0) {
      setCurrent && setCurrent((prev: Date) => set(subDays(prev, 1), { hours: 23, minutes: 59, seconds: 59 }));
      setIsDragging(false);
      return;
    }

    if (pixelOffset > blockWidth * pow) {
      setCurrent && setCurrent((prev: Date) => set(addDays(prev, 1), { hours: 0, minutes: 0, seconds: 1 }));
      setIsDragging(false);
      return;
    }
    // }

    countIndicatorValue(x);
    setScrollX(x);
  }, [countIndicatorValue, currentHeight, currentSecond, currentTime, currentWidth, isFindCurrentDay, isFull, playerOpt, setCurrent, timeBlocks])

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isDragging || !containerRef.current) return;
    const touch = e.touches[0];
    let x = touch.clientX - startX;
    if (isFull && isIos) x = touch.clientY - startX;
    moveLogic(x);
  }, [isDragging, isFull, moveLogic, startX])

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    if (!isDragging || !containerRef.current) return;
    let x = e.clientX - startX;
    moveLogic(x);
  }, [isDragging, moveLogic, startX]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsRecording(false);
    setRecordEvent && setRecordEvent(undefined);
    indicatorValue.current = '';
  }, [setRecordEvent])

  const seekCallback = useCallback((time: number) => {
    if (playerOpt && playerOpt.player && playerOpt.url) {
      const mainCamera = playerOpt.player.main;
      const secondary = playerOpt.player.secondary;

      // 各自ref控制各自的视频播放器
      if (mainCamera && mainCamera.current) {
        const seekTime = mainCamera.current.currentTime + time;
        if (seekTime > mainCamera.current.duration) return;
        mainCamera.current.seek(seekTime);
      }

      if (secondary && secondary.current) {
        const seekTime = secondary.current.currentTime + time;
        if (seekTime > secondary.current.duration) return;
        secondary.current.seek(seekTime);
      }
    }
  }, [playerOpt])

  //左右箭头
  const handleLeft = useCallback(() => {
    seekCallback(-10);
    moveLogic(scrollX + 1 / pow)
  }, [moveLogic, scrollX, seekCallback])

  const handleRight = useCallback(() => {
    seekCallback(10);
    moveLogic(scrollX - 1 / pow)
  }, [moveLogic, scrollX, seekCallback])

  const initPosition = useCallback(() => {
    const date = new Date();
    const second = date.getHours() * 3600 + date.getMinutes() * 60 + date.getSeconds();
    moveBySecond(second);
  }, [moveBySecond])

  useEffect(() => {
    initPosition();
    window.addEventListener('resize', initPosition);

    return () => window.removeEventListener('resize', initPosition);
  }, [initPosition]);

  // 自动滚动到当前时间位置，在拖拽时不会触发此函数
  const autoScroll = useCallback(() => {
    if (isIos && isFull) {
      setScrollX(p => {
        const { totalSeconds } = countIndicator(p);
        const viewWidth = currentHeight || window.innerHeight / 2;
        const x = -((totalSeconds + 1) / 1800 * viewGrid * secondsPerPixel) + viewWidth;
        setStartX(x);
        return x;
      });
      return;
    }
    setScrollX(p => {
      const { totalSeconds } = countIndicator(p);
      const viewWidth = currentWidth || window.innerWidth / 2; //当前容器宽度，找到指示器的位置
      const x = -((totalSeconds + 1) / 1800 * viewGrid * secondsPerPixel) + viewWidth; //当前时间*时间轴密度，因为是向左拖拽所以要+上指示器的位置才能让它指向当前时间
      setStartX(x);
      return x;
    });
  }, [countIndicator, currentHeight, currentWidth, isFull])

  const startGetM3u8 = useCallback(async (camera_lens: string, time: string, mainCamera: React.MutableRefObject<Player | null>,
    secondary_camera_lens?: string, secondaryCamera?: React.MutableRefObject<Player | null>) => {

    if (mainCamera.current) { // 起码有一个播放器要打开才能访问
      if (secondary_camera_lens && secondaryCamera && secondaryCamera.current) { // 双摄像头直播切换
        const res = await startLiveWithCamera([camera_lens, secondary_camera_lens], time);

        if (res.code === 0 && res.data) {
          try {
            // 切换播放源，此处需要用到hls.js的switchURL方法
            // mainCamera.current.config.url = splitURL(res.data[camera_lens].hls_file);
            mainCamera.current.resetState();
            mainCamera.current.switchURL(splitURL(res.data[camera_lens].hls_file));

            // secondaryCamera.current.config.url = splitURL(res.data[secondary_camera_lens].hls_file);
            mainCamera.current.resetState();
            secondaryCamera.current.switchURL(splitURL(res.data[secondary_camera_lens].hls_file));
          } catch (e) {
            console.log('播放错误', e);
          }
        }

        if (res.code === 1701) {
          Toast.show('找不到此IPC设备');
        }

        return;
      }
      // 单目摄像头直播切换
      const res = await startLiveWithCamera([camera_lens], time);

      if (res.code === 0 && res.data) {
        const { hls_file } = res.data[camera_lens];
        try {
          // mainCamera.current.config.url = splitURL(hls_file);
          mainCamera.current.resetState();
          mainCamera.current.switchURL(splitURL(hls_file));
        } catch (e) {
          console.log('播放错误', e)
        }
      }

      if (res.code === 1701) {
        Toast.show('找不到此IPC设备');
      }
    }
  }, [])

  // 监听播放事件
  const playEvent = useCallback((mainEvents: IEventBlock | undefined, secondaryEvents: IEventBlock | undefined) => {
    const mainCamera = playerOpt.player.main;
    const secondaryCamera = playerOpt.player.secondary;
    if (!mainEvents && !secondaryEvents) return;

    if (!mainCamera || !mainCamera.current || !secondaryCamera || !secondaryCamera.current) {
      setRecordEvent && setRecordEvent(undefined);
      console.log('请打开播放器');
      return;
    }
    if (isRecording) return;
    setIsRecording(true);
    // 查询到事件后应该以当前时间轴的时间戳来查询直播切片
    if (mainEvents) {
      setCurrentEventKey(mainEvents.id);

      if (secondaryEvents) {
        console.log('开始播放,当前副摄切换播放源为:', secondaryEvents.file, '当前播放的事件id:', secondaryEvents.id, '当前播放事件参数:', secondaryEvents);
        startGetM3u8(mainEvents.camera_lens, mainEvents.time, mainCamera, secondaryEvents.camera_lens, secondaryCamera);
      } else {
        console.log('开始播放,当前主摄切换播放源为:', mainEvents.file, '当前播放的事件id:', mainEvents.id, '当前播放事件参数:', mainEvents);
        startGetM3u8(mainEvents.camera_lens, mainEvents.time, mainCamera);
      }
    } else {
      // 当轴驱动时可能存在主摄事件不存在,此时将主显显示为副摄镜头的事件,而副摄直播不变
      secondaryEvents && setCurrentEventKey(secondaryEvents.id);
      if (secondaryEvents) {
        console.log('开始播放,当前主摄切换播放源为:', secondaryEvents.file, '当前播放的事件id:', secondaryEvents.id, '当前播放事件参数:', secondaryEvents);
        startGetM3u8(secondaryEvents.camera_lens, secondaryEvents.time, mainCamera);
      }
    }
  }, [isRecording, playerOpt.player.main, playerOpt.player.secondary, setRecordEvent, startGetM3u8])

  // 监听事件,如果指示器所指的范围内有事件并且此时不在拖拽中,也不在播放中,则转为回看
  const eventListener = useCallback(() => {
    if (isDragging) return;
    if (events) {
      const curEvent = recordOpt?.event;
      let mainEvents: IEventBlock | undefined;
      let secondaryEvents: IEventBlock | undefined;
      const { totalSeconds } = countIndicator(scrollX);
      Object.keys(events).forEach(key => {
        const es = [...events[key]]
        const e: IEventBlock | undefined = es.find((it) => totalSeconds >= it.start && totalSeconds <= it.end);
        if (e) {
          if (e.camera_lens.split('_')[1] === '0') mainEvents = e;
          if (e.camera_lens.split('_')[1] === '1') secondaryEvents = e;
        }
      }) // 获取当前时间主副摄像头事件

      // 判断当前事件是主镜头事件还是其他镜头事件,如果是由拖动轴来驱动的,默认主镜头事件
      if (mainEvents || secondaryEvents) {
        // console.log(`当前主显的镜头为:${curEvent ? curEvent.camera_lens : '轴驱动'},当前事件为:${curEvent ? curEvent.id : mainEvents?.id || secondaryEvents?.id}`);
        onChange && onChange(curEvent ? curEvent.id : mainEvents ? mainEvents.id : secondaryEvents ? secondaryEvents.id : '');

        if (mainEvents) {
          if (currentEventKey !== mainEvents.id) {
            setIsRecording(false);
            setRecordEvent && setRecordEvent(undefined);
          }
        } else {
          if (secondaryEvents && currentEventKey !== secondaryEvents.id) {
            setIsRecording(false);
            setRecordEvent && setRecordEvent(undefined);
          }
        }
      } else {
        // console.log('当前时间轴范围内无事件');
        onChange && onChange('');
        setIsRecording(false);
        return;
      }

      switch (curEvent?.camera_lens.split('_')[1]) {
        // 根据事件片段栏所选择的事件所属镜头不同,让主副显使用不同的url
        case '0': playEvent(mainEvents, secondaryEvents); break;
        case '1': playEvent(secondaryEvents, mainEvents); break;
        default: playEvent(mainEvents, secondaryEvents); break;
      }
    }
  }, [isDragging, events, recordOpt?.event, countIndicator, scrollX, onChange, currentEventKey, setRecordEvent, playEvent])

  useEffect(() => {
    eventListener();
  }, [eventListener])

  useEffect(() => {
    if (recordOpt && recordOpt.isRecord) {
      setIsDragging(true);
      const date = new Date(recordOpt.recordTime);
      const h = date.getHours();
      const m = date.getMinutes();
      const s = date.getSeconds();
      moveBySecond(h * 3600 + m * 60 + s);
      setIsDragging(false);
      return;
    }
  }, [initPosition, moveBySecond, recordOpt])

  useEffect(() => {
    const timer = setInterval(() => {
      const date = new Date();
      setCurrentTime(date);
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    timer.current = setInterval(() => {
      autoScroll();
    }, 1000);
    if (isDragging) { // 如果是拖动状态下清除定时器
      clearInterval(timer.current);
    }
    if (isPause) {
      console.log('当前直播暂停中,时间轴暂停推进');
      clearInterval(timer.current);
    }
    return () => {
      timer.current && clearInterval(timer.current);
    }
  }, [autoScroll, isDragging, isPause])

  return (
    <div className={`time-axis-wrapper ${isFull ? 'full' : 'notFull'} ${deviceType} ${type}  ${isIos ? 'ios' : ''}`}>
      {/* 左侧箭头 */}
      <button className={`axis-arrow left  ${isFull ? 'full' : 'notFull'} ${deviceType} ${type}  ${isIos ? 'ios' : ''}`} onClick={handleLeft}>
        <PreloadImage style={{ height: px2rem(24) }} src={isFull ? leftSeek : leftSeek_dark} alt='' />
      </button>
      <div className={`current-time-indicator  ${isFull ? 'full' : 'notFull'} ${deviceType} ${type} ${isIos ? 'ios' : ''}`}>
        <span className={`current-time-indicator-value  ${isFull ? 'full' : 'notFull'} ${deviceType} ${type} ${isIos ? 'ios' : ''}`}>{indicatorValue.current}</span>
      </div>

      <div className={`time-axis-container ${isFull ? 'full' : 'notFull'} ${deviceType} ${type}`}
        onMouseDown={handleMouseDown} onMouseMove={handleMouseMove} onMouseUp={handleMouseUp} onMouseLeave={() => setIsDragging(false)}
        onTouchStart={handleTouchStart} onTouchMove={handleTouchMove} onTouchEnd={handleMouseUp} onTouchCancel={handleMouseUp}
        ref={containerRef} style={{ cursor: isDragging ? 'grabbing' : 'grab' }}>
        <div className="time-axis-content" style={{ transform: `translateX(${px2rem(scrollX)})` }}>
          {
            timeBlocks.map((date, blockIndex) => (
              <div key={date.toDateString()} className='date-block' style={{ left: px2rem(getBlockPosition(blockIndex)) }}>
                {
                  genTimeMarkers(date).map((marker, index) => {
                    return <div key={marker.time.getDate() + index}>
                      <div key={index} className={`time-marker ${marker.isMajor ? 'major' : 'minor'} ${deviceType} ${isFull ? 'full' : 'notFull'}`} style={{ left: px2rem(index * viewGrid * secondsPerPixel) }}>
                        <div className={`marker-line ${isFull ? 'full' : 'notFull'} ${deviceType} ${type}`} />
                        <span className={`time-label ${isFull ? 'full' : 'notFull'} ${deviceType} ${type} ${isIos ? 'ios' : ''}`}>{`${marker.formatted}`}</span>
                      </div>
                      {!marker.isMajor && !isFull ? <span className='time-icon' style={{ left: px2rem(index * viewGrid * secondsPerPixel) }}><div className='time-axis-oval'></div></span> : <></>}
                    </div>
                  })
                }
              </div>
            ))
          }
          <div className={`event-area-content-LiveText ${isFull ? 'full' : 'notFull'} ${deviceType} ${type} ${isIos ? 'ios' : ''}`} style={{
            display: isFindCurrentDay(currentTime.getDate()) ? 'flex' : 'none',
            left: px2rem(currentSecond / marksSecond * secondsPerPixel * viewGrid + getBlockPosition(timeBlocks.findIndex((item) => item.getDate() === currentTime.getDate())))
          }}>
            Live
          </div>
          {
            events && Object.keys(events).map((key, index) => {
              return (
                <div key={key} className={`event-area-content ${isFull ? 'full' : 'notFull'} ${deviceType} ${type} ${isIos ? 'ios' : ''}`} style={{ width: px2rem(currentSecond / marksSecond * secondsPerPixel * viewGrid), top: px2rem(index * 16 + 38) }}>
                  {events[key].map((event, index) => {
                    return <div key={`event-${index}`} className={`event-block  ${isFull ? 'full' : 'notFull'} ${deviceType} ${type}`} style={{
                      display: isFindCurrentDay(event.day) ? 'flex' : 'none',
                      left: px2rem(event.start / marksSecond * secondsPerPixel * viewGrid + getBlockPosition(timeBlocks.findIndex((item) => item.getDate() === event.day))),
                      width: px2rem((event.end - event.start) / marksSecond * secondsPerPixel * viewGrid), backgroundColor: event.color
                    }} />
                  })}
                </div>
              )
            })
          }
        </div>
      </div>

      {/* 右侧箭头 */}
      <button className={`axis-arrow right  ${isFull ? 'full' : 'notFull'} ${deviceType} ${type} ${isIos ? 'ios' : ''}`} onClick={handleRight}>
        <PreloadImage style={{ height: px2rem(24) }} src={isFull ? rightSeek : rightSeek_dark} alt='' />
      </button>
    </div >
  );
};

export default TimeAxis;