import React, { useCallback, useRef, useState } from "react";
import { Image, List, Checkbox, Toast, CenterPopup } from "antd-mobile";
import styles from "./index.module.scss";
import TimePicker from "@/components/TimePicker";
import close from "@/Resources/camMgmtImg/close.png";
import finish from "@/Resources/camMgmtImg/finish.png";
import picker from "@/Resources/camMgmtImg/picker.png";
import closeDark from "@/Resources/camMgmtImg/close-dark.png";
import finishDark from "@/Resources/camMgmtImg/finish-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { ICollapsePanel } from "@/layouts/Layout";
import { ICameraDetail } from "@/pages/IPC/IPC_APP/CameraDetail";
import { IDeviceDetail } from "../../..";

// 星期映射
const dayValueToApi: Record<string, string> = {
  "1": "Mon",
  "2": "Tue",
  "3": "Wed",
  "4": "Thu",
  "5": "Fri",
  "6": "Sat",
  "7": "Sun"
};

// 重复模式映射
const repeatModeToApi: Record<string, string> = {
  "执行一次": "once",
  "每天": "everyday",
  "法定工作日": "workday",
  "法定节假日": "holiday",
  "自定义": "customized"
};

interface ShutDownProps {
  onClose?: () => void;
  onFinish?: () => void;
  camera: (ICollapsePanel & ICameraDetail & IDeviceDetail)
  callback: (value: any, key: any, paramName: any) => void;
}

const ShutdownSetting: React.FC<ShutDownProps> = ({ onClose, onFinish, camera, callback }) => {
  const { isDarkMode } = useTheme();

  const getCurrentTime = () => {
    const now = new Date();
    return [
      String(now.getHours()).padStart(2, '0'),
      String(now.getMinutes()).padStart(2, '0')
    ];
  };

  const [startVisible, setStartVisible] = useState(false);
  const [endVisible, setEndVisible] = useState(false);
  const [repeatVisible, setRepeatVisible] = useState(false);
  const [startTime, setStartTime] = useState(getCurrentTime);
  const [endTime, setEndTime] = useState(getCurrentTime);
  const [repeatMode, setRepeatMode] = useState("每天");
  const [selectedRepeatMode, setSelectedRepeatMode] = useState(repeatMode);
  const [customDaysVisible, setCustomDaysVisible] = useState(false);
  const [selectedDays, setSelectedDays] = useState<string[]>([]);

  // 弹出层挂在container ref
  const containerRef = useRef(null);

  // 重复模式选项
  const repeatOptions = [
    { label: "执行一次", value: "执行一次" },
    { label: "每天", value: "每天" },
    { label: "法定工作日", value: "法定工作日" },
    { label: "法定节假日", value: "法定节假日" },
    { label: "自定义", value: "自定义" },
  ];

  const weekDays = [
    { label: "周一", value: "1" },
    { label: "周二", value: "2" },
    { label: "周三", value: "3" },
    { label: "周四", value: "4" },
    { label: "周五", value: "5" },
    { label: "周六", value: "6" },
    { label: "周日", value: "7" },
  ];

  const handleRepeatOptionClick = (value: string) => {
    setSelectedRepeatMode(value);
    if (value === "自定义") {
      setCustomDaysVisible(true);
      setRepeatVisible(false);
    }
  };

  // 将选中的日期转换为显示文本
  const getSelectedDaysText = () => {
    if (selectedDays.length === 7) return "每天";
    if (selectedDays.length === 0) return "自定义";
    return selectedDays
      .map((day) => weekDays.find((d) => d.value === day)?.label)
      .join("、");
  };

  // 将选中的日期值转换为API需要的格式
  const getApiDays = useCallback(() => {
    return selectedDays.map(day => dayValueToApi[day] || "");
  },[selectedDays]);

  const handleStartTimeConfirm = (time: string[]) => {
    setStartTime(time);
  };

  const handleEndTimeConfirm = (time: string[]) => {
    setEndTime(time);
  };

  // 保存时间段
  const saveTimeSlot = () => {
    if (!camera?.did) {
      Toast.show({
        content: "缺少摄像头信息，无法保存"
      });
      return;
    }

    // 验证时间
    const startHour = parseInt(startTime[0]);
    const startMinute = parseInt(startTime[1]);
    const endHour = parseInt(endTime[0]);
    const endMinute = parseInt(endTime[1]);

    // 简单验证，确保结束时间晚于开始时间
    if (startHour > endHour || (startHour === endHour && startMinute >= endMinute)) {
      Toast.show({
        content: "结束时间必须晚于开始时间"
      });
      return;
    }
    // 如果是自定义重复模式，检查是否选择了天数
    if (repeatMode === "自定义" && selectedDays.length === 0) {
      Toast.show({
        content: "请选择至少一天"
      });
      return;
    }

    console.log("获取当前配置并添加新时间段");
    saveSlot();
  };

  const saveSlot = useCallback(() => {
    const currentSchedule = camera.data.recordConfig.recordPlan.schedule || [];

    // 获取API需要的repeat_policy值
    let apiRepeatPolicy = repeatModeToApi[repeatMode];

    // 如果是自定义显示的周几组合（不在预定义选项中），则强制设置为customized
    if (!apiRepeatPolicy && selectedDays.length > 0) {
      apiRepeatPolicy = "customized";
    }

    // 创建新时间段
    const newTimeSlot = {
      start: `${startTime[0]}:${startTime[1]}`,
      end: `${endTime[0]}:${endTime[1]}`,
      repeat_policy: apiRepeatPolicy,
      customized: apiRepeatPolicy === "customized" ? getApiDays() : [],
      enabled: true
    };

    // 添加新时间段
    const updatedSchedule = currentSchedule.concat(newTimeSlot);
    console.log('原本时间段', currentSchedule, '现在时间', newTimeSlot);

    // 保存更新后的配置
    console.log('新时间段', { type: 'customized', schedule: updatedSchedule });
    callback({
      type: "customized",
      schedule: updatedSchedule,
    }, 'recordPlan', 'record_schedule')

    onFinish && onFinish();
  }, [callback, camera.data.recordConfig.recordPlan.schedule, endTime, getApiDays, onFinish, repeatMode, selectedDays.length, startTime])

  return (
    <div className={styles.container} ref={containerRef}>
      <div className={styles.header}>
        <Image
          className={styles.close}
          src={isDarkMode ? closeDark : close}
          onClick={onClose}
        />
        <Image
          className={styles.edit}
          src={isDarkMode ? finishDark : finish}
          onClick={saveTimeSlot}
        />
      </div>
      <div className={styles.title}>定时关机</div>

      <div className={styles.content}>
        <List className={styles.settingList}>
          <div className={styles.listAll}>
            <List.Item
              className={styles.settingItem}
              prefix={<span className={styles.label}>开始时间</span>}
              onClick={() => setStartVisible(true)}
              extra={
                <div className={styles.valueContainer}>
                  <span
                    className={styles.value}
                  >{`${startTime[0]}:${startTime[1]}`}</span>
                </div>
              }
              arrow={true}
            />
            <List.Item
              className={styles.settingItem}
              prefix={<span className={styles.label}>结束时间</span>}
              onClick={() => setEndVisible(true)}
              extra={
                <div className={styles.valueContainer}>
                  <span
                    className={styles.value}
                  >{`${endTime[0]}:${endTime[1]}`}</span>
                </div>
              }
              arrow={true}
            />
          </div>
        </List>
        <div className={styles.thinLine} style={{ marginTop: 16 }} />
        <List className={styles.listBox}>
          <List.Item
            className={styles.settingItem}
            prefix={<span className={styles.label}>重复</span>}
            extra={
              <div className={styles.valueContainer}>
                <span className={styles.value}>{repeatMode}</span>
              </div>
            }
            onClick={() => {
              setSelectedRepeatMode(repeatMode);
              setRepeatVisible(true);
            }}
          />
        </List>
      </div>

      {/* 开始时间选择器 */}
      <TimePicker
        visible={startVisible}
        onClose={() => setStartVisible(false)}
        value={startTime}
        onConfirm={handleStartTimeConfirm}
        title="选择开始时间"
      />

      {/* 结束时间选择器 */}
      <TimePicker
        visible={endVisible}
        onClose={() => setEndVisible(false)}
        value={endTime}
        onConfirm={handleEndTimeConfirm}
        title="选择结束时间"
      />

      {/* 重复模式选择弹窗 */}
      <CenterPopup
        visible={repeatVisible}
        onMaskClick={() => setRepeatVisible(false)}
        getContainer={containerRef.current}>
        <div className={styles.repeatPopup}>
          <div className={styles.popupHeader}>
            <div className={styles.popupTitle}>重复</div>
          </div>
          <div className={styles.repeatOptions}>
            {repeatOptions.map((option) => (
              <div
                key={option.value}
                className={styles.repeatOption}
                data-selected={selectedRepeatMode === option.value}
                onClick={() => handleRepeatOptionClick(option.value)}
              >
                <div className={styles.optionContent}>
                  <div className={styles.iconContainer}>
                    {selectedRepeatMode === option.value && (
                      <img className={styles.checkedIcon} alt="" src={picker} />
                    )}
                  </div>
                  <span className={styles.optionText}>{option.label}</span>
                </div>
              </div>
            ))}
          </div>
          <div className={styles.popupFooter}>
            <div
              className={styles.popupButton}
              onClick={() => setRepeatVisible(false)}
            >
              取消
            </div>
            <div
              className={`${styles.popupButton} ${styles.primary}`}
              onClick={() => {
                setRepeatMode(selectedRepeatMode);
                setRepeatVisible(false);
              }}
            >
              确定
            </div>
          </div>
        </div>
      </CenterPopup>

      {/* 自定义重复日期弹窗 */}
      <CenterPopup
        visible={customDaysVisible}
        onMaskClick={() => setCustomDaysVisible(false)}
        getContainer={containerRef.current}>
        <div className={styles.repeatPopup}>
          <div className={styles.popupHeader}>
            <div className={styles.popupTitle}>自定义</div>
          </div>
          <div className={styles.repeatOptions}>
            {weekDays.map((day) => (
              <div
                key={day.value}
                className={styles.repeatOption}
                onClick={() => {
                  const isSelected = selectedDays.includes(day.value);
                  const newSelectedDays = isSelected
                    ? selectedDays.filter((d) => d !== day.value)
                    : [...selectedDays, day.value];
                  setSelectedDays(newSelectedDays);
                }}
              >
                <div className={styles.optionContent}>
                  <span className={styles.optionText}>{day.label}</span>
                  <Checkbox checked={selectedDays.includes(day.value)} />
                </div>
              </div>
            ))}
          </div>
          <div className={styles.popupFooter}>
            <div
              className={styles.popupButton}
              onClick={() => setCustomDaysVisible(false)}
            >
              取消
            </div>
            <div
              className={`${styles.popupButton} ${styles.primary}`}
              onClick={() => {
                setRepeatMode(getSelectedDaysText());
                setCustomDaysVisible(false);
              }}
            >
              确定
            </div>
          </div>
        </div>
      </CenterPopup>
    </div>
  );
};

export default ShutdownSetting;