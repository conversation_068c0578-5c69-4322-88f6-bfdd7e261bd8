import { List, ProgressBar, Badge, Checkbox, Toast } from "antd-mobile";
import styles from "./index.module.scss";
import { useEffect, useState, useCallback, forwardRef, useImperativeHandle } from "react";
import { getTaskManagerFileIconForApp } from '@/utils/fileTypeUtils';
import { TaskInfo, controlTask, deleteTask } from "@/api/nasDisk";
import request from "@/request";
import EmptyState from "../../components/EmptyState";
import { modalShow } from "@/components/List";
import start from '@/Resources/nasDiskImg/play.png';
import pause from '@/Resources/nasDiskImg/pause.png';
// import pauseOne from '@/Resources/nasDiskImg/pauseOne.png';
import restart from '@/Resources/nasDiskImg/restart.png'
import restartBlack from '@/Resources/nasDiskImg/restartBlack.png'

interface DownloadTaskProps {
  isMultiSelect: boolean;
  onEnterMultiSelect: () => void;
  onSelectedTasksChange?: (count: number) => void;
  onTotalTasksChange?: (count: number) => void;
  onExitMultiSelect?: () => void;
}

// 暴露给父组件的方法
export interface DownloadTaskRef {
  deleteSelectedTasks: () => Promise<void>;
  selectAllTasks: (selectAll: boolean) => void;
}

/**
 * 任务状态分类说明：
 * - 活动状态（active）: waiting（排队等待中）、running（执行中）、paused（暂停中）
 * - 结束状态（history）: success（成功完成）、failed（失败）、cancelled（被取消）、partial error（部分失败）
 */



// 任务状态映射
const getTaskStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'waiting': '等待中',
    'running': '正在下载',
    'paused': '已暂停',
    'success': '下载完成',
    'failed': '下载失败',
    'cancelled': '已取消',
    'partial error': '部分失败'
  };
  return statusMap[status] || status;
};

  // 获取状态颜色
  const getStatusColor = (status: TaskInfo['status']) => {
    switch (status) {
      case 'success_waiting':
        return '#3482FF';
      case 'running':
        return '#3482FF';
      case 'waiting':
        return '#3482FF';
      case 'failed':
        return '#F30018';
      case 'paused':
        return '#fa8c16';
      default:
        return '#666';
    }
  };

// 格式化文件大小
const formatFileSize = (size: string | number | null | undefined): string => {
  // 处理 null、undefined 或空值
  if (size == null || size === '') {
    return '0.0B';
  }

  if (typeof size === 'string') {
    // 如果已经是格式化的字符串，直接返回
    if (size.includes('B') || size.includes('MB') || size.includes('GB') || size.includes('TB')) {
      return size;
    }
    // 如果是纯数字字符串，转换为数字处理
    const numSize = parseInt(size);
    if (isNaN(numSize)) return '0.0B'; // 无效字符串返回 0.0B
    size = numSize;
  }

  // 确保 size 是一个有效的数字
  const numericSize = Number(size);
  if (isNaN(numericSize) || numericSize < 0) {
    return '0.0B';
  }

  if (numericSize === 0) return '0.0B';

  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(numericSize) / Math.log(1024));
  const sizeIndex = Math.min(i, sizes.length - 1); // 确保索引不越界
  return Math.round(numericSize / Math.pow(1024, sizeIndex) * 100) / 100 + ' ' + sizes[sizeIndex];
};

// 格式化时间 - 处理毫秒时间戳
const formatTime = (timestamp: string): string => {
  if (!timestamp) return '';
  const date = new Date(parseInt(timestamp)); // 直接使用毫秒时间戳，不需要乘以1000
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取任务显示名称
const getTaskDisplayName = (task: TaskInfo): string => {
  if (task.src && task.src.length > 0 && task.src[0]) {
    const path = task.src[0];
    // 找到最后一个"/"的位置
    const lastSlashIndex = path.lastIndexOf('/');
    if (lastSlashIndex !== -1 && lastSlashIndex < path.length - 1) {
      // 返回最后一个"/"后面的部分
      return path.substring(lastSlashIndex + 1);
    }
    // 如果没有"/"或者"/"在最后，返回整个路径
    return path;
  }
  return task.dst || `任务${task.task_id}`;
};

// 存储池位置展示名称
const formatStorageLocation = (path: string) => {
  const poolMatch = path.match(/pool(\d+)/);
  if (!poolMatch) return "未知存储池";

  const poolNumber = parseInt(poolMatch[1], 10);
  const storagePoolName = `存储池${poolNumber + 1}`;
  const lastSlashIndex = path.lastIndexOf('/');
  const lastPart = lastSlashIndex >= 0 ? path.substring(lastSlashIndex) : '';

  return `${storagePoolName}${lastPart}`;
};

const DownloadTask = forwardRef<DownloadTaskRef, DownloadTaskProps>((props, ref) => {
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [downloadingTasks, setDownloadingTasks] = useState<TaskInfo[]>([]);
  const [failedTasks, setFailedTasks] = useState<TaskInfo[]>([]);
  const [completedTasks, setCompletedTasks] = useState<TaskInfo[]>([]);
  const [hasError, setHasError] = useState(false);
  const { isMultiSelect, onEnterMultiSelect, onSelectedTasksChange, onTotalTasksChange, onExitMultiSelect } = props;





  // 获取正在下载的任务（active）
  const fetchActiveTask = useCallback(async () => {
    try {
      const response = await request.post('/taskcenter/get_taskinfo', {
        selector: [
          {
            key: "module",
            value: ["bpan"]
          },
          {
            key: "type",
            value: ["active"]
          },
          {
            key: "action",
            value: ["download"]
          }
        ]
      }, { showLoading: false }); // 禁用loading动画

      if (response?.data?.info) {
        setDownloadingTasks(response.data.info);
      } else {
        setDownloadingTasks([]);
      }
      setHasError(false);
    } catch (error) {
      console.error('获取正在下载任务失败:', error);
      setHasError(true);
    }
  }, []);

  // 获取失败的任务（history + failed status）
  const fetchFailedTask = useCallback(async () => {
    try {
      const response = await request.post('/taskcenter/get_taskinfo', {
        selector: [
          {
            key: "module",
            value: ["bpan"]
          },
          {
            key: "type",
            value: ["history"]
          },
          {
            key: "action",
            value: ["download"]
          },
          {
            key: "status",
            value: ["failed"]
          }
        ]
      }, { showLoading: false }); // 禁用loading动画

      if (response?.data?.info) {
        setFailedTasks(response.data.info);
      } else {
        setFailedTasks([]);
      }
      setHasError(false);
    } catch (error) {
      console.error('获取失败任务失败:', error);
      setHasError(true);
    }
  }, []);

  // 获取已完成的任务（history + success status）
  const fetchCompletedTask = useCallback(async () => {
    try {
      const response = await request.post('/taskcenter/get_taskinfo', {
        selector: [
          {
            key: "module",
            value: ["bpan"]
          },
          {
            key: "type",
            value: ["history"]
          },
          {
            key: "action",
            value: ["download"]
          },
          {
            key: "status",
            value: ["success"]
          }
        ]
      }, { showLoading: false }); // 禁用loading动画

      if (response?.data?.info) {
        setCompletedTasks(response.data.info);
      } else {
        setCompletedTasks([]);
      }
      setHasError(false);
    } catch (error) {
      console.error('获取已完成任务失败:', error);
      setHasError(true);
    }
  }, []);

  // 初始化数据和设置轮询
  useEffect(() => {
    // 立即获取数据
    fetchActiveTask();
    fetchFailedTask();
    fetchCompletedTask();

    // 设置轮询
    const activeTimer = setInterval(fetchActiveTask, 5000); // 每5秒轮询正在下载的任务
    const failedTimer = setInterval(fetchFailedTask, 10000); // 每10秒轮询失败任务
    const completedTimer = setInterval(fetchCompletedTask, 10000); // 每10秒轮询已完成的任务

    return () => {
      clearInterval(activeTimer);
      clearInterval(failedTimer);
      clearInterval(completedTimer);
    };
  }, [fetchActiveTask, fetchFailedTask, fetchCompletedTask]);

  useEffect(() => {
    // 当多选模式关闭时，清空选中项
    if (!isMultiSelect) {
      setSelectedTasks([]);
    }
  }, [isMultiSelect]);

  const toggleTaskSelection = (taskId: string) => {
    if (selectedTasks.includes(taskId)) {
      setSelectedTasks(selectedTasks.filter(id => id !== taskId));
    } else {
      setSelectedTasks([...selectedTasks, taskId]);
    }
  };

  const handleLongPress = (taskId: string) => {
    // 如果还不是多选模式，则进入多选模式并选中当前任务
    if (!isMultiSelect && onEnterMultiSelect) {
      onEnterMultiSelect();
      setSelectedTasks([taskId]);
    }
  };

  // 当选中的任务发生变化时，通知父组件
  useEffect(() => {
    if (onSelectedTasksChange) {
      onSelectedTasksChange(selectedTasks.length);
    }
  }, [selectedTasks, onSelectedTasksChange]);

  // 监听任务总数变化，通知父组件
  useEffect(() => {
    const totalCount = downloadingTasks.length + failedTasks.length + completedTasks.length;
    if (onTotalTasksChange) {
      onTotalTasksChange(totalCount);
    }
  }, [downloadingTasks.length, failedTasks.length, completedTasks.length, onTotalTasksChange]);

  // 暴露删除功能给父组件
  useImperativeHandle(ref, () => ({
    deleteSelectedTasks: handleDeleteSelectedTasks,
    selectAllTasks: (selectAll: boolean) => {
      if (selectAll) {
        // 全选所有任务
        const allTaskIds = [
          ...downloadingTasks.map(task => task.task_id),
          ...failedTasks.map(task => task.task_id),
          ...completedTasks.map(task => task.task_id)
        ];
        setSelectedTasks(allTaskIds);
      } else {
        // 取消全选
        setSelectedTasks([]);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }), [selectedTasks, downloadingTasks, failedTasks, completedTasks]);

  // 处理正在下载列表的全选/取消全选
  const handleDownloadingSelectAll = () => {
    const downloadingTaskIds = downloadingTasks.map(task => task.task_id);
    const isAllSelected = downloadingTaskIds.length > 0 && downloadingTaskIds.every(id => selectedTasks.includes(id));

    if (isAllSelected) {
      // 取消全选：从选中列表中移除所有正在下载的任务ID
      setSelectedTasks(selectedTasks.filter(id => !downloadingTaskIds.includes(id)));
    } else {
      // 全选：将所有正在下载的任务ID添加到选中列表，去重
      const newSelectedTasks = [...new Set([...selectedTasks, ...downloadingTaskIds])];
      setSelectedTasks(newSelectedTasks);
    }
  };

  // 处理失败任务列表的全选/取消全选
  const handleFailedSelectAll = () => {
    const failedTaskIds = failedTasks.map(task => task.task_id);
    const isAllSelected = failedTaskIds.length > 0 && failedTaskIds.every(id => selectedTasks.includes(id));

    if (isAllSelected) {
      // 取消全选：从选中列表中移除所有失败任务的ID
      setSelectedTasks(selectedTasks.filter(id => !failedTaskIds.includes(id)));
    } else {
      // 全选：将所有失败任务的ID添加到选中列表，去重
      const newSelectedTasks = [...new Set([...selectedTasks, ...failedTaskIds])];
      setSelectedTasks(newSelectedTasks);
    }
  };

  // 处理已完成列表的全选/取消全选
  const handleCompletedSelectAll = () => {
    const completedTaskIds = completedTasks.map(task => task.task_id);
    const isAllSelected = completedTaskIds.length > 0 && completedTaskIds.every(id => selectedTasks.includes(id));

    if (isAllSelected) {
      // 取消全选：从选中列表中移除所有已完成的任务ID
      setSelectedTasks(selectedTasks.filter(id => !completedTaskIds.includes(id)));
    } else {
      // 全选：将所有已完成的任务ID添加到选中列表，去重
      const newSelectedTasks = [...new Set([...selectedTasks, ...completedTaskIds])];
      setSelectedTasks(newSelectedTasks);
    }
  };



  // 单个任务操作处理
  const handleTaskAction = async (taskId: string, action: 'pause' | 'resume' | 'retry') => {
    console.log(`执行任务操作: ${action} - ${taskId}`);

    // 将前端action映射到后端command
    const actionToCommandMap: Record<string, 'pause' | 'continue' | 'restart'> = {
      'pause': 'pause',      // 暂停
      'resume': 'continue',  // 恢复 -> 继续
      'retry': 'restart'     // 重试 -> 重新开始
    };

    const command = actionToCommandMap[action];
    if (!command) {
      console.error('未知的任务操作:', action);
      return;
    }

    try {
      const response = await controlTask({
        task_id: [taskId],  // 任务ID数组
        command: command    // 控制命令
      });

      if (response.code === 0) {
        console.log(`任务${action}操作成功:`, taskId);
        Toast.show({
          content: action === 'pause' ? '任务已暂停' : action === 'resume' ? '任务已开始' : '任务已重试',
          duration: 2000,
        });
        // 操作成功后立即刷新数据
        await fetchActiveTask();
        await fetchFailedTask();
        await fetchCompletedTask();
      } else {
        console.error(`任务${action}操作失败:`, response.result || '未知错误');
        Toast.show({
          content: `操作失败: ${response.result || '未知错误'}`,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error(`任务${action}操作异常:`, error);
      Toast.show({
        content: '操作失败，请重试',
        duration: 2000,
      });
    }
  };

  // 获取正在运行的任务
  const getRunningTasks = () => {
    return downloadingTasks.filter(task => task.status === 'running' || task.status === 'waiting');
  };

  // 获取已暂停的任务
  const getPausedTasks = () => {
    return downloadingTasks.filter(task => task.status === 'paused');
  };

  // 判断当前应该显示什么按钮
  const getBatchButtonState = () => {
    const runningTasks = getRunningTasks();
    const pausedTasks = getPausedTasks();

    if (runningTasks.length > 0) {
      return {
        action: 'pause',
        text: '全部暂停',
        icon: '',
        taskIds: runningTasks.map(task => task.task_id)
      };
    } else if (pausedTasks.length > 0) {
      return {
        action: 'resume',
        text: '全部开始',
        icon: '',
        taskIds: pausedTasks.map(task => task.task_id)
      };
    } else {
      return null;
    }
  };

  // 全部暂停操作
  const handlePauseAll = async () => {
    console.log('全部暂停下载任务');

    // 获取所有正在运行的任务ID
    const runningTaskIds = downloadingTasks
      .filter(task => task.status === 'running' || task.status === 'waiting')
      .map(task => task.task_id);

    if (runningTaskIds.length === 0) {
      Toast.show({
        content: '没有可暂停的任务',
        duration: 2000,
      });
      return;
    }

    try {
      const response = await controlTask({
        task_id: runningTaskIds,
        command: 'pause'
      });

      if (response.code === 0) {
        console.log('批量暂停任务成功:', runningTaskIds);
        Toast.show({
          content: `已暂停 ${runningTaskIds.length} 个任务`,
          duration: 2000,
        });
        // 操作成功后立即刷新数据
        await fetchActiveTask();
      } else {
        console.error('批量暂停任务失败:', response.result || '未知错误');
        Toast.show({
          content: `暂停失败: ${response.result || '未知错误'}`,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('批量暂停任务异常:', error);
      Toast.show({
        content: '暂停失败，请重试',
        duration: 2000,
      });
    }
  };

  // 全部开始操作
  const handleResumeAll = async () => {
    console.log('全部开始下载任务');

    // 获取所有已暂停的任务ID
    const pausedTaskIds = downloadingTasks
      .filter(task => task.status === 'paused')
      .map(task => task.task_id);

    if (pausedTaskIds.length === 0) {
      Toast.show({
        content: '没有可开始的任务',
        duration: 2000,
      });
      return;
    }

    try {
      const response = await controlTask({
        task_id: pausedTaskIds,
        command: 'continue'
      });

      if (response.code === 0) {
        console.log('批量开始任务成功:', pausedTaskIds);
        Toast.show({
          content: `已开始 ${pausedTaskIds.length} 个任务`,
          duration: 2000,
        });
        // 操作成功后立即刷新数据
        await fetchActiveTask();
      } else {
        console.error('批量开始任务失败:', response.result || '未知错误');
        Toast.show({
          content: `开始失败: ${response.result || '未知错误'}`,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('批量开始任务异常:', error);
      Toast.show({
        content: '开始失败，请重试',
        duration: 2000,
      });
    }
  };

  // 统一的批量操作处理
  const handleBatchAction = async () => {
    const buttonState = getBatchButtonState();
    if (!buttonState) return;

    if (buttonState.action === 'pause') {
      await handlePauseAll();
    } else if (buttonState.action === 'resume') {
      await handleResumeAll();
    }
  };

  // 执行批量删除选中任务的实际操作
  const performDeleteSelectedTasks = async () => {
    try {
      const response = await deleteTask({
        selector: [
          {
            key: 'task_id',
            value: selectedTasks
          }
        ]
      });

      if (response.code === 0) {
        console.log('批量删除任务成功:', selectedTasks);
        Toast.show({
          content: `已删除 ${selectedTasks.length} 个任务`,
          duration: 2000,
        });

        // 清空选中状态
        setSelectedTasks([]);

        // 操作成功后立即刷新数据
        await fetchActiveTask();
        await fetchFailedTask();
        await fetchCompletedTask();

        // 通知父组件退出多选模式
        if (onExitMultiSelect) {
          onExitMultiSelect();
        }
      } else {
        console.error('批量删除任务失败:', response.result || '未知错误');
        Toast.show({
          content: `删除失败: ${response.result || '未知错误'}`,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('批量删除任务异常:', error);
      Toast.show({
        content: '删除失败，请重试',
        duration: 2000,
      });
    }
  };

  // 批量删除选中的任务 - 显示确认弹窗
  const handleDeleteSelectedTasks = async () => {
    if (selectedTasks.length === 0) {
      Toast.show({
        content: '请选择要删除的任务',
        duration: 2000,
      });
      return;
    }

    modalShow(
      "删除任务",
      "确定要删除所选的任务吗？",
      (modal) => {
        // 确认按钮点击 - 执行删除操作
        modal.destroy();
        performDeleteSelectedTasks();
      },
      () => {
        // 取消按钮点击
        console.log("用户取消删除任务");
      },
      false,
      {
        position: 'bottom',
        okBtnText: '确定',
        cancelBtnText: '取消',
        okBtnStyle: { backgroundColor: '##382FF', color: '#fff' },
        cancelBtnStyle: { backgroundColor: '#f5f5f5', color: '#666' }
      }
    );
  };
  // 全部重试操作
  const handleRetryAll = async () => {
    console.log('全部重试失败任务');

    // 获取所有失败的任务ID
    const failedTaskIds = failedTasks.map(task => task.task_id);

    if (failedTaskIds.length === 0) {
      Toast.show({
        content: '没有可重试的任务',
        duration: 2000,
      });
      return;
    }

    try {
      const response = await controlTask({
        task_id: failedTaskIds,
        command: 'restart'
      });

      if (response.code === 0) {
        console.log('批量重试任务成功:', failedTaskIds);
        Toast.show({
          content: `已重试 ${failedTaskIds.length} 个任务`,
          duration: 2000,
        });
        // 操作成功后立即刷新数据
        await fetchActiveTask();
        await fetchFailedTask();
      } else {
        console.error('批量重试任务失败:', response.result || '未知错误');
        Toast.show({
          content: `重试失败: ${response.result || '未知错误'}`,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('批量重试任务异常:', error);
      Toast.show({
        content: '重试失败，请重试',
        duration: 2000,
      });
    }
  };

  // 获取任务的操作按钮
  const getTaskActionButton = (task: TaskInfo) => {
    if (task.status === 'running' || task.status === 'waiting') {
      // 正在运行或等待中 - 显示暂停按钮
      return (
        <button
          className={styles.pauseButton}
          onClick={(e) => {
            e.stopPropagation();
            handleTaskAction(task.task_id, 'pause');
          }}
        >
          <img src={pause} alt="暂停" style={{ width: '16px', height: '16px' }} />
        </button>
      );
    } else if (task.status === 'paused') {
      // 已暂停 - 显示开始按钮
      return (
        <button
          className={styles.playButton}
          onClick={(e) => {
            e.stopPropagation();
            handleTaskAction(task.task_id, 'resume');
          }}
        >
          <img src={start} alt="开始" style={{ width: '16px', height: '16px' }} />
        </button>
      );
    }
    // 其他状态不显示按钮
    return null;
  };

  // 获取失败任务的重试按钮
  const getRetryButton = (task: TaskInfo) => {
    return (
      <button
        className={styles.retryButton}
        onClick={(e) => {
          e.stopPropagation();
          handleTaskAction(task.task_id, 'retry');
        }}
        title="重试"
      >
        <img src={restart} alt="" style={{ width: '16px', height: '16px' }} />
      </button>
    );
  };

  // 执行清空已完成记录的实际操作
  const performClearCompletedTasks = async () => {
    try {
      const completedTaskIds = completedTasks.map(task => task.task_id);
      const response = await deleteTask({
        selector: [
          {
            key: 'task_id',
            value: completedTaskIds
          }
        ]
      });

      if (response.code === 0) {
        console.log('清空已完成任务成功:', completedTaskIds);
        Toast.show({
          content: `已清空 ${completedTaskIds.length} 个已完成任务`,
          duration: 2000,
        });

        // 操作成功后立即刷新数据
        await fetchCompletedTask();
      } else {
        console.error('清空已完成任务失败:', response.result || '未知错误');
        Toast.show({
          content: `清空失败: ${response.result || '未知错误'}`,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('清空已完成任务异常:', error);
      Toast.show({
        content: '清空失败，请重试',
        duration: 2000,
      });
    }
  };

  // 清空已完成记录 - 显示确认弹窗
  const handleClearCompletedTasks = () => {
    if (completedTasks.length === 0) {
      Toast.show({
        content: '没有已完成的任务需要清空',
        duration: 2000,
      });
      return;
    }

    modalShow(
      "清空记录",
      "仅清空记录，不会删除已下载的文件",
      (modal) => {
        // 确认按钮点击 - 执行清空操作
        modal.destroy();
        performClearCompletedTasks();
      },
      () => {
        // 取消按钮点击
        console.log("用户取消清空记录");
      },
      false,
      {
        position: 'bottom',
        okBtnText: '确定',
        cancelBtnText: '取消',
        okBtnStyle: { backgroundColor: '#3482FF', color: '#fff' },
        cancelBtnStyle: { backgroundColor: '#f5f5f5', color: '#666' }
      }
    );
  };

  // 错误状态 - 只在没有任何数据且确实有错误时显示
  if (hasError && downloadingTasks.length === 0 && failedTasks.length === 0 && completedTasks.length === 0) {
    return (
      <EmptyState
        title="加载失败"
        description="网络连接异常，请检查网络后重试"
        buttonText="重试"
        onButtonClick={() => {
          fetchActiveTask();
          fetchFailedTask();
          fetchCompletedTask();
        }}
      />
    );
  }

  // 如果没有任何任务数据，显示空状态
  if (downloadingTasks.length === 0 && failedTasks.length === 0 && completedTasks.length === 0) {
    return (
      <EmptyState />
    );
  }

  return (
    <div className={styles.unifiedContainer}>
      {/* 正在下载部分 */}
      {downloadingTasks.length > 0 && (
        <div className={styles.sectionContainer}>
          <div className={styles.header}>
            <Badge>
              <span>
                进行中 | {downloadingTasks.filter((task) => ['waiting', 'running', 'paused'].includes(task.status)).length} 项
              </span>
            </Badge>
            {isMultiSelect ? (
              <span
                className={styles.clearText}
                onClick={handleDownloadingSelectAll}
              >
                全选
              </span>
            ) : (
              (() => {
                const buttonState = getBatchButtonState();
                if (!buttonState) return null;

                return (
                  <div onClick={handleBatchAction} className={styles.retryButton}>
                    {/* <span >
                      <img src={buttonState.icon} alt="" style={{ width: '16px', height: '16px' }} />

                    </span> */}
                    <span className={styles.clearText}>{buttonState.text}</span>
                  </div>
                );
              })()
            )}
          </div>
          {downloadingTasks.map((task) => (
            <List.Item
              key={task.task_id}
              className={styles.taskItem}
              arrow={false}
              onClick={() => isMultiSelect && toggleTaskSelection(task.task_id)}
            >
              <div
                className={styles.taskAll}
                onContextMenu={(e) => {
                  e.preventDefault();
                  handleLongPress(task.task_id);
                }}
                onTouchStart={() => {
                  let pressTimer = setTimeout(() => {
                    handleLongPress(task.task_id);
                  }, 500);

                  // 清除计时器
                  document.addEventListener('touchend', () => {
                    clearTimeout(pressTimer);
                  }, { once: true });
                }}
              >
                <img src={getTaskManagerFileIconForApp(task.src?.[0] || '')} alt="" className={styles.folderIcon} />

                <div className={styles.taskMiddle}>
                  <span className={styles.taskName}>{getTaskDisplayName(task)}</span>

                  <ProgressBar
                    percent={task.detail?.progress || 0}
                    className={styles.progressBar}
                                        style={{
                                          '--fill-color': getStatusColor(task.status),
                                        } as React.CSSProperties}
                  />

                  <div className={styles.bottomRow}>
                    <span className={styles.fileSize}>
                      {task.detail ? `${formatFileSize(task.detail.handle_size)} / ${formatFileSize(task.detail.total_size)}` : '计算中...'}
                    </span>

                    <span className={styles.statusText}>
                      {task.status === "running" && task.detail?.speed ? task.detail.speed : getTaskStatusText(task.status)}
                    </span>
                  </div>
                </div>

                <div className={styles.taskRight}>
                  {isMultiSelect ? (
                    <Checkbox
                      checked={selectedTasks.includes(task.task_id)}
                      onChange={(checked) => {
                        if (checked) {
                          setSelectedTasks([...selectedTasks, task.task_id]);
                        } else {
                          setSelectedTasks(selectedTasks.filter(id => id !== task.task_id));
                        }
                      }}
                      onClick={(e) => e.stopPropagation()}
                    />
                  ) : (
                    getTaskActionButton(task)
                  )}
                </div>
              </div>
            </List.Item>
          ))}
        </div>
      )}

      {/* 失败任务部分 */}
      {failedTasks.length > 0 && (
        <div className={styles.sectionContainer}>
          <div className={styles.header}>
            <span>
              已失败 | {failedTasks.length} 项
            </span>
            {isMultiSelect ? (
              <span
                className={styles.clearText}
                onClick={handleFailedSelectAll}
              >
                全选
              </span>
            ) : (
              <div onClick={handleRetryAll} className={styles.retryButton}>
                <span className={styles.clearText}>全部重试</span>
              </div>
            )}
          </div>
          {failedTasks.map((task) => (
            <List.Item
              key={task.task_id}
              className={styles.taskItem}
              arrow={false}
              onClick={() => isMultiSelect && toggleTaskSelection(task.task_id)}
            >
              <div
                className={styles.taskAll}
                onContextMenu={(e) => {
                  e.preventDefault();
                  handleLongPress(task.task_id);
                }}
                onTouchStart={() => {
                  let pressTimer = setTimeout(() => {
                    handleLongPress(task.task_id);
                  }, 500);

                  // 清除计时器
                  document.addEventListener('touchend', () => {
                    clearTimeout(pressTimer);
                  }, { once: true });
                }}
              >
                <img src={getTaskManagerFileIconForApp(task.src?.[0] || '')} alt="" className={styles.folderIcon} />

                <div className={styles.taskMiddle}>
                  <span className={styles.taskName}>{getTaskDisplayName(task)}</span>

                  <ProgressBar
                    percent={0}
                    className={styles.progressBar}
                    style={{
                      '--fill-color': getStatusColor(task.status),
                    } as React.CSSProperties}
                  />

                  <div className={styles.bottomRow}>
                    <span className={styles.fileSize}>
                      {task.detail ? `${formatFileSize(task.detail.handle_size)} / ${formatFileSize(task.detail.total_size)}` : '计算中...'}
                    </span>

                    <span className={styles.statusText} style={{ color: '#666', fontFamily: "MiSans" }}>
                      任务异常
                    </span>
                  </div>
                </div>

                <div className={styles.taskRight}>
                  {isMultiSelect ? (
                    <Checkbox
                      checked={selectedTasks.includes(task.task_id)}
                      onChange={(checked) => {
                        if (checked) {
                          setSelectedTasks([...selectedTasks, task.task_id]);
                        } else {
                          setSelectedTasks(selectedTasks.filter(id => id !== task.task_id));
                        }
                      }}
                      onClick={(e) => e.stopPropagation()}
                    />
                  ) : (
                    getRetryButton(task)
                  )}
                </div>
              </div>
            </List.Item>
          ))}
        </div>
      )}

      {/* 已完成部分 */}
      {completedTasks.length > 0 && (
        <div className={styles.sectionContainer}>
          <div className={styles.header}>
            <Badge>
              <span>
                已完成 | {completedTasks.length} 项
              </span>
            </Badge>
            {isMultiSelect ? (
              <span
                className={styles.clearText}
                onClick={handleCompletedSelectAll}
              >
                全选
              </span>
            ) : (
              <div onClick={handleClearCompletedTasks} style={{ cursor: 'pointer' }}>
                <Badge>
                  <span className={styles.clearText}>清空记录</span>
                </Badge>
              </div>
            )}
          </div>
          {completedTasks.map((task) => (
            <List.Item
              key={task.task_id}
              className={styles.taskItem}
              arrow={false}
              onClick={() => isMultiSelect && toggleTaskSelection(task.task_id)}
            >
              <div
                className={styles.taskAll}
                onContextMenu={(e) => {
                  e.preventDefault();
                  handleLongPress(task.task_id);
                }}
                onTouchStart={() => {
                  let pressTimer = setTimeout(() => {
                    handleLongPress(task.task_id);
                  }, 500);

                  // 清除计时器
                  document.addEventListener('touchend', () => {
                    clearTimeout(pressTimer);
                  }, { once: true });
                }}
              >
                <img src={getTaskManagerFileIconForApp(task.src?.[0] || '')} alt="" className={styles.folderIcon} />

                <div className={styles.taskMiddle}>
                  <span className={styles.taskName}>{getTaskDisplayName(task)}</span>
                  <div className={styles.bottomRow}>
                    <span className={styles.fileSize}>
                      {task.detail ? formatFileSize(task.detail.total_size) : '未知大小'} | {task.done_time ? formatTime(task.done_time) : '完成时间未知'}
                    </span>
                  </div>
                </div>

                <div className={styles.taskRight}>
                  {isMultiSelect ? (
                    <Checkbox
                      checked={selectedTasks.includes(task.task_id)}
                      onChange={(checked) => {
                        if (checked) {
                          setSelectedTasks([...selectedTasks, task.task_id]);
                        } else {
                          setSelectedTasks(selectedTasks.filter(id => id !== task.task_id));
                        }
                      }}
                      onClick={(e) => e.stopPropagation()}
                    />
                  ) : (
                    <span className={styles.storageLocation}>{formatStorageLocation(task.dst)}</span>
                  )}
                </div>
              </div>
            </List.Item>
          ))}
        </div>
      )}
    </div>
  );
});

export default DownloadTask;