.container {
  background-color: var(--background-color);
  // padding: 0 10px;
  min-height: calc(100vh - 35px);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
  }

  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px 20px;
  }

  .mainList {
    --border-inner: 0;
    --border-bottom: 0;
    --border-top: 0;
    padding-bottom: 36px;
    background-color: var(--background-color);
    height: calc(100vh - 157px);
    scrollbar-width: none;
    overflow-y: auto;
    :global {
      .adm-switch.adm-switch-checked .adm-switch-checkbox {
        background-color: var(--primary-color);
      }
      a.adm-list-item:active:not(.adm-list-item-disabled) {
        background-color: var(--background-color);
      }
    }

    .sectionTitle {
      color: #8c93b0;
      font-size: 12px;
      padding: 8px 22px;
      background-color: var(--background-color);
    }

    .configItem {
      --padding-left: 20px;
      --padding-right: 16px;
      background-color: var(--background-color);

      &:last-child {
        border-bottom: none;
      }

      .itemContent {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .itemLabel {
          font-size: 16px;
          color: var(--text-color);
          font-weight: 400;
        }

        .itemValue {
          font-size: 14px;
          color: var(--list-value-text-color);
        }
      }
    }
  }

  .thinLine {
    position: relative;
    margin: 0 16px;
  }

  .thinLine::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background: var(--thinLine-background-color);
    transform: scaleY(0.5);
    transform-origin: 0 100%;
  }
}

.popoverContent {
  max-width: 240px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px 0;
  background: var(--background-color);
  border-radius: 8px;

  .optionItem {
    display: flex;
    align-items: center;
    padding: 12px 16px;

    &:active {
      background-color: #f5f5f5;
    }

    .optionRadio {
      --icon-size: 18px;
      --font-size: 16px;
      --gap: 12px;
    }

    .optionLabel {
      font-size: 16px;
      color: var(--text-color);
      margin-left: 12px;
    }
  }
}

.thresholdPopup {
  padding: 16px 30px;
  background-color: var(--modal-background-color);
  border-radius: 16px;

  .popupHeader {
    margin-bottom: 24px;

    .popupTitle {
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      color: var(--text-color);
    }
  }

  .popupBody {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .thresholdInput {
      width: 100%;
      height: 44px;
      border-radius: 16px;
      box-sizing: border-box;
      padding: 0 16px;
      font-size: 16px;
      text-align: center;
      background-color: var(--cancel-btn-background-color);
      :global {
        .adm-input-element {
          color: var(--text-color);
        }
      }

      &:focus {
        border-color: #1677ff;
        outline: none;
      }
    }

    .popupButtons {
      display: flex;
      gap: 12px;
      padding: 0 12px;

      .cancelButton {
        flex: 1;
        border-radius: 30px;
        background-color: var(--cancel-btn-background-color);
        color: var(--title-color);
        border: none;
      }

      .confirmButton {
        flex: 1;
        background-color: var(--primary-color);
        border-radius: 30px;
        color: #fff;
        border: none;
      }
    }
  }
}

.capacityPopup {
  padding: 16px 30px;
  background-color: var(--modal-background-color);
  border-radius: 16px;
  .popupHeader {
    margin-bottom: 24px;

    .popupTitle {
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      color: var(--text-color);
    }
  }

  .popupBody {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .capacityInputContainer {
      display: flex;
      align-items: center;
      gap: 12px;
      :global {
        .adm-input-element {
          color: var(--text-color);
        }
      }

      .capacityInput {
        flex: 1;
        height: 44px;
        // border: 1px solid #eee;
        border-radius: 16px;
        box-sizing: border-box;
        padding: 0 16px;
        font-size: 16px;
        text-align: center;
        background-color: var(--cancel-btn-background-color);

        &:focus {
          border-color: #1677ff;
          outline: none;
        }
      }

      .unitDisplay {
        display: flex;
        align-items: center;
        padding: 0 16px;
        height: 44px;
        // border: 1px solid #eee;
        border-radius: 16px;
        font-size: 16px;
        color: var(--text-color);
        cursor: pointer;
        min-width: 60px;
        justify-content: space-between;
        background-color: var(--cancel-btn-background-color);

        .arrowIcon {
          margin-left: 8px;
        }

        &:active {
          background-color: #f5f5f5;
        }
      }
    }

    .popupButtons {
      display: flex;
      gap: 12px;
      padding: 0 12px;

      .cancelButton {
        flex: 1;
        border-radius: 30px;
        background-color: var(--cancel-btn-background-color);
        color: var(--title-color);
        border: none;
      }

      .confirmButton {
        flex: 1;
        background-color: var(--primary-color);
        border-radius: 30px;
        color: #fff;
        border: none;
      }
    }
  }
}

.customArrow {
  width: 16px;
  height: 16px;
}
