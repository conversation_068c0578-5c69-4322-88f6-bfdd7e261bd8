import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import FilmCard, { IFilmCard } from "@/components/FATWall_APP/FilmCard";
import styles from '../RecentlyPlay/index.module.scss';
import useRowDragScroll from "@/hooks/useDragScroll";
import { px2rem } from "@/utils/setRootFontSize";
import { PreloadImage } from "@/components/Image";
import refreshIcon from '@/Resources/layout/refreshBtn.png';
import refreshIcon_dark from '@/Resources/layout/refreshBtn_dark.png';
import { filmAndTvProps, getRecentlyAdd } from "@/api/fatWall";
import { useInViewport } from "ahooks";
import FATErrorComponents from "../../Error";
import { formatTimeAgo } from "@/pages/FATWall/FATWall_APP/Recently/RecentlyPlay";
import { useHistory } from 'react-router-dom';
import { Divider } from "antd";
import { useTheme } from "@/utils/themeDetector";
import { defaultPageParam } from "@/pages/FATWall/FATWall_APP/Recently";
import { useLibraryList } from "../..";

const RowDragWrapper = (props: { item: IFilmCard[], onCardClick: (item: IFilmCard) => void }) => {
  const { rowRef, handleMouseDown, isDragging } = useRowDragScroll();

  return (
    <div className={styles.film_cards_container} ref={rowRef} onMouseDown={handleMouseDown} style={{ cursor: isDragging ? 'grabbing' : 'grab' }}>
      {
        props.item.map((it, index) => (
          <div key={it.title + index} className={styles.film_card_container} onClick={() => props.onCardClick(it)}>
            <FilmCard {...it} key={it.title} options={{ style: { width: px2rem('170px'), height: px2rem('95px') } }} />
          </div>
        ))
      }
    </div >
  )
}

const RecentlyAdd = () => {
  const [recentlyAddFilm, setRecentlyAddFilm] = useState<filmAndTvProps>({ medias: [], count: 0 });
  const [isError, setIsError] = useState<boolean>(false); // 查询是否出错，用于显示刷新按钮

  // 加载更多的必要参数
  const [pageOpt, setPageOpt] = useState<{ offset: number, limit: number }>(defaultPageParam); // 分页参数
  const prevParams = useRef({ ...pageOpt });
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(true);

  // 最近播放和添加的电影列表数据
  const addedRun = useCallback(async (callback: (v: filmAndTvProps) => void, pageOpt) => {
    const data = await getRecentlyAdd(pageOpt).catch(e => {
      console.log('获取最近观看失败：', e);
      setIsError(true);
    })

    if (data && data.code === 0 && data.data) {
      callback(data.data);
      setIsError(false);

      // 判断是否还有更多数据可以加载
      if (data.data.count < pageOpt.limit) {
        setHasMore(false);
      }
    }
  }, []);

  const libs = useLibraryList().libs;

  const recentlyAddFilmList: IFilmCard[] = useMemo(() => {
    return recentlyAddFilm.medias.map((media) => {
      const time = formatTimeAgo(media.create_time || 0);
      const poster = media.poster.length > 0 ? media.poster.length > 1 ? media.poster[1] : media.poster[0] : ''; // 数组0索引为封面图，1索引为海报
      return {
        ...media, poster: poster, media_id: media.media_id, progress: media.last_seen_percent, title: media.trans_name, time: `${time}`, type: 'add', year: `${media.year}`
      }
    })
  }, [recentlyAddFilm])

  useEffect(() => {
    addedRun((data) => {
      setRecentlyAddFilm(p => {
        const newData = [...p.medias, ...data.medias];
        return { count: p.count + data.count, medias: newData };
      });
    }, pageOpt);
  }, [addedRun, pageOpt])

  const recentlyAddListByTime = useMemo(() => {
    let obj: { [key: string]: IFilmCard[] } = {
      sevenDays: [],
      oneMonths: [],
      threeMonths: []
    };
    recentlyAddFilmList.forEach((it: any) => {
      const now = Math.floor(Date.now() / 1000);
      const seconds = now - it.create_time;
      const minutes = seconds / 60;
      const hours = minutes / 60;
      const time = hours / 24; // 天数

      if (time <= 7) {
        obj['sevenDays'].push(it);
      } else if (time > 7 && time <= 31) {
        obj['oneMonths'].push(it);
      } else {
        obj['threeMonths'].push(it);
      }
    })
    return obj;
  }, [recentlyAddFilmList])

  const timeLabel: { [key: string]: string } = {
    sevenDays: '近7天',
    oneMonths: '近1个月',
    threeMonths: '近3个月'
  }

  const clearAndRefresh = useCallback(() => {
    setHasMore(true);
    setRecentlyAddFilm({ medias: [], count: 0 });
    setPageOpt(defaultPageParam);

    if (prevParams.current) {
      const { limit, offset } = prevParams.current;
      if (limit === defaultPageParam.limit && offset === defaultPageParam.offset) {
        addedRun((v) => setRecentlyAddFilm(v), defaultPageParam);
      }
    }
    prevParams.current = defaultPageParam;
  }, [addedRun])

  const history = useHistory();

  // 处理卡片点击跳转，只传递media_id
  const handleCardClick = useCallback((item: IFilmCard) => {
    console.log('RecentlyAdd卡片点击:', item);

    // 将参数拼接到URL上，以便webView能够正确获取
    const params = new URLSearchParams({
      classes: (item as any).classes || '',  // 使用any类型转换，因为IFilmCard类型没有classes属性
      media_id: item.media_id?.toString() || '0',
      lib_id: '0'
    });
    
    history.push(`/filmAndTelevisionWall_pc/all/videoDetails?${params.toString()}`);
  }, [history])

  const { isDarkMode } = useTheme();

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.2,
  })

  useEffect(() => {
    if (inViewport) {
      setPageOpt((prev) => {
        prevParams.current = { ...prev, offset: prev.offset + prev.limit };
        return { ...prev, offset: prev.offset + prev.limit }
      })
    }
  }, [inViewport])

  return (
    <div className={styles.component_container}>
      <div className={styles.header}>
        <div className={styles.util_items}>
          <PreloadImage src={isDarkMode ? refreshIcon_dark : refreshIcon} alt="refresh" onClick={clearAndRefresh} />
        </div>
      </div>
      <FATErrorComponents span={isError ? '获取失败' : '暂无内容'} canTry={isError} refresh={clearAndRefresh} show={isError || libs.length === 0 || recentlyAddFilmList.length === 0} subSpan={libs.length > 0 && recentlyAddFilmList.length === 0 ? '请在媒体库关联的文件夹中添加视频' : undefined}>
        {
          Object.keys(recentlyAddListByTime).map((key) => {
            if (recentlyAddListByTime[key].length > 0) {
              return <div key={key}>
                <div key={key} className={styles.container}>
                  <span className={styles.time_title}>{timeLabel[key]}</span>
                  <RowDragWrapper item={recentlyAddListByTime[key]} onCardClick={handleCardClick} />
                </div>
                {
                  key === 'sevenDays' && (recentlyAddListByTime['oneMonths'].length > 0 || recentlyAddListByTime['threeMonths'].length > 0) ? <Divider style={{ width: `calc(100% - ${px2rem('56px')})`, margin: `${px2rem('8px')} auto` }} /> :
                    key === 'oneMonths' && recentlyAddListByTime['threeMonths'].length > 0 ? <Divider style={{ width: `calc(100% - ${px2rem('56px')})`, margin: `${px2rem('8px')} auto` }} /> : <></>
                }
              </div>
            }
            return null;
          })
        }

        {
          hasMore && recentlyAddFilm.count >= pageOpt.limit && <div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>
        }
      </FATErrorComponents>
    </div>
  )
}

export default RecentlyAdd;