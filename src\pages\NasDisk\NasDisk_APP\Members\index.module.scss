.membersContainer {
  background-color: var(--bg-color);
}

.content {
  padding: 0 24px;
}

// 用户信息区域
.userSection {
  display: flex;
  align-items: center;
  padding: 24px 0;

  .avatar {
    width: 64px;
    height: 64px;
    border-radius: 12px;
    margin-right: 16px;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .username {
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }
}

// 账号信息区域
.infoSection {
  margin-bottom: 32px;

  .infoItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    padding: 16px 0;


    &:last-child {
      border-bottom: none;
    }

    .infoLabel {
      font-size: 17px;
      color: #000;
      font-family: MiSans;
      font-weight: 500;
    }

    .infoValue {
      font-size: 14px;
      color: #000;
      font-weight: 400;
    }

    .memberStatus {
      font-size: 14px;
      color: #999;
      font-weight: 400;
    }

    .unbindArrow {
      font-size: 16px;
      color: #999;
    }
  }
}

// 开通会员按钮
.buttonContainer {
  position: fixed;
  bottom: 100px;
  left: 24px;
  right: 24px;

  .memberButton {
    height: 45px;
    color: #E2AE1E;
    border-radius: 24px;
    background-color: #402C00;
    border: none;
    font-size: 16px;
    font-weight: 600;

    &:active {
      background-color: #6B4220;
    }
  }
}



