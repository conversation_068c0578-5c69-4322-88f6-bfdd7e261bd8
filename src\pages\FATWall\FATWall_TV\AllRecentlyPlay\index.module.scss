.container {
  width: 100%;
  background-color: var(--text-color);
  overflow: auto;
  display: flex;
  flex-direction: column;

  .header {
    padding: 85px 120px;

    span {
      font-family: MiSans;
      font-weight: 500;
      font-size: 48px;
      line-height: 100%;
      letter-spacing: 0px;
      color: var(--background-color);
    }
  }

  .file_container {
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 20px 120px;

    .time_title {
      color: rgb(255, 255, 255, 0.4);
      font-family: MiSans;
      font-size: 36px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .file_content {
      display: grid;
      grid-template-columns: repeat(4, 395px);
      gap: 35px;
    }
  }
}
