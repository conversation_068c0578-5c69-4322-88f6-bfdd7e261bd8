body {
  :global {
    .ant-modal {
      padding-bottom: 0px;
      // max-width: 100vw !important;
    }
  }
}

.modal_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  // width: calc(100% - 32px);
  z-index: 1001;
  user-select: none;
  touch-action: none;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease-in-out;

  .eventList_container {
    background-color: var(--background-color);
  }
  :global {
    .ant-modal-content {
      padding: 0 !important;
      background-color: var(--modal-content-background-color);
      border-radius: 32px;
    }
    .ant-modal-header {
      background-color: var(--modal-content-background-color);
      margin-bottom: 0;
      border-radius: 32px;
    }
    .ant-modal-footer {
      margin: 0;
    }
  }
}

.bottom {
  align-items: flex-end;
}

.modal_container {
  position: absolute;
  width: calc(100% - 48px);
  // width: 100%;
  min-width: 40%;
  background-color: var(--modal-content-background-color);
  border-radius: 20px;
  box-sizing: border-box;
}

.modal_top {
  display: flex;
  flex-direction: row;
  align-items: center;
  // width: calc(100% - 32px);
  width: 100%;
  height: 56px;
  padding: 8px 16px;
}

.modal_top_left {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 12px;
  min-width: 120px;
}

.modal_top_right {
  width: 200px;
  height: 40px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;

  img {
    width: 40px;
    height: 40px;
  }
}

.modal_top_title {
  font-family: MiSans;
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0px;
  text-align: center;
  flex: 1;
  color: var(--text-color);
}

.modal_top_close {
  cursor: pointer;
  display: flex;
  align-items: center;

  img {
    height: 28px;
  }
}

.modal_content {
  // width: calc(100% - 32px);
  max-width: calc(100vw - 32px);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;

  /* 新增滚动关键属性 */
  overflow-y: auto;
  /* 启用垂直滚动 */
  -webkit-overflow-scrolling: touch;
  /* 启用iOS平滑滚动 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE/Edge */
}

.modal_content::-webkit-scrollbar {
  width: 0;
  height: 0;
  background: transparent;
}

.modal_footer {
  padding: 12px;
  display: flex;
  justify-content: center;
}

.modal_footer_btns_confirm,
.modal_footer_btns_cancel {
  // width: 160px;
  height: 50px;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  border-radius: 24px;
  cursor: pointer;
  color: var(--text-color);
}
