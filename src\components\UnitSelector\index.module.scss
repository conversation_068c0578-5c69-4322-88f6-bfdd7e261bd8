.container {
  background-color: var(--modal-background-color);
  padding: 16px 0;

  .header {
    text-align: center;
    margin-bottom: 16px;
    padding: 0 16px;

    .title {
      font-size: 17px;
      font-weight: 500;
      color: var(--text-color);
    }
  }

  .optionList {
    max-height: 70vh;
    overflow-y: auto;

    .optionItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .optionLabel {
        font-size: 16px;
        color: var(--text-color);
      }

      .checkIcon {
        width: 22px;
        height: 22px;
      }
    }
  }

  .footer {
    display: flex;
    padding: 16px;
    border-top: 1px solid #f5f5f5;
    margin-top: 16px;

    .cancelButton,
    .confirmButton {
      flex: 1;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      border-radius: 22px;
    }

    .cancelButton {
      margin-right: 8px;
      background-color: #f5f5f5;
      color: var(--text-color);
    }

    .confirmButton {
      margin-left: 8px;
      background-color: #32bac0;
      color: white;
    }
  }
}
