.cameraPlayer_eventOperate {
  padding: 15px 28px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.cameraPlayer_eventOperate_left {
  height: 30px;
  display: flex;
  align-items: center;
  color: rgba(140, 147, 176, 1);
  font-weight: 500;
  cursor: pointer;
  font-size: 12px;

  img {
    width: 20px;
    height: 20px;
  }
}

.cameraPlayer_eventOperate_center {
  flex: 1;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.cameraPlayer_eventOperate_right {
  height: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 30px;
    height: 30px;
  }
}

.identify_users_container {
  position: relative;
  display: flex;
  flex-direction: row;
  height: 30px;
  border-radius: 15px;
  /* background-color: #FFF; */
  cursor: pointer;
}

.identify_users_content {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  border: 1px solid #FFF;
  position: absolute;

  img {
    width: 26px;
    height: 26px;
  }
}

.eventFilterModal_container {
  padding-top: 10px;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none;
}

.event_userModal_container {
  padding-top: 10px;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none;
}

.eventFilterModal_content {
  height: 60px;
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
  transition: background 0.15s ease-out, transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-color);

  &:active,
  &:hover {
    background-color: var(--card-active-background-color);
  }

  span {
    margin: 0 8px;
    font-weight: 600;
    font-size: 15px;
    display: flex;
    justify-content: center;

    img {
      width: 30px;
      height: 30px;
    }
  }
}

.cameraPlayer_eventOperate_right_eventFilter {
  width: 45px;
  height: 30px;
  cursor: pointer;
  background-color: var(--card-background-color);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  img {
    width: 20px;
    height: 18px;
  }
}

.filter_selected {
  background-color: var(--card-active-background-color);
}