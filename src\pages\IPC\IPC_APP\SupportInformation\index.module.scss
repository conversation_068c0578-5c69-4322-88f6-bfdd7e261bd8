.container {
  height: 100%;
  padding: 0 8px;
  background-color: var(--background-color);
  min-height: calc(100vh - 35px);

  .backIcon {
    width: 40px;
    height: 40px;
    margin-left: 10px;
  }
}

.title {
  font-size: 32px;
  font-weight: 400;
  color: var(--title-color);
  padding: 10px 16px;
}

.content {
  flex: 1;
  height: calc(100% - 157px);
  scrollbar-width: none;
  overflow-y: auto;
}

.brandSection {
  margin-top: 16px;
}

.brandName {
  font-size: 14px;
  color: #8c93b0;
  padding: 0 14px 16px;
}

.cameraList {
  --border-inner: 0;
  --border-bottom: 0;
  --border-top: 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--background-color);
  padding: 0 12px;
  :global {
    .adm-list-item {
      padding-left: 0;
      background-color: var(--background-color);
    }
    .adm-list-item-content {
      padding-right: 0;
    }
    .adm-list-body {
      background-color: var(--background-color);
    }
  }
}

.cameraItem {
  --arrow-color: #999;
  position: relative;
  min-height: 56px;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 16px;
    right: 16px;
    height: 1px;
  }

  &:last-child::after {
    display: none;
  }
}

.itemContent {
  display: flex;
  align-items: center;
  width: 100%;
}

.cameraIcon {
  width: 37px;
  height: 37px;
  margin-right: 12px;
  flex-shrink: 0;
}

.cameraName {
  flex: 1;
  font-size: 16px;
  color: var(--title-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 24px;
  font-weight: 600;
}

// 空状态样式
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  min-height: 300px;
}

.emptyText {
  font-size: 16px;
  color: var(--text-secondary-color);
  margin-top: 16px;
}
