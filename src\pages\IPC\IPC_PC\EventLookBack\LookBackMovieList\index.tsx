import MonitorPlayer, { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer"
import styles from "./index.module.scss";
import { useState } from "react";
import Modal from "@/components/Modal";
import { PreloadImage } from "@/components/Image";
import { px2rem } from "@/utils/setRootFontSize";
import { eventLookBackData, eventLookBackDataType } from "..";

interface ILookBackMovieList {
  data: eventLookBackData[] // 全部事件数据
  curData?: eventLookBackDataType //当前事件数据，控制播放中的视频
  isShow: boolean;
  setIsShow: (v: boolean) => void;
  // 自定义操作按钮，用于不同入口的不同操作
  customOperations?: Array<{
    label: string;
    name: string;
    navBarIcon: string;
    icon: string;
    color?: string;
    onClick: (data?: eventLookBackDataType) => void;
  }>;
}

const Content = (props: Omit<ILookBackMovieList, 'isShow'> & { width: string, height: string }) => {
  const { data, curData, width, height, setIsShow, customOperations } = props;
  const [active, setActive] = useState<number>(curData?.id || 0);

  return (
    <div className={styles.container} style={{ height: height }}>
      <div className={styles.left}>
        <MonitorPlayer videoContainerStyle={{
          borderRadius: customOperations ? px2rem('32px') : `${px2rem('32px')} 0 0 ${px2rem('32px')}`,
          width: px2rem(width),
          height
        }} baseConfig={{
          width: width, url: curData?.movieUrl || '',
          type: "Movie", mediaName: "lookBackList"
        }}
          options={{
            type: 'modal',
            title: curData?.eventName ? `${curData?.eventTime} ${curData?.eventName}` : '',
            onClose: () => setIsShow(false),
            customOperations: customOperations?.map(op => ({
              ...op,
              onClick: () => op.onClick(curData)
            }))
          }} />
      </div>
      {!customOperations && (
        <div className={styles.right}>
          {
            data.map((item) => (
              <div className={styles.right_container} key={item.date}>
                <div className={styles.listLabel}>{item.date}</div>
                {
                  item.data.map((it) => (
                    <div className={styles.previewImage} onClick={() => setActive(it.id)} key={it.id}>
                      <PreloadImage src={splitURL(it.url)} alt="previewImage" needHeader={true} />
                      <div className={styles.activeText} style={{ display: it.id === active ? 'flex' : 'none' }}>播放中</div>
                    </div>
                  ))
                }
              </div>
            ))
          }
        </div>
      )}
    </div>
  )
}

const LookBackMovieList = (props: ILookBackMovieList) => {
  const { isShow, setIsShow } = props;
  const width = 900;
  const height = px2rem(String(width * 0.5625));
  return (
    <Modal isShow={isShow} onCancel={() => setIsShow(false)}
      content={
        <Content setIsShow={setIsShow} width={`${width}px`} height={height} data={props.data} curData={props.curData} customOperations={props.customOperations} />
      }
      footer={null} contentStyle={{ height: height, padding: 0 }} />
  )
}

export default LookBackMovieList