.container {
  min-height: calc(100vh - 35px);
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
  padding: 0 10px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 6px;
  }
  .close,.outline{
    width: 40px;
    height: 40px;
  }

  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px 14px;
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    .list {
      flex: 1;
      overflow-y: auto;
      .all {
        padding: 16px 14px;
        background-color: var(--background-color);

        .searching {
          display: flex;
          align-items: center;
          gap: 8px;
          color: var(--primary-color);
          font-size: 14px;
        }

        .result {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: var(--primary-color);
          span {
            margin-left: 10px;
          }
        }

        .noDevice {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 60px;
        }

        .noDeviceIcon {
          margin-bottom: 24px;
        }

        .placeholderCircle {
          border-radius: 50%;
        }

        .noDeviceText {
          font-size: 20px;
          color: #000;
          font-weight: 500;
        }

        .viewSupportedCameras {
          font-size: 14px;
          color: #599cfa;
          cursor: pointer;
        }

        .errorContainer {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 60px;
        }

        .errorIcon {
          margin-bottom: 24px;
        }

        .errorText {
          font-size: 20px;
          color: #000;
          font-weight: 500;
        }

        .retryBtn {
          background-color: var(--primary-color);
          color: #000;
          font-size: 14px;
          padding: 3px 8px;
        }
      }
    }
  }

  .deviceList {
    --border-inner: 0;
    --border-bottom: 0;
    --border-top: 0;
    background-color: var(--background-color);

    :global {
      .adm-list-item {
        --adm-list-item-prefix-padding-right: 12px;
        .adm-list-item-content-arrow {
          display: none !important;
        }
        padding-left: 4px;
      }
      .adm-checkbox.adm-checkbox-checked .adm-checkbox-icon {
        border-color: var(--primary-color);
        background-color: var(--primary-color);
      }
      a.adm-list-item:active:not(.adm-list-item-disabled){
        background-color: var(--background-color);
      }
    }
    .deviceItem {
      --prefix-width: 60px;
      --padding-left: 16px;
      --padding-right: 16px;
      background-color: var(--background-color);

      .thumbnail {
        width: 44px;
        height: 44px;
        border-radius: 4px;
      }

      .deviceName {
        font-size: 16px;
        color: var(--text-color);
        flex: 1;
        padding-right: 12px;
      }

      .adm-checkbox {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .footer {
    padding: 24px;
    .nextButton {
      --border-radius: 16px;
      height: 48px;
      font-size: 16px;
      background-color: var(--primary-color);
      border: none;

      &:disabled {
        opacity: 0.5;
      }
    }
  }
}
