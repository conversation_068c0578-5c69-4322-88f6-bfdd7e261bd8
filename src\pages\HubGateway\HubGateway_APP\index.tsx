import { useEffect, useState } from 'react';
import { Input, Button, Toast } from 'antd-mobile';
import { Route, Switch, useHistory, useRouteMatch, useLocation } from 'react-router-dom';
import styles from './index.module.scss';
import next_dark from "@/Resources/modal/next_dark.png";
import next from "@/Resources/modal/next.png";
import NavigatorBar from '@/components/NavBar';
import { useTheme } from '@/utils/themeDetector';
import { PreloadImage } from '@/components/Image';
import {
    CopyOutlined
} from '@ant-design/icons';
import Help from './Help';
import Connect from './Connect';
import { gotoHelpCenter } from '@/api/hubGatewayJSBridge';
import { exitWebClient } from '@/api/cameraPlayer';
import { useRequest } from 'ahooks';
import { getCentralInfo } from '@/api/hubGateway';
const HubGateway = () => {
    const history = useHistory();
    const location = useLocation();
    const { path } = useRouteMatch();
    const [ipAddress, setIpAddress] = useState('');
    const [loginCode, setLoginCode] = useState('');
    const { isDarkMode } = useTheme();


    const handleCopyLoginCode = () => {
        if (loginCode) {
            navigator.clipboard.writeText(loginCode).then(() => {
                Toast.show('已复制到剪贴板');
            }).catch(() => {
                Toast.show('复制失败');
            });
        }
    };
    const { run: getCentral, } = useRequest(
        getCentralInfo,
        {
            manual: true,
            onSuccess: (res) => {
                if (res.code === 0 && res.data) {
                    setIpAddress(res.data.url);
                    setLoginCode(res.data.passcode);
                } else {
                    Toast.show({
                        content: res?.result,
                        position: 'bottom',
                        duration: 1500,
                    })
                }
            },
            onError: () => {
                Toast.show({
                    content: '网络异常，请重试',
                    position: 'bottom',
                    duration: 1500,
                });
            },
        }
    );
    useEffect(() => {
        getCentral();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 处理从Connect页面传递过来的数据
    useEffect(() => {
        const state = location.state as { loginCode?: string; ipAddress?: string } | undefined;
        if (state?.loginCode) {
            setLoginCode(state.loginCode);
        }
        if (state?.ipAddress) {
            setIpAddress(state.ipAddress);
        }
        // 清除location state，避免重复处理
        if (state) {
            history.replace(location.pathname, undefined);
        }
    }, [location.state, history, location.pathname]);
    const handleRefreshLoginCode = () => {
        getCentral()
        Toast.show('登录码已刷新');
    };

    const handleConnectStrategy = () => {
        history.push('/hubGateway_app/connect')
    };

    //methodName:gotoHelp的方法
    const handleHelp = () => {
        gotoHelpCenter((res) => {
            console.log(res);
        });
    };
    const handleBackClick = () => {

        exitWebClient().catch((error) => {
            console.error("退出Web客户端失败:", error);
        });
    };


    return (

        <Switch>
            <Route exact path={path}>
                <div className={styles.container}>
                    <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} title='中枢网关' onBack={handleBackClick} />

                    <div className={styles.content}>
                        {/* 自动化极客版 */}
                        <div className={styles.section}>
                            <div className={styles.TitleBox}>
                                <span className={styles.title}>自动化极客版</span>
                                <div onClick={handleConnectStrategy} >
                                    <span className={styles.titleTwo}>连接攻略</span>
                                    <PreloadImage src={isDarkMode ? next_dark : next} style={{ height: '10px', width: '10px' }} alt="next" />
                                </div>

                            </div>
                            {/* IP地址 */}
                            <div className={styles.inputSection}>
                                <div className={styles.titleTwo}>IP地址</div>
                                <div className={`${styles.inputWrapper} ${styles.ipWrapper}`}>
                                    <div className={styles.inputtext}>
                                        {ipAddress}
                                    </div>
                                </div>

                            </div>

                            {/* 登录码 */}
                            <div className={styles.inputSection}>
                                <div className={styles.titleTwo}>登录码</div>
                                <div className={`${styles.inputWrapper} ${styles.codeWrapper}`}>
                                    <div className={styles.inputtext}>
                                        {loginCode}
                                    </div>
                                    <div
                                        className={styles.copyIcon}
                                        onClick={handleCopyLoginCode}
                                    >
                                        <CopyOutlined />
                                    </div>
                                </div>
                            </div>

                            {/* 重新获取登录码按钮 */}
                            <div className={styles.buttonSection}>
                                <Button
                                    color="primary"
                                    size="large"
                                    onClick={handleRefreshLoginCode}
                                    className={styles.refreshButton}
                                >
                                    重新获取登录码
                                </Button>
                            </div>
                        </div>
                    </div>
                    <div className={styles.help} onClick={handleHelp}>
                        {/* 使用帮助 */}
                        <div className={styles.helpSection}>
                            <div className={styles.Boxtwo}>
                                <span className={styles.title}>使用帮助</span>
                                <PreloadImage src={isDarkMode ? next_dark : next} style={{ height: '10px', width: '10px' }} alt="next" />
                            </div>
                        </div>
                    </div>
                </div>
            </Route>
            <Route path={`${path}/help`}>
                <Help />
            </Route>
            <Route path={`${path}/connect`}>
                <Connect />
            </Route>
        </Switch>

    );
};

export default HubGateway;