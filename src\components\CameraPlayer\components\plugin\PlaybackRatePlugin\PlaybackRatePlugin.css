.rate-container {
  position: relative;
  flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 8px 0;
}

.rate-select-container {
  position: absolute;
  width: 30px;
  bottom: 30px;
  padding: 6px 0 0 0;
  background-color: #0000008A;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.rate-select-container.mobile {
  top: -150%
}

.rate-select-span {
  margin-bottom: 4px;
  color: #FFF;
  cursor: pointer;
}

.rate-select-span:hover {
  color: rgba(243, 91, 91, 0.644);
}

.rate-content {
  display: block;
}

.iconBtn_container_img {
  height: 28px;
  transform: scale(0.5);
  cursor: pointer;
}

.iconBtn_container_img.full.android {
  height: 16px;
}

.iconBtn_container_img.full.pc {
  height: 40px;
}

.iconBtn_container_img.notFull.pc {
  height: 28px;
  transform: scale(1);
}

.iconBtn_container_img.pc.dashboard {
  height: 40px;
  transform: scale(1);
}