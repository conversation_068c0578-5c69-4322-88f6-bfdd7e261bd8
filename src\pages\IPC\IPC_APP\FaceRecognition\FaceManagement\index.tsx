import React, { useState } from "react";
import { Popup, Input, Button, Toast } from "antd-mobile";
import { useHistory } from "react-router-dom";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
// import addPeople from "@/Resources/camMgmtImg/add-people.png";
import figure from "@/Resources/camMgmtImg/figure.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import nullData from "@/Resources/camMgmtImg/null-page.png";
import nullDataDark from "@/Resources/camMgmtImg/null-page-dark.png";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import { getFacialInfo, setFacialInfo, FacialInfo } from "@/api/ipc";
import { PreloadImage } from "@/components/Image";
import { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";

const FaceManagementPage = () => {
  const history = useHistory();
  const { isDarkMode } = useTheme();

  const [namePopupVisible, setNamePopupVisible] = useState(false);
  const [faceNameInput, setFaceNameInput] = useState("");
  const [markedFaces, setMarkedFaces] = useState<FacialInfo[]>([]);
  const [unmarkedFaces, setUnmarkedFaces] = useState<FacialInfo[]>([]);
  const [selectedUnmarkedFace, setSelectedUnmarkedFace] =
    useState<FacialInfo | null>(null);

  // 获取人脸信息
  const { run: fetchFaces } = useRequest(getFacialInfo, {
    onSuccess: (res) => {
      if (res && res.code === 0) {
        // 将人脸分为已标记和未标记两组
        const marked: FacialInfo[] = [];
        const unmarked: FacialInfo[] = [];

        res.data.info.forEach((face) => {
          if (face.name && face.name.trim() !== "") {
            marked.push(face);
          } else {
            unmarked.push(face);
          }
        });

        setMarkedFaces(marked);
        setUnmarkedFaces(unmarked);
      } else {
        Toast.show({
          content: res?.result,
        });
      }
    },
    onError: (error) => {
      console.error("获取人脸信息失败:", error);
      Toast.show({
        content: "获取人脸信息失败，使用默认数据",
      });
    },
  });


  // 设置人脸信息
  const { run: saveFaceInfo } = useRequest(setFacialInfo, {
    manual: true,
    onSuccess: (res) => {
      if (res && res.code === 0) {
        Toast.show({
          content: "保存成功",
          position: "bottom",
          duration: 1000,
        });

        // 重新获取人脸列表
        fetchFaces();

        // 关闭弹窗并清空输入
        setNamePopupVisible(false);
        setFaceNameInput("");
        setSelectedUnmarkedFace(null);
      } else {
        Toast.show({
          content: res?.result,
        });
      }
    },
    onError: (error) => {
      console.error("保存人脸信息失败:", error);
      Toast.show({
        content: "保存失败，请重试",
      });
    },
  });

  const handleAddFace = () => {
    setSelectedUnmarkedFace(null);
    setFaceNameInput("");
    setNamePopupVisible(true);
  };

  const handleNameConfirm = () => {
    if (!faceNameInput.trim()) {
      Toast.show({ content: "请输入人脸备注名称", position: "center" });
      return;
    }

    if (selectedUnmarkedFace) {
      // 更新现有的未标记人脸
      saveFaceInfo({
        uuid: selectedUnmarkedFace.uuid,
        name: faceNameInput,
        profile_pic: selectedUnmarkedFace.profile_pic,
        delete: false,
      });
    } else {
      // 仅在添加新人脸时实现
      setNamePopupVisible(false);
    }
  };

  const handleFaceClick = (face: any) => {
    if (face.isAddButton) {
      handleAddFace();
    } else {
      // 跳转到人脸详情页，并传递人脸信息
      history.push({
        pathname: `/cameraManagement_app/faceRecognition/faceDetail/${face.uuid}`, state: {
          face: {
            uuid: face.uuid,
            name: face.name,
            profile_pic: face.profile_pic,
            picture_dir: face.picture_dir,
            video_dir: face.video_dir
          }
        }
      });
    }
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <div className={styles.emptyState}>
      <div className={styles.emptyIcon}>
        <PreloadImage
          src={isDarkMode ? nullDataDark : nullData}
          alt="暂无数据"
        />
      </div>
      <div className={styles.emptyText}>暂无内容</div>
    </div>
  );

  // 检查是否有任何人脸数据
  const hasAnyFaces = markedFaces.length > 0 || unmarkedFaces.length > 0;

  return (
    <div className={styles.container}>
      <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
      <div className={styles.title}>人脸管理</div>

      {/* 如果没有任何人脸数据，显示空状态 */}
      {!hasAnyFaces ? (
        renderEmptyState()
      ) : (
        <>
          <div className={styles.section}>
            {markedFaces.length > 0 && (<div className={styles.sectionTitle}>已标记</div>)}
            <div className={styles.faceGrid}>
              {/* <div className={styles.faceItem} 添加人物展示一期不支持
              //  onClick={handleAddFace}
               >
                <div className={styles.avatarWrapper}>
                  <Image
                    src={addPeople}
                    className={`${styles.avatar} ${styles.addButton}`}
                    fit="cover"
                  />
                </div>
                <div className={styles.faceName}>添加人物</div>
              </div> */}

              {markedFaces.map((face) => (
                <div
                  key={face.uuid}
                  className={styles.faceItem}
                  onClick={() => handleFaceClick(face)}
                >
                  <div className={styles.avatarWrapper}>
                    <PreloadImage
                      src={splitURL(face.profile_pic) || figure}
                      className={styles.avatar}
                      needHeader={true}
                    />
                  </div>
                  <div className={styles.faceName}>{face.name}</div>
                </div>
              ))}
            </div>
          </div>

          {unmarkedFaces.length > 0 && (
            <div className={styles.section}>
              <div className={styles.sectionTitle}>陌生人</div>
              <div className={styles.faceGrid}>
                {unmarkedFaces.map((face) => (
                  <div
                    key={face.uuid}
                    className={styles.faceItem}
                    onClick={() => handleFaceClick(face)}
                  >
                    <div className={styles.avatarWrapper}>
                      <PreloadImage
                        src={splitURL(face.profile_pic) || figure}
                        className={styles.avatar}
                        needHeader={true}
                      />
                    </div>
                    <div className={styles.faceName}>未标记</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </>
      )}

      <Popup
        visible={namePopupVisible}
        onMaskClick={() => setNamePopupVisible(false)}
        position="bottom"
        bodyStyle={{
          borderRadius: "16px",
          margin: "0 12px 12px 12px",
          width: "calc(100% - 24px)",
          maxWidth: "calc(100% - 24px)",
        }}
      >
        <div className={styles.thresholdPopup}>
          <div className={styles.popupHeader}>
            <div className={styles.popupTitle}>
              {selectedUnmarkedFace ? "标记人脸" : "添加人物"}
            </div>
          </div>

          <div className={styles.popupBody}>
            <Input
              className={styles.thresholdInput}
              value={faceNameInput}
              onChange={(value) => setFaceNameInput(value)}
              onFocus={(e) => e.target.select()}
              autoFocus
              clearable
              placeholder="请输入人脸备注名称"
            />

            <div className={styles.popupButtons}>
              <Button
                className={styles.cancelButton}
                onClick={() => setNamePopupVisible(false)}
              >
                取消
              </Button>
              <Button
                className={styles.confirmButton}
                color="primary"
                onClick={handleNameConfirm}
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default FaceManagementPage;
