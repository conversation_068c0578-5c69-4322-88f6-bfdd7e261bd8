import React from 'react';
import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import emptyStateImg from '@/Resources/nasDiskImg/no-file.png';

interface EmptyStateProps {
  /** 空状态图片 */
  imageSrc?: string;
  /** 图片alt文本 */
  imageAlt?: string;
  /** 空状态标题 */
  title?: string;
  /** 空状态描述 */
  description?: string;
  /** 底部提示文本 */
  bottomText?: string;
  /** 按钮文本 */
  buttonText?: string;
  /** 按钮点击回调 */
  onButtonClick?: () => void;
  /** 是否显示按钮 */
  showButton?: boolean;
  /** 自定义容器样式类名 */
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  imageSrc = emptyStateImg,
  imageAlt = '暂无内容',
  description = '没有文件',
  className
}) => {
  return (
    <div className={`${styles.emptyState} ${className || ''}`}>
      <div className={styles.emptyContent}>
        <div className={styles.emptyImage}>
          <PreloadImage src={imageSrc} alt={imageAlt} style={{width:'100px',height:'64px'}} />
        </div>
        {description && (
          <div className={styles.emptyDescription}>{description}</div>
        )}
      </div>

    </div>
  );
};

export default EmptyState;
