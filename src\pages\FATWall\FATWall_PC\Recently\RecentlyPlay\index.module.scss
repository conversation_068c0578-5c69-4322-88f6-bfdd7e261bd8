.component_container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  user-select: none;
}

.container {
  width: 100%;
  position: relative;
}

.header {
  width: 100%;
  height: 60px;
  font-family: MiSans;
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0px;
  color: var(--text-color);
  background-color: var(--background-color);
  position: sticky;
  top: 0;
  z-index: 10;

  .util_items {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 60px;
    padding: 28px;
    gap: 8px;
    font-family: MiSans;
    font-weight: 500;
    font-size: 17px;
    line-height: 100%;
    letter-spacing: 0px;
    vertical-align: middle;

    .operationItem {
      margin: 0 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 12px;
      cursor: pointer;
      color: var(--subtitle-text-color);

      & {
        border: 1px solid var(--list-value-text-color);
        border-radius: 6px;
      }

      img {
        height: 20px;
      }
    }

    img {
      height: 26px;
      cursor: pointer;
    }
  }
}

.time_title {
  display: block;
  font-family: MiSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: var(--subtitle-text-color);
  padding: 8px 28px;
}

.film_cards_container {
  display: flex;
  flex-wrap: wrap;
  gap: 1px;
  width: 100%;
  transition: cursor 0.2s;
  padding: 0 10px;
  margin-bottom: 8px;
  scrollbar-width: none; // 隐藏默认滚动条

  &::-webkit-scrollbar {
    display: none; // 隐藏默认滚动条
  }
}

.select_item {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 4px;
  right: 4px;
  visibility: hidden;
  background-color: var(--modal-content-background-color);
  border-radius: 8px;

  &:hover {
    cursor: pointer;
  }

  img {
    height: 16px;
  }
}

.film_card_container {
  position: relative;
  display: flex;
  justify-content: center;
  transition: transform 0.2s;
  padding: 10px;

  &:active {
    transform: scale(0.98); // 点击反馈
  }
  &:hover {
    background-color: var(--fat-card-hover-bg);
  }
}

.result_not_container {
  width: 100%;
  position: relative;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .result_not_container_span {
    font-family: MiSans W;
    font-weight: 500;
    font-size: 12px;
    line-height: 100%;
    letter-spacing: 0px;
    color: var(--subtitle-text-color);
    margin-bottom: 6px;
  }

  .search_not_result_show_icon {
    width: 100px;
    height: 64px;
    margin-bottom: 18px;
  }
  .content_null_img {
    width: 100px;
    height: 64px;
  }

  .search_buttons_item {
    margin-top: 14px;
    padding: 8.5px 20px;
    background: var(--primary-btn-background-color);
    color: var(--primary-color);
    font-family: MiSans W;
    font-weight: 500;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    border-radius: 73px;
    cursor: pointer;
  }
}
