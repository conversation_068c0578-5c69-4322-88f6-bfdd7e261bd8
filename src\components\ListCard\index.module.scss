.container {
  width: 100%;
  height: 70px;
  display: flex;
  align-items: center;
  // padding: 0 28px;
  background-color: var(--background-color);
}

.left {
  display: flex;
  align-items: center;

  .avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;

    img {
      height: 50px;
      width: 50px;
      border-radius: 25px;
    }
  }

  .left_content {
    display: flex;
    flex-direction: column;
    width: 200px;

    .title {
      font-family: MiSans;
      font-weight: 500;
      white-space: nowrap;    /* 禁止换行 */
      overflow: hidden;       /* 隐藏超出部分 */
      text-overflow: ellipsis; /* 超出显示... */
      font-size: 17px;
      line-height: 100%;
      letter-spacing: 0px;
      color: var(--text-color);
      margin: 4px 0;
      padding: 2px 0;
    }

    .subtitle {
      font-family: MiSans;
      font-weight: 400;
      font-size: 14px;
      line-height: 100%;
      letter-spacing: 0px;
      color: var(--subtitle-text-color);
      padding: 2px 0;
    }
  }
}

.right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  img {
    height: 17px;
  }
}


.morePopoverContainer {
  :global(.adm-popover-inner) {
    border-radius: 15px !important;
  }
}

.morePopover {
  min-width: 160px;
}

.morePopoverItem {
  padding: 5px 5px;
  display: flex;
  flex-direction: column;
  // align-items: center;
  font-weight: 600;
  color: #000000;
  transition: background-color 0.2s;

  &:hover,
  &:active {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:first-child {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
  }

  &:last-child {
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
  }
}

.morePopoverText {
  font-size: 16px;
  color: var(--text-color);
}

.scanningText {
  font-family: MiSans;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #528FE5;
  padding: 2px 0;
}

.scanningFailedText {
  font-family: MiSans;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #FF9C00;
  padding: 2px 0;
}

.scanCompletedText {
  font-family: MiSans;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #32BAC0;
  padding: 2px 0;
}
