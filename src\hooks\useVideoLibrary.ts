import { createContext, useCallback, useEffect, useState } from "react";
import { useRequest } from "ahooks";
import { getFullLibraryData } from "@/api/fatWall";


interface UseLibraryDataProps {
  lib_id: number;
  name: string;
  count?: number;
}

// 获取媒体库列表hook
export const useVideoLibraryList = () => {

  const { runAsync: getLibraryData } = useRequest(getFullLibraryData, { manual: true, }); // 获取所有媒体库

  // 获取媒体库列表
  const [libraries, setLibraries] = useState<UseLibraryDataProps[]>([]);

  const getLib = useCallback(async () => {
    const res = await getLibraryData({ sort_type: 0, asc: 0 }).catch((e) => console.log('查询所有媒体库失败：', e));
    if (res && res.code === 0 && res.data) {
      const myLibs = res.data.my_libs.libs.map((item) => { return { lib_id: item.lib_id, name: item.name, count: item.media_count } });
      const otherLibs = res.data.share2me.libs.map((item) => { return { lib_id: item.lib_id, name: item.name, count: item.media_count } });
      const allLibs = myLibs.concat(otherLibs);
      setLibraries(allLibs);
    }
  }, [getLibraryData])

  useEffect(() => {
    getLib();
  }, [getLib])

  return {
    libraries,
    getLib,
    setLibraries
  }
}

// 媒体库上下文
export const LibraryContext = createContext<{
  libs: UseLibraryDataProps[],
  setLibs: React.Dispatch<React.SetStateAction<UseLibraryDataProps[]>>,
  refreshLibraries: () => Promise<void>
}>({ libs: [], setLibs: (v) => null, refreshLibraries: async () => { } }); // 媒体库列表