.mediaSearchModal {
  :global(.ant-modal-content) {
    padding: 0;
    border-radius: 32px;
    overflow: hidden;
    height: 636px;
    width: 862px;
    background-color: var(--desktop-modal-bg-color);
  }
  :global {
    .ant-input::placeholder {
      color: var(--correction-disable-title-color)
    }
  }
}

.modalWrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.searchContainer {
  padding: 10px 20px 16px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.searchBox {
  flex: 1;
  height: 40px;
  background-color: var(--event-card-background-color);
  border-radius: 20px;
  display: flex;
  align-items: center;
}

.searchIcon {
  color: var(--list-value-text-color);
  margin-right: 8px;
}

.searchInput {
  flex: 1;
  border: none;
  background: transparent;
  height: 100%;
  font-size: 14px;
  outline: none;
  width: 80%;
  color: var(--subtitle-text-color);
}

.searchInput::placeholder {
  color: var(--subtitle-text-color);
}

.clearButton {
  cursor: pointer;
  color: var(--list-value-text-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.searchResults {
  flex: 1;
  overflow-y: auto;
  padding: 0 30px 0 24px;
}

.noResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--list-value-text-color);
}

.noResultsIcon {
  width: 100px;
  height: 64px;
  margin-bottom: 16px;
}

.noResultsText {
  font-size: 14px;
  color: var(--list-value-text-color);
}

.retryButton {
  margin-top: 16px;
  color: var(--primary-color);
  cursor: pointer;
}

.resultItem {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 28px;
}

.modalFooter {
  display: flex;
  background-color: var(--desktop-modal-bg-color);
  padding: 10px;
  color: var(--text-color);
//   width: 50%;
  margin-bottom: 20px;
  margin-left: 20px;
  border-radius: 20px;
  justify-content: space-between;

  > div {
    display: flex;
    gap: 5px;
    padding-right: 15px;
    border-right: 1px solid var(--thinLine-background-color);
    color: #5c6167;
  }
  > div:last-child {
    border-right: none;
  }
}

// 添加选中项目的高亮样式
.selectedItem {
  position: relative;

  &::after {
    content: "";
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    pointer-events: none;
    z-index: 1;
  }
} 