import React, { useState, useEffect, useRef, TouchEvent, WheelEvent } from 'react';
import { CenterPopup, Popup } from 'antd-mobile';
import styles from './index.module.scss';
import { getDeviceType } from '@/utils/DeviceType';

interface TimePickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (time: string[]) => void;
  value: string[];
  title?: string;
}

const TimePicker: React.FC<TimePickerProps> = ({
  visible,
  onClose,
  onConfirm,
  value = ["00", "00"],
  title = "选择时间"
}) => {
  const [currentHour, setCurrentHour] = useState(value[0]);
  const [currentMinute, setCurrentMinute] = useState(value[1]);

  // 滑动相关状态
  const [isTouching, setIsTouching] = useState(false);
  const hourColumnRef = useRef<HTMLDivElement>(null);
  const minuteColumnRef = useRef<HTMLDivElement>(null);
  const touchStartY = useRef<number>(0);
  const touchCurrentType = useRef<'hour' | 'minute' | null>(null);
  const [translateY, setTranslateY] = useState({ hour: 0, minute: 0 });
  const animationInProgress = useRef(false);
  const lastTouchTime = useRef<number>(0);
  const lastTouchY = useRef<number>(0);
  const velocityY = useRef<number>(0);
  const touchMoveDistances = useRef<number[]>([]);
  const touchMoveTimes = useRef<number[]>([]);
  const rafId = useRef<number | null>(null);
  const delayTimerId = useRef<NodeJS.Timeout | null>(null);

  // 新增 PC 端交互逻辑
  const wheelDelta = useRef<number>(0);
  const wheelTimer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (visible) {
      setCurrentHour(value[0]);
      setCurrentMinute(value[1]);
      setTranslateY({ hour: 0, minute: 0 });
    }
  }, [visible, value]);

  // 组件卸载时清理RAF和timer
  useEffect(() => {
    return () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      if (delayTimerId.current) {
        clearTimeout(delayTimerId.current);
      }
    };
  }, []);

  // 生成小时和分钟数组
  const hours = Array.from({ length: 24 }, (_, i) => `${i}`.padStart(2, "0"));
  const minutes = Array.from({ length: 60 }, (_, i) => `${i}`.padStart(2, "0"));

  // 处理时间变更，支持批量变更
  const handleTimeChange = (
    type: 'hour' | 'minute',
    direction: 'prev' | 'next',
    count: number = 1
  ) => {
    if (animationInProgress.current && count === 1) return;

    const isInertiaScroll = count > 1;

    if (!isInertiaScroll) {
      animationInProgress.current = true;
    }

    // 设置动画初始状态，惯性滚动不需要这一步
    if (!isInertiaScroll) {
      setTranslateY(prev => ({
        ...prev,
        [type]: direction === 'prev' ? -30 : 30
      }));
    }

    // 更新时间值的函数
    const updateTimeValue = () => {
      // 执行动画：重置为0
      if (!isInertiaScroll) {
        setTranslateY(prev => ({
          ...prev,
          [type]: 0
        }));
      }

      // 获取当前数组和值
      const array = type === 'hour' ? hours : minutes;
      let current = type === 'hour' ? currentHour : currentMinute;

      // 计算新值
      for (let i = 0; i < count; i++) {
        const currentIndex = array.indexOf(current);
        const newIndex = direction === 'prev'
          ? (currentIndex - 1 + array.length) % array.length
          : (currentIndex + 1) % array.length;
        current = array[newIndex];
      }

      // 更新状态
      if (type === 'hour') {
        setCurrentHour(current);
      } else {
        setCurrentMinute(current);
      }

      // 动画完成后重置标志
      if (!isInertiaScroll) {
        delayTimerId.current = setTimeout(() => {
          animationInProgress.current = false;
        }, 100); // 进一步减少恢复时间
      }
    };

    // 让DOM有时间应用新的translateY
    if (isInertiaScroll) {
      updateTimeValue();
    } else {
      setTimeout(updateTimeValue, 10);
    }
  };

  // 统一处理滚动事件（触摸和滚轮通用）
  const handleScroll = (type: 'hour' | 'minute', delta: number) => {
    if (Math.abs(delta) < 5) return;
    const direction = delta > 0 ? 'next' : 'prev';
    const steps = Math.floor(Math.abs(delta) / 80); // 根据滚动速度调整步长
    handleTimeChange(type, direction, steps);
  };
  // 鼠标滚轮事件处理
  const handleWheel = (e: WheelEvent<HTMLDivElement>, type: 'hour' | 'minute') => {
    // e.preventDefault();

    // 累积滚动量
    wheelDelta.current += e.deltaY;

    // 节流处理
    if (!wheelTimer.current) {
      wheelTimer.current = setTimeout(() => {
        handleScroll(type, wheelDelta.current);
        wheelDelta.current = 0;
        wheelTimer.current = null;
      }, 50);
    }

    // 添加视觉反馈
    const currentElement = type === 'hour'
      ? hourColumnRef.current
      : minuteColumnRef.current;

    if (currentElement) {
      currentElement.classList.add(styles.wheelActive);
      setTimeout(() => {
        currentElement?.classList.remove(styles.wheelActive);
      }, 100);
    }
  };

  // 处理触摸开始
  const handleTouchStart = (e: TouchEvent, type: 'hour' | 'minute') => {
    // 取消可能正在进行的动画
    if (rafId.current) {
      cancelAnimationFrame(rafId.current);
      rafId.current = null;
    }

    if (delayTimerId.current) {
      clearTimeout(delayTimerId.current);
      delayTimerId.current = null;
    }

    touchStartY.current = e.touches[0].clientY;
    lastTouchY.current = e.touches[0].clientY;
    touchCurrentType.current = type;
    setIsTouching(true);
    lastTouchTime.current = Date.now();
    velocityY.current = 0;
    touchMoveDistances.current = [];
    touchMoveTimes.current = [];
    animationInProgress.current = false;
  };

  // 处理触摸移动
  const handleTouchMove = (e: TouchEvent) => {
    if (!isTouching || !touchCurrentType.current) return;

    const currentY = e.touches[0].clientY;
    const diff = currentY - lastTouchY.current;
    const now = Date.now();
    const timeDiff = now - lastTouchTime.current;

    // 保存最近的10个移动点来计算速度
    if (timeDiff > 0) {
      touchMoveDistances.current.push(diff);
      touchMoveTimes.current.push(timeDiff);

      // 只保留最近的10个点
      if (touchMoveDistances.current.length > 10) {
        touchMoveDistances.current.shift();
        touchMoveTimes.current.shift();
      }
    }

    lastTouchY.current = currentY;
    lastTouchTime.current = now;

    // 应用视觉反馈
    const totalDiff = currentY - touchStartY.current;
    const translateAmount = Math.min(Math.max(totalDiff * 0.7, -30), 30);
    setTranslateY(prev => ({
      ...prev,
      [touchCurrentType.current!]: translateAmount
    }));

    // 如果移动距离超过阈值，触发翻页
    if (Math.abs(diff) > 10) { // 降低触发阈值
      const direction = diff > 0 ? 'prev' : 'next';
      handleTimeChange(touchCurrentType.current, direction);
      // 移动触发点，但保留部分差距以便连续触发
      touchStartY.current = currentY - (diff * 0.5);
    }
  };

  // 处理触摸结束
  const handleTouchEnd = () => {
    if (!isTouching || !touchCurrentType.current) return;

    // 重置位置
    setTranslateY(prev => ({
      ...prev,
      [touchCurrentType.current!]: 0
    }));

    // 计算平均速度
    const totalDistance = touchMoveDistances.current.reduce((sum, distance) => sum + distance, 0);
    const totalTime = touchMoveTimes.current.reduce((sum, time) => sum + time, 0);

    // 计算速度 (像素/毫秒)
    const velocity = totalTime > 0 ? totalDistance / totalTime : 0;
    velocityY.current = velocity;

    // 根据速度和方向计算惯性滚动
    const direction = velocity > 0 ? 'prev' : 'next';
    const absVelocity = Math.abs(velocity);

    // 速度超过阈值时触发惯性滚动
    if (absVelocity > 0.1) {
      // 根据速度计算滚动数量，速度越快滚动越多
      const scrollCount = Math.min(
        Math.ceil(absVelocity * 30), // 增加系数提高滚动量
        20 // 最多滚动20个
      );

      // 启用惯性滚动
      let remainingCount = scrollCount;
      const type = touchCurrentType.current;

      const doInertiaScroll = () => {
        if (remainingCount > 0 && type) {
          // 每次滚动的数量，刚开始多一些，逐渐减少
          const scrollStep = Math.max(1, Math.floor(remainingCount / 4));
          handleTimeChange(type, direction, scrollStep);
          remainingCount -= scrollStep;

          // 继续滚动
          rafId.current = requestAnimationFrame(doInertiaScroll);
        }
      };

      // 延迟一帧开始惯性滚动
      rafId.current = requestAnimationFrame(doInertiaScroll);
    }

    setIsTouching(false);
    touchCurrentType.current = null;
  };

  // 提交选择
  const handleConfirm = () => {
    onConfirm([currentHour, currentMinute]);
    onClose();
  };

  // 创建时间项目
  const renderTimeItems = (type: 'hour' | 'minute') => {
    const current = type === 'hour' ? currentHour : currentMinute;
    const array = type === 'hour' ? hours : minutes;
    const currentIndex = array.indexOf(current);
    const prev2Index = (currentIndex - 2 + array.length) % array.length;
    const prev1Index = (currentIndex - 1 + array.length) % array.length;
    const next1Index = (currentIndex + 1) % array.length;
    const next2Index = (currentIndex + 2) % array.length;
    const unit = type === 'hour' ? '时' : '分';

    return (
      <div
        className={styles.timeColumnInner}
        style={{
          transform: `translateY(${type === 'hour' ? translateY.hour : translateY.minute}px)`,
          transition: isTouching ? 'none' : 'transform 0.2s cubic-bezier(0.1, 0.7, 0.1, 1)'
        }}
      >
        <div key="prev2" className={styles.timeItemFar}>{array[prev2Index]}</div>
        <div
          key="prev"
          className={styles.timeItemPrev}
          onClick={() => handleTimeChange(type, 'prev')}
        >
          {array[prev1Index]}
        </div>
        <div key="current" className={styles.timeItemCurrent}>
          <span className={styles.timeNumber}>{current}</span>
          <span className={styles.timeUnitInline}>{unit}</span>
        </div>
        <div
          key="next"
          className={styles.timeItemNext}
          onClick={() => handleTimeChange(type, 'next')}
        >
          {array[next1Index]}
        </div>
        <div key="next2" className={styles.timeItemFar}>{array[next2Index]}</div>
      </div>
    );
  };

  const deviceType = getDeviceType(); //0 mobile 1 pc

  return (
    <>
      {
        deviceType ? <CenterPopup
          visible={visible}
          onMaskClick={onClose}
          // position="bottom"
          bodyStyle={{
            borderRadius: "16px",
            margin: "0 12px 12px 12px",
            width: "calc(100% - 24px)",
            maxWidth: "calc(100% - 24px)"
          }}
        >
          <div className={styles.timePickerPopup}>
            <div className={styles.popupHeader}>
              <div className={styles.popupTitle}>{title}</div>
            </div>
            <div className={styles.timePickerContainer}>
              <div
                ref={hourColumnRef}
                className={styles.timeColumn}
                onTouchStart={(e) => handleTouchStart(e, 'hour')}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                onWheel={(e) => handleWheel(e, 'hour')} // 新增滚轮事件
              >
                {renderTimeItems('hour')}
              </div>
              <div
                ref={minuteColumnRef}
                className={styles.timeColumn}
                onTouchStart={(e) => handleTouchStart(e, 'minute')}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                onWheel={(e) => handleWheel(e, 'minute')} // 新增滚轮事件
              >
                {renderTimeItems('minute')}
              </div>
            </div>
            <div className={styles.popupFooter}>
              <div
                className={styles.popupButton}
                onClick={onClose}
              >
                取消
              </div>
              <div
                className={`${styles.popupButton} ${styles.primary}`}
                onClick={handleConfirm}
              >
                确定
              </div>
            </div>
          </div>
        </CenterPopup> : <Popup
          visible={visible}
          onMaskClick={onClose}
          position="bottom"
          bodyStyle={{
            borderRadius: "16px",
            margin: "0 12px 12px 12px",
            width: "calc(100% - 24px)",
          }}
        >
          <div className={styles.timePickerPopup}>
            <div className={styles.popupHeader}>
              <div className={styles.popupTitle}>{title}</div>
            </div>
            <div className={styles.timePickerContainer}>
              <div
                ref={hourColumnRef}
                className={styles.timeColumn}
                onTouchStart={(e) => handleTouchStart(e, 'hour')}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                onWheel={(e) => handleWheel(e, 'hour')} // 新增滚轮事件
              >
                {renderTimeItems('hour')}
              </div>
              <div
                ref={minuteColumnRef}
                className={styles.timeColumn}
                onTouchStart={(e) => handleTouchStart(e, 'minute')}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                onWheel={(e) => handleWheel(e, 'minute')} // 新增滚轮事件
              >
                {renderTimeItems('minute')}
              </div>
            </div>
            <div className={styles.popupFooter}>
              <div
                className={styles.popupButton}
                onClick={onClose}
              >
                取消
              </div>
              <div
                className={`${styles.popupButton} ${styles.primary}`}
                onClick={handleConfirm}
              >
                确定
              </div>
            </div>
          </div>
        </Popup>
      }
    </>
  );
};

export default TimePicker; 