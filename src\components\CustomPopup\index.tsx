import React, { ReactNode } from 'react';
import { Popup, Button, Input, Space, CheckList, Checkbox } from 'antd-mobile';
import type { CheckListValue } from 'antd-mobile/es/components/check-list';
import './index.scss';

export type PopupButtonType = 'default' | 'primary' | 'warning';

export interface PopupButton {
  text: string;
  type?: PopupButtonType;
  className?: string;
  onClick?: () => void;
  color?: string;
  textColor?: string;
}

export interface CustomPopupProps {
  visible: boolean;
  title?: string;
  content?: string | ReactNode;
  buttons?: PopupButton[];
  checkboxText?: string;
  checked?: boolean;
  onCheckChange?: (checked: boolean) => void;
  inputValue?: string;
  onInputChange?: (value: string) => void;
  showInput?: boolean;
  showCheckbox?: boolean;
  onClose?: () => void;
  position?: 'bottom' | 'top' | 'left' | 'right';
  inputPlaceholder?: string;
  bodyStyle?: React.CSSProperties;
  primaryButtonColor?: string;
  defaultButtonColor?: string;
}

const CustomPopup: React.FC<CustomPopupProps> = ({
  visible,
  title,
  content,
  buttons = [],
  checkboxText,
  checked = false,
  onCheckChange,
  inputValue = '',
  onInputChange,
  showInput = false,
  showCheckbox = false,
  onClose,
  position = 'bottom',
  inputPlaceholder = '输入内容',
  bodyStyle,
  primaryButtonColor = '#1677ff',
  defaultButtonColor = '#f5f5f5',
}) => {
  const defaultBodyStyle = {
    padding: '20px 16px',
    ...bodyStyle,
  };

  const handleCheckChange = (val: CheckListValue[]) => {
    if (onCheckChange) {
      onCheckChange(val.length > 0);
    }
  };

  const getButtonStyle = (button: PopupButton) => {
    let style: React.CSSProperties = {};
    
    if (button.color) {
      style.backgroundColor = button.color;
    } else if (button.type === 'primary') {
      style.backgroundColor = primaryButtonColor;
    } else if (button.type === 'warning') {
      style.backgroundColor = '#ff9800';
    } else {
      style.backgroundColor = defaultButtonColor;
    }
    
    if (button.textColor) {
      style.color = button.textColor;
    } else if (button.type === 'default' && !button.color) {
      style.color = '#666';
    } else {
      style.color = '#fff';
    }
    
    return style;
  };

  return (
    <Popup
      visible={visible}
      onMaskClick={onClose}
      position={position}
      bodyStyle={defaultBodyStyle}
    >
      <div className="custom-popup">
        {title && <div className="popup-title">{title}</div>}
        
        {content && <div className="popup-content">{content}</div>}
        
        {showInput && (
          <div className="popup-input">
            <Input
              placeholder={inputPlaceholder}
              value={inputValue}
              onChange={onInputChange}
              clearable
              className="custom-popup-input"
            />
          </div>
        )}
        
        {showCheckbox && checkboxText && (
          <div className="popup-checkbox">
            <Checkbox defaultChecked value="checkbox" >{checkboxText}</Checkbox>
          </div>
        )}
        
        <div className="popup-buttons">
          {buttons.map((button, index) => (
            <Button
              key={index}
              className={`popup-button ${button.className || ''}`}
              onClick={button.onClick}
              style={getButtonStyle(button)}
            >
              {button.text}
            </Button>
          ))}
        </div>
      </div>
    </Popup>
  );
};

export default CustomPopup; 