.root_container {
  flex: 1;
  padding: 0 20px;
  user-select: none;
}

.header {
  width: 100%;
  height: 210px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(217, 217, 217, 1);
  border-radius: 12px;
  padding: 20px 24px;
}

.header_img_show {
  width: 100%;

  img {
    height: 154px;
  }
}

.content {
  width: 100%;
  height: calc(100% - 210px);
  display: flex;
  flex-direction: column;
  padding: 0 12px;
}

.content_span {
  margin-top: 15px;

  font-family: MiSans W;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(116, 116, 116, 1);
}

.list_container {
  margin-top: 20px;
}

.list_right_container {
  width: 230px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.list_right_container_disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed !important;
}

.list_right_container_span {
  font-family: MiSans;
  font-weight: 400;
  font-size: 14px;

  width: 200px;
  white-space: normal;
  word-break: break-all;
}

.list_right_container_img {
  height: 18px;
}

// form 样式

.form_container {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
}

.input_container {
  width: 100%;
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  position: relative;

  span {
    position: absolute;
    top: 70%;
    right: 20px;
    transform: translateY(-50%);
    display: flex;
    justify-content: center;
    color: var(--primary-color);
    cursor: pointer;
  }

  :global {
    .ant-form-item {
      width: 80%;
      margin: 0;
    }
    .ant-input {
      width: 100%;
    }
    .ant-form-item-control-input-content {
      display: flex;
      gap: 20px;
    }
  }
}

.size_limit_container {
  display: flex;

  :global {
    .ant-form-item-row {
      width: 100%;
      flex-direction: row !important;
    }
    .ant-form-item-control-input-content {
      display: flex;
      gap: 20px;
    }
  }
}

// number Input 组件的样式

.number_input_container {
  width: 260px !important;

  :global {
    .ant-input-number-outlined {
      width: 100% !important;
    }
    .ant-input-number-suffix {
      margin: 0 !important;
    }
    .ant-input-number-input-wrap,
    .ant-input-number-input {
      display: flex;
      height: 100%;
    }
  }
}

.number_input_img {
  height: 24px;
}

.select_container {
  width: 25% !important;
  display: flex;
  justify-content: right;
  align-items: center;

  :global {
    .ant-form-item {
      width: 60px !important;
    }
  }
}

.storage_location_container {
  width: 500px;
  height: 500px;
  display: flex;
}

.modal_footer {
  width: 100%;
  display: flex;
  gap: 20px;

  .modal_footer_btns {
    min-width: 200px;
    flex: 1;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;

    font-family: MiSans W;
    font-weight: 500;
    font-size: 17px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    vertical-align: middle;
    background: var(--primary-color);
    color: var(--text-color-reverse);
    cursor: pointer;
  }

  .modal_footer_btns_other {
    background: rgba(245, 245, 245, 1);
    color: rgba(112, 112, 112, 1);
    border: 1px solid rgba(135, 135, 135, 1);
    gap: 10px;

    img {
      height: 24px;
    }
  }
}
