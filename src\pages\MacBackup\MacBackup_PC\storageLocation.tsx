import { FileItem, getPoolInfo, listDirectory, StoragePool } from "@/api/fatWall";
import FileSelectorUtil, { IFileDirBaseProps } from "@/components/FileSelector";
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from "react";
import { useControllableValue, useRequest } from 'ahooks';
import { WebDavInfo } from "@/utils/DeviceType";
import styles from './index.module.scss';

const StorageLocation = forwardRef((props: {
  openStorageLocationModal: boolean,
  setSelectedValue: (string: string) => void,
  setCurrentDirPath: (string: string) => void,
  onChange: (v: WebDavInfo) => void,
  defaultValue: WebDavInfo;
}, ref) => {

  const { openStorageLocationModal, setSelectedValue, setCurrentDirPath } = props;

  // 原始存储池信息
  const [poolData, setPoolData] = useState<StoragePool[]>([]);

  // 当前文件夹信息
  const [currentFileDirList, setCurrentFileDirList] = useState<FileItem[]>([]);

  // 当前所在的路径配置
  const [currentFileDirConfig, setCurrentFileDirConfig] = useState<{ dir_name: string; data_dir: string }>({ dir_name: '', data_dir: '' });
  const dir_config_ref = useRef<{ dir_name: string; data_dir: string }>({ dir_name: '', data_dir: '' });

  // 查询文件夹的分页配置
  const page_config_ref = useRef<{ size: number, token: string }>({ size: 20, token: "" });

  // webDav配置
  const [currentWebDav, setCurrentWebDav] = useControllableValue(props);

  // 获取存储池信息
  const { runAsync: fetchPoolInfo } = useRequest(getPoolInfo, { manual: true });

  // 获取子文件夹信息
  const { runAsync: fetchDirectoryList } = useRequest(listDirectory, { manual: true });

  // 文件夹列表信息配置
  const file_dir_list: IFileDirBaseProps[] = useMemo(() => {
    if (currentFileDirList.length === 0 && currentFileDirConfig.dir_name === '' && currentFileDirConfig.data_dir === '') {
      return poolData.map(item => {
        return {
          id: `${item.data_dir}`,
          dir_name: item.name,
          data_dir: item.data_dir,
          isDirectory: true
        }
      })
    }

    return currentFileDirList.map(item => {
      return {
        id: `${item.parent}${item.name}/`,
        dir_name: item.name,
        data_dir: `${item.parent}${item.name}/`,
        isDirectory: item.xattr?.directory || false
      }
    })
  }, [currentFileDirConfig.data_dir, currentFileDirConfig.dir_name, currentFileDirList, poolData])

  // 当选择的是顶部时使用存储池的数据
  const initData = useCallback(() => {
    setCurrentFileDirList([]);
    setCurrentFileDirConfig({ dir_name: '', data_dir: '' });
    dir_config_ref.current = { dir_name: '', data_dir: '' };
  }, [])

  const getDirectoryList = useCallback(async (path, dir_name, callback: (d: FileItem[]) => void) => {
    console.log(`开始获取文件夹列表: path:${path},dir_name: ${dir_name}`);

    const res = await fetchDirectoryList({
      page: page_config_ref.current, path: { parent: path, recursion: false }
    }).catch((e) => console.log('获取文件夹列表失败: ', e));

    if (res && res.code === 0) {
      callback(res.data.files);
      setCurrentFileDirConfig({ dir_name: dir_name || '', data_dir: path || '' });
      dir_config_ref.current = { dir_name: dir_name || '', data_dir: path || '' };

      page_config_ref.current = { size: page_config_ref.current.size, token: res.data.page.token };
    }

    return res;

  }, [fetchDirectoryList])

  const getPoolInfoData = useCallback(async () => {
    const res = await fetchPoolInfo({}).catch((e) => console.log('获取存储池信息失败: ', e));
    if (res && res.code === 0) {
      if (res.data.internal_pool.length > 0) {
        setPoolData(res.data.internal_pool);
        setCurrentFileDirConfig({ dir_name: '', data_dir: '' });
        dir_config_ref.current = { dir_name: '', data_dir: '' }; // 用来刷新时使用的配置信息

        if (res.data.webDAV) setCurrentWebDav(res.data.webDAV);
      }
    }
  }, [fetchPoolInfo, setCurrentWebDav])

  useEffect(() => {
    if (openStorageLocationModal) {
      getPoolInfoData();
    }
  }, [getPoolInfoData, openStorageLocationModal])

  useImperativeHandle(ref, () => ({
    refresh: () => {
      page_config_ref.current = { size: 20, token: "" }; // 刷新时重置分页配置
      getDirectoryList(dir_config_ref.current.data_dir, dir_config_ref.current.dir_name, d => setCurrentFileDirList(d));
    }
  }), [getDirectoryList])

  return (
    <div className={styles.storage_location_container}>
      <FileSelectorUtil onChange={setSelectedValue} setCurrentFileDirList={setCurrentFileDirList} data={{
        currentFileDirConfig: currentFileDirConfig,
        current_file_dir_list: file_dir_list,
        web_alias_root: currentWebDav.alias_root
      }} dirOnClick={getDirectoryList} initData={initData} requestPageOpt={page_config_ref} getCurrentFileDirPath={setCurrentDirPath} />
    </div>
  )
})

export default StorageLocation;