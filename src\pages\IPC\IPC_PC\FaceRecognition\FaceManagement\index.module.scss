.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: var(--background-color);
  padding: 0 16px 16px;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  .title {
    font-size: 20px;
    font-weight: 500;
    color: var(--title-color);
  }
}

.addFaceButtonContainer {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.addFaceButton {
  padding: 8px 20px;
  background-color: var(--background-color);
  color: var(--subtitle-text-color);
  border: 1px solid var(--thinLine-background-color);
  border-radius: 8px;
  font-size: 17px;
  cursor: pointer;
  outline: none;
}

.section {
  margin-bottom: 24px;
  .sectionTitle {
    font-size: 14px;
    color: #8c93b0;
    margin-bottom: 24px;
  }
}

.divider {
  height: 1px;
  background-color: var(--thinLine-background-color);
  margin: 8px 0 20px;
  width: 100%;
}

.faceGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16px;
}

.faceItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s;
  &:hover {
    transform: scale(1.05);
  }
  .avatarContainer {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 8px;
  }
  .avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .faceName {
    font-size: 12px;
    color: var(--text-color);
    max-width: 60px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.addFaceModal {
  :global {
    .ant-modal-content {
      border-radius: 16px;
      overflow: hidden;
      position: relative;
      padding: 0;
      background-color: var(--desktop-modal-bg-color);
    }
    .ant-modal-header {
      border-bottom: none;
      display: none;
    }
    .ant-modal-body {
      padding: 0;
      min-height: 400px;
      display: flex;
      flex-direction: column;
    }
  }
}

.faceDetailModal {
  background-color: var(--desktop-modal-bg-color);
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 600px;
}

.modalHeader {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 20px;
  position: relative;
  .closeButton {
    font-size: 24px;
    color: var(--title-color);
    cursor: pointer;
    font-weight: 300;
    position: absolute;
    left: 16px;
  }
  .modalTitle {
    font-size: 16px;
    color: var(--text-color);
    font-weight: 500;
    flex: 1;
    text-align: center;
  }
}

.modalContent {
  display: flex;
  flex: 1;
  padding: 24px;
}

.leftSection {
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  position: relative;
}

.leftSection::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 100%;
  background-color: var(--thinLine-background-color);
}

.avatarContainer {
  width: 100x;
  height: 100x;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  // border: 1px solid #f0f0f0;
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
  color: #ccc;
  font-size: 55px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nameLabel {
  font-size: 18px;
  color: var(--text-color);
  font-weight: 500;
  text-align: center;
}

.rightSection {
  width: 70%;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding-left: 40px;
}

.linkFaceBox {
  width: 120px;
  height: 120px;
  background-color: rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c93b0;
  font-size: 14px;
  cursor: pointer;
}

.plusIcon {
  font-size: 30px;
  margin-bottom: 8px;
  color: #ccc;
}

.linkFaceText {
  color: #8c93b0;
  font-size: 14px;
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
}

.saveButton {
  width: 120px;
  height: 40px;
  border-radius: 20px;
  font-size: 16px;
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  color: #fff;
  &:hover {
    background-color: #d6e9ff;
  }
}
.nameInput {
  background-color: var(--cancel-btn-background-color) !important;
  border: none;
  color: var(--text-color);
}

// 空状态样式
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  min-height: 400px;
}

.emptyIcon {
  margin-bottom: 24px;
}

.emptyText {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 8px;
}

