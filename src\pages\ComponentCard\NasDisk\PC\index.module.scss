// .cardContainer {
//   background-color: var(--componentcard-bg-color);
//   border-radius: 16px;
//   overflow: hidden;
//   position: relative;
//   display: flex;
//   flex-direction: column;
//   transition: all 0.3s ease;
//   width: 100%;
//   height: 100%;
//   box-sizing: border-box;
// }

// /* 小尺寸卡片 (300*300px) */
// .small {
//   max-width: 300px;
//   max-height: 300px;

//   .cardContent {
//     flex-direction: column;
//     padding: 10px 20px;
//   }

//   .transferIcon {
//     margin: 20px 0 10px;
//   }

//   .statusInfo {
//     align-items: center;
//     text-align: center;
//   }
// }

// /* 大尺寸卡片 (684*300px) */
// .large {
//   max-width: 684px;
//   max-height: 300px;

//   .cardContent {
//     flex-direction: row;
//     align-items: center;
//     justify-content: flex-start;
//     padding: 20px 30px;
//   }

//   .transferIcon {
//     margin-right: 40px;
//     margin-bottom: 0;
//   }

//   .statusInfo {
//     align-items: flex-start;
//     text-align: left;
//   }
// }

// .cardHeader {
//   padding: 14px 16px;
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   min-height: 72px;
//   box-sizing: border-box;
//   background-color: var(--componentcard-title-bg-color);
// }

// .logoArea {
//   display: flex;
//   align-items: center;
// }

// .logo {
//   width: 34px;
//   height: 34px;
//   background-color: rgba(118, 175, 255, 0.34);
//   border-radius: 8px;
//   margin-right: 12px;
//   position: relative;
//   overflow: hidden;
// }

// .headerButtons {
//   display: flex;
//   align-items: center;
// }

// .collapseButton {
//   width: 26px;
//   height: 26px;
//   border: none;
//   background: none;
//   cursor: pointer;
// }

// .title {
//   font-size: 16px;
//   font-weight: 500;
//   color: var(--text-color);
// }
// .loginStatus {
//   font-family: MiSans;
//   font-weight: 400;
//   font-size: 14px;
//   line-height: 100%;
//   letter-spacing: 0px;
//   vertical-align: middle;
//   color: var(--subtitle-text-color);
// }
// .cardContent {
//   flex: 1;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   position: relative;
//   // overflow: hidden;
// }

// .transferIcon {
//   width: 82px;
//   height: 54px;
// }

// .statusLabel {
//   position: absolute;
//   padding: 16px;
//   background-color: var(--primary-btn-background-color);
//   color: var(--primary-color);
//   border-radius: 16px;
//   animation: fadeIn 0.3s ease;
//   z-index: 10;
//   white-space: nowrap;
// }

// @keyframes fadeIn {
//   from {
//     opacity: 0;
//     transform: translateY(-10px);
//   }
//   to {
//     opacity: 1;
//     transform: translateY(0);
//   }
// }

// .statusLabelSmall,.statusLabelLarge {
//   top: -15px;
//   right: 20px;
//   width: 200px;
// }


// .statusText {
//   font-size: 14px;
//   font-weight: 500;
// }

// .statusInfo {
//   display: flex;
//   flex-direction: column;
// }

// .statusTitle {
//   font-family: MiSans;
//   font-weight: 500;
//   font-size: 16px;
//   line-height: 100%;
//   letter-spacing: 0%;
//   text-align: center;
//   vertical-align: middle;
// }

// .statusSubtitle {
//   font-family: MiSans;
//   font-weight: 400;
//   font-size: 14px;
//   line-height: 140%;
//   letter-spacing: 0px;
//   text-align: center;
//   color: var(--title-color);
//   margin-top: 6px;
// }

// .cardFooter {
//   padding: 16px;
//   min-height: 80px;
//   box-sizing: border-box;
// }

// .actionButton {
//   width: 100%;
//   height: 48px;
//   border-radius: 8px;
//   background-color: var(--componentcard-btn-bg-color);
//   border: none;
//   font-size: 16px;
//   font-weight: 500;
//   color: var(--title-color);
//   cursor: pointer;

//   // &:hover {
//   //   background-color: #e8e8e8;
//   // }
// }
