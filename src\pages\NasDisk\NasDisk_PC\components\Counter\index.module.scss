// PC端收银台样式
.counterOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.65);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}

// 收银台模态框
.counterModal {
  width: 90%;
  max-width: 1000px;
  height: 80%;
  max-height: 700px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
  overflow: hidden;

  @keyframes slideIn {
    0% {
      transform: scale(0.8);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  // 收银台头部
  .counterHeader {
    height: 60px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;

    .counterTitle {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      text-align: center;
      flex: 1;
    }

    .counterClose {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: #666;
      cursor: pointer;
      transition: all 0.2s ease;
      user-select: none;

      &:hover {
        background-color: #e8e8e8;
        color: #333;
        transform: rotate(90deg);
      }

      &:active {
        background-color: #d9d9d9;
      }
    }
  }

  // 收银台容器
  .counterContainer {
    flex: 1;
    position: relative;
    background-color: #fff;
    overflow: hidden;
    min-height: 500px;

    .counterIframe {
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 0 0 12px 12px;
    }

    // 加载状态覆盖层
    .loadingOverlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 10;

      .loadingSpinner {
        width: 48px;
        height: 48px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #402C00;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }

      .loadingText {
        font-size: 16px;
        color: #666;
        text-align: center;
        font-weight: 500;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .counterModal {
    width: 95%;
    height: 85%;
    margin: 0 16px;
  }
  
  .counterHeader {
    height: 56px;
    padding: 0 16px;
    
    .counterTitle {
      font-size: 16px;
    }
    
    .counterClose {
      width: 32px;
      height: 32px;
      font-size: 16px;
    }
  }
  
  .counterContainer {
    min-height: 400px;
    
    .loadingOverlay {
      .loadingSpinner {
        width: 40px;
        height: 40px;
        border-width: 3px;
      }
      
      .loadingText {
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .counterModal {
    width: 100%;
    height: 100%;
    border-radius: 0;
    max-width: none;
    max-height: none;
  }
  
  .counterContainer {
    .counterIframe {
      border-radius: 0;
    }
  }
} 