.container {
  display: flex;
  flex-direction: column;
  background-color: #000;
  color: #fff;
  max-height: 100vh;
  height: 100%;
  padding-bottom: 20px;
  position: relative;
  overflow: hidden; // 防止内容溢出

  // 将导航栏样式移到container内部，使其只在组件内生效
  :global(.adm-nav-bar) {
    --adm-color-text: #FFFFFF;
    --adm-font-size-7: 16px;
  }

  :global(.adm-nav-bar-back-arrow) {
    color: #FFFFFF;
    font-size: 20px;
  }
}

// 添加滚动容器
.scrollContainer {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; // 为iOS提供平滑滚动
  height: 100%;

  // 设置滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
  }
}

.headerSection {
  position: relative;
  height: 300px;
  background-size: cover;
  background-position: center;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);

  // 将导航栏位置样式放在这里，使其定位在顶部
  :global(.adm-nav-bar) {
    position: fixed;
    top: 35px;
    left: 0;
    right: 0;
    z-index: 10;
    background-color: transparent;
  }
}

.actorInfo {
  position: relative;
  z-index: 1;
}

.actorName {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
  word-wrap: break-word; // 允许长单词换行
  word-break: break-all; // 在任何字符间换行（适用于中文）
  line-height: 1.3; // 设置行高
  color: #ffffff;
}

.worksSection {
  padding-left: 20px;
  padding-top: 20px;
}

.sectionTitle {
  font-family: 'MiSans W', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
  color: #fff;
  margin: 0 0 16px 0;
}

.worksGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loadingSpinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-family: 'MiSans', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: var(--subtitle-text-color);
}

.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.emptyText {
  font-family: 'MiSans', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: var(--subtitle-text-color);
}
