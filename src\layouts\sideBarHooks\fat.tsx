import { useCallback, useMemo, useState } from "react";
import styles from '../Layout.module.scss';
import { PreloadImage } from "@/components/Image";
import libraryAdd from "@/Resources/layout/libraryAdd.png";
import lib_manager_icon from '@/Resources/layout/library_manager_icon.png';
import lib_icon from '@/Resources/layout/library_icon.png';
import { useHistory } from "react-router-dom";
import CreateLibraryModal from "@/pages/FATWall/FATWall_PC/LibraryManagement/CreateLibraryModal";
import { useVideoLibraryList } from "@/hooks/useVideoLibrary";

const useFATSideBar = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const { libraries, getLib, setLibraries } = useVideoLibraryList();

  const history = useHistory();

  // 侧边栏item点击回调

  const callback = useCallback((item) => {
    history.push({ pathname: `/filmAndTelevisionWall_pc/library/${item.lib_id}`, state: { data: item, title: item.name } }) // 传递页面标题，layout中获取显示
  }, [history])

  const toLibraryManagement = useCallback(() => {
    history.push({ pathname: '/filmAndTelevisionWall_pc/libraryManagement', state: { title: '媒体库管理' } })
  }, [history])

  // 处理创建媒体库
  const handleCreateLibrary = useCallback(() => {
    setIsCreateModalOpen(true);
  }, []);

  // 处理创建弹窗关闭
  const handleCreateModalClose = useCallback(() => {
    setIsCreateModalOpen(false);
  }, []);

  // 处理创建成功
  const handleCreateSuccess = useCallback(() => {
    getLib(); // 刷新侧边栏媒体库列表
    // 创建成功后跳转到最近添加页面
    history.push('/filmAndTelevisionWall_pc/recently/recentlyAdd');
  }, [getLib, history]);

  const baseComponent = useMemo(() => {
    return (
      <div className={styles.fat_side_bar_container}>
        <div className={styles.fat_side_bar_header}>
          <span>媒体库</span>
          <div onClick={handleCreateLibrary}>
            <PreloadImage src={libraryAdd} alt="add" />
          </div>
        </div>
        <div className={styles.fat_side_bar_content}>
          <div className={styles.fat_side_bar_content_item} onClick={() => toLibraryManagement()}>
            <PreloadImage src={lib_manager_icon} alt="library_manager" />
            <span>媒体库管理</span>
          </div>
          {
            libraries.map((it) => (
              <div key={it.lib_id} className={styles.fat_side_bar_content_item} onClick={() => callback(it)}>
                <PreloadImage src={lib_icon} alt="library_file" />
                <span>{it.name}</span>
              </div>
            ))
          }
        </div>
      </div>
    )
  }, [callback, libraries, toLibraryManagement, handleCreateLibrary])

  return {
    fatComponents: useMemo(() => (
      <>
        {baseComponent}
        <CreateLibraryModal
          visible={isCreateModalOpen}
          onClose={handleCreateModalClose}
          onSuccess={handleCreateSuccess}
          libraryCount={libraries.length}
        />
      </>
    ), [baseComponent, handleCreateModalClose, handleCreateSuccess, isCreateModalOpen, libraries.length]),
    libraries,
    setLibraries,
    getLib
  }
}

export default useFATSideBar;