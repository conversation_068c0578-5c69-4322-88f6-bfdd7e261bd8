import React, { useState, useMemo, useCallback, useRef } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, subMonths, addMonths, startOfDay, endOfDay } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import './DatePicker.css';
import left_arrow from '@/Resources/datePicker/<EMAIL>';
import right_arrow from '@/Resources/datePicker/<EMAIL>';
import left_arrow_dark from '@/Resources/datePicker/<EMAIL>';
import right_arrow_dark from '@/Resources/datePicker/<EMAIL>';
import { Divider } from 'antd-mobile';
import { px2rem } from '../TimeAxis/TimeAxis';
import { PreloadImage } from '@/components/Image';
import { Toast } from '@/components/Toast/manager';
import { getDeviceType } from '@/utils/DeviceType';
import Modal from '@/components/Modal';
import { useTheme } from '@/utils/themeDetector';
import { IEventVideo } from '@/pages/IPC/IPC_APP/CameraDetail';
import { getVideoRecord } from '@/api/ipc';
import { useUpdateEffect } from 'ahooks';

interface DatePickerProps {
  onSelect?: (date: Date) => void;
  onCancel: () => void;
  isShow: boolean
  camera_lens?: string[]
}

const DatePicker: React.FC<DatePickerProps> = ({ onSelect, onCancel, isShow, camera_lens }) => {
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [startX, setStartX] = useState<number>(0);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [scrollX, setScrollX] = useState<number>(0);
  const [markedDates, setMarkedDates] = useState<string[]>([]); // 格式: ['2023-09-03', '2023-09-15'] //如果不传该值则是一个正常的日期选择器，不做是否有视频判断
  const pageRef = useRef<{ size: number, token: string }>({ size: 20, token: '' });

  // 生成月份日期数据
  const dates = useMemo(() => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

    // 添加首日对齐占位符
    const startDay = monthStart.getDay(); // 0=周日,1=周一...6=周六
    return [
      ...Array(startDay).fill(null), // 填充空白
      ...days
    ];
  }, [currentDate]);

  // 处理日期点击
  const handleDateClick = useCallback((date: Date) => {
    if (camera_lens) {
      const hasVideo = markedDates.includes(format(date, 'yyyy-MM-dd')) && date <= new Date();
      if (!hasVideo) {
        Toast.show('没有视频');
        return;
      }
    }
    setSelectedDate(date);
    onSelect?.(date);
    onCancel();
  }, [camera_lens, markedDates, onCancel, onSelect]);

  //切换月份
  const changeMonth = useCallback((type: 'next' | 'prev') => {
    setCurrentDate(prev => {
      return type === 'prev'
        ? subMonths(prev, 1)  // 上个月
        : addMonths(prev, 1)  // 下个月
    })
  }, [])

  // 生成星期标题
  const weekdays = useMemo(() => {
    return ['日', '一', '二', '三', '四', '五', '六'].map((day) => (
      <div key={day} className="weekday">{day}</div>
    ));
  }, []);

  const onCancelCallback = useCallback(() => {
    onCancel();
    // setSelectedDate(null);
  }, [onCancel])

  const selectToday = useCallback(() => {
    const now = new Date();
    setCurrentDate(now);
    handleDateClick(now);
  }, [handleDateClick])


  // 拖拽逻辑
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    setStartX(e.clientX);
  }, []);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    setIsDragging(true);
    setStartX(touch.clientX);
  }, []);

  //处理拖拽边界
  const handleScroll = useCallback((x: number) => {
    const cWidth = window.innerWidth / 2;
    if (x > cWidth) {
      setScrollX(0);
      changeMonth("prev")
      setIsDragging(false);
      return;
    }
    if (x < -cWidth) {
      setScrollX(0);
      changeMonth("next")
      setIsDragging(false);
      return;
    }
    setScrollX(x);
  }, [changeMonth])

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isDragging) return;
    const touch = e.touches[0];
    const x = touch.clientX - startX;
    handleScroll(x);
  }, [handleScroll, isDragging, startX])

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    if (!isDragging) return;
    let x = e.clientX - startX;
    handleScroll(x)
  }, [handleScroll, isDragging, startX]);


  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setScrollX(0);
  }, [])

  // 获取当月事件数据
  const getCurMonthEventData = useCallback(async () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const params = {
      page: pageRef.current,
      options: {
        option: ['event_name', 'time', 'camera_lens'],
        event_name: ['human', 'fire', 'pet'],
        camera_lens: camera_lens,
        time: {
          start: startOfDay(monthStart).getTime(),
          end: endOfDay(monthEnd).getTime()
        }
      }
    }
    const res = await getVideoRecord(params).then().catch(() => null);

    if (res && res.code === 0 && res.data) {
      const videos: IEventVideo[] = res.data.videos.map((it, i) => ({ ...it, id: it.event_name + i }));
      const tempData: string[] = [];
      videos.forEach((it) => {
        const dateString: string = format(new Date(Number(it.time)), 'yyyy-MM-dd');
        if (tempData.includes(dateString)) return;
        tempData.push(dateString);
      })
      setMarkedDates(tempData);
      pageRef.current = { ...pageRef.current, token: res.data.page.token }
    }

    if (res && res.code === 5000) {
      Toast.show('请求超时，请稍后再试');
    }
  }, [camera_lens, currentDate])

  useUpdateEffect(() => {
    if (camera_lens) {
      getCurMonthEventData()
    }
  }, [camera_lens, getCurMonthEventData])

  const deviceType = getDeviceType();
  const { isDarkMode } = useTheme();

  return (
    <Modal position={deviceType ? 'normal' : 'bottom'} contentStyle={{ height: px2rem(400), width: deviceType ? px2rem(600) : '' }} isShow={isShow} onCancel={onCancelCallback} content={
      <div className="date-picker" onTouchEnd={handleMouseUp} onTouchCancel={handleMouseUp} onMouseUp={handleMouseUp} onMouseLeave={handleMouseUp} onMouseMove={handleMouseMove} onMouseDown={handleMouseDown} onTouchStart={handleTouchStart} onTouchMove={handleTouchMove} >
        {/* 头部 */}
        <div className="header">
          <div className='title'>选择日期</div>
          <div className='header_date_select'>
            <div className='header_date_select_left'></div>
            <div className='header_date_select_center'>
              <PreloadImage onClick={() => changeMonth('prev')} src={isDarkMode ? left_arrow_dark : left_arrow} alt='left_arrow' />
              <span>{format(currentDate, 'yyyy.MM', { locale: zhCN })}</span>
              <PreloadImage onClick={() => changeMonth('next')} src={isDarkMode ? right_arrow_dark : right_arrow} alt='left_arrow' />
            </div>
            <div className='header_date_select_right' onClick={selectToday}>今</div>
          </div>
        </div>

        <Divider />

        {/* 星期栏 */}
        <div className="weekdays">{weekdays}</div>

        {/* 日期网格 */}
        <div className="dates-grid" style={{ transform: `translateX(${px2rem(scrollX)})` }}>
          {dates.map((date, index) => {
            if (!date) {
              return <div key={`empty-${index}`} className="date-cell empty" />;
            }
            const isCurrentMonth = isSameMonth(date, currentDate);
            const hasVideo = markedDates && date <= new Date() ? markedDates.includes(format(date, 'yyyy-MM-dd')) : false;
            const isSelected = camera_lens ? selectedDate && isSameDay(date, selectedDate) && hasVideo : selectedDate && isSameDay(date, selectedDate);
            return (
              <div key={date.toISOString()} className={`date-cell  ${!isCurrentMonth ? 'other-month' : ''} ${isSelected ? 'selected' : ''}`} onClick={() => handleDateClick(date)} >
                <div className={`date-number ${camera_lens ? hasVideo && isCurrentMonth ? 'hasVideo' : 'notVideo' : ''}`}>{format(date, 'd')}</div>
              </div>
            );
          })}
        </div>
      </div>
    } />
  );
};

export default DatePicker;