.container {
  min-height: calc(100vh - 35px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
}

.title {
  font-size: 32px;
  font-weight: 400;
  color: var(--title-color);
  padding: 10px 16px;
}

.profileWrapper {
  margin-bottom: 20px;
}

.profileList {
  --border-inner: none;
  --border-top: none;
  --border-bottom: none;
  --prefix-padding-right: 12px;
  --align-items: center;
  --padding-left: 16px;
  --padding-right: 16px;
  --adm-font-size-main: 18px;
  background-color: var(--background-color);

  :global {
    .adm-list-body {
      background-color: var(--background-color);
    }
    .adm-list-item {
      background-color: var(--background-color);
    }
    .adm-list-item-content {
      padding: 8px 24px 8px 0;
    }

    .adm-list-item-content-main {
      display: flex;
      align-items: center;

      .adm-list-item-title {
        font-weight: 500;
        font-size: 24px;
        color: var(--title-color);
      }
    }

    .adm-list-item-content-extra {
      flex: none;
      margin-right: 8px;
    }

    .adm-list-item-content-arrow {
      color: var(--list-value-text-color);
    }
    a.adm-list-item:active:not(.adm-list-item-disabled),
    .adm-list-item {
      background-color: var(--background-color);
    }
  }
}

.avatarWrapper {
  position: relative;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  overflow: hidden;
  mask-image: radial-gradient(circle, white 100%, black 100%);
  -webkit-mask-image: radial-gradient(circle, white 100%, black 100%);
}

.avatar {
  width: 100%;
  height: 100%;
  display: block;
}

.avatarText {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 10px;
  color: #fff;
  background: rgba(0, 0, 0, 0.7);
  height: 20px;
  line-height: 20px;
}

.extraContent {
  display: flex;
  align-items: center;
}

.editText {
  font-size: 14px;
  color: var(--list-value-text-color);
}

.markText {
  font-size: 14px;
  color: var(--emergency-text-color);
  font-weight: 500;
}

.content {
  padding: 0 16px;
}

.tabsHeader {
  display: flex;
  align-items: center;
  position: relative;
  padding: 30px 0 16px;
}

.tabItem {
  font-size: 16px;
  color: #999;
  margin-right: 24px;
  cursor: pointer;
  position: relative;
  padding-bottom: 8px;

  &.activeTab {
    color: var(--text-color);
    font-weight: 600;
  }
}

.editIcon {
  position: absolute;
  right: 0;
  cursor: pointer;
}

.photoGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.photoItem {
  // aspect-ratio: 1;
  border-radius: 8px;
  // overflow: hidden;
  .photo {
    width: 79px;
    height: 102px;
    border-radius: 12px;
    object-fit: cover;
  }
}

.addPhoto {
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.videoList {
  padding: 0;
}

.dateHeader {
  display: flex;
  align-items: center;
  margin: 10px;
  color: #8c93b0;

  .date {
    font-size: 14px;
    margin-right: 8px;
  }

  .subDate {
    font-size: 12px;
  }
}

.videoItem {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--background-color);
  border-radius: 12px;
  padding: 0 10px;

  .videoInfo {
    display: flex;
    align-items: center;

    .videoAvatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin-right: 12px;
    }

    .videoTimeInfo {
      .videoTime {
        font-size: 16px;
        color: var(--text-color);
        font-weight: 600;
      }

      .videoSubText {
        font-size: 12px;
        color: var(--list-value-text-color);
      }
    }
  }

  .videoPreviewContainer {
    display: flex;
    align-items: center;
  }

  .videoPreview {
    position: relative;
    width: 106px;
    height: 64px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 8px;

    .videoThumbnail {
      width: 106px;
      height: 64px;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .deleteButton {
    background-color: var(--emergency-background-color);
    color: var(--emergency-text-color);
    padding: 4px 8px;
    border-radius: 13px;
    font-size: 12px;
    cursor: pointer;
    white-space: nowrap;
  }
}

.thinLine {
  position: relative;
  margin: 0 16px;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    transform: scaleY(0.5);
    transform-origin: 0 100%;
  }

  &.customMargin {
    margin-top: 24px;
  }
}
.modalContent {
  .modalHeader {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    text-align: center;
    color: var(--text-color);
  }

  .remarkInput {
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    width: 100%;
    font-size: 14px;
    background-color: var(--cancel-btn-background-color);
    :global {
      .adm-input-element {
        color: var(--text-color);
      }
    }
  }

  .modalFooter {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;

    button {
      flex: 1;
      padding: 12px;
      border-radius: 8px;
      border: none;
      font-size: 16px;
    }

    .cancelButton {
      margin-right: 8px;
      background-color: var(--cancel-btn-background-color);
      color: var(--title-color);
    }

    .confirmButton {
      margin-left: 8px;
      background-color: var(--primary-color);
      color: #fff;
    }
  }
}

.popoverContent {
  // min-width: 140px;
  // padding: 8px 0;
  // background-color: var(--background-color);
  border-radius: 8px;
}

.popoverItem {
  padding: 12px 16px;
  text-align: center;
  cursor: pointer;

  &:active {
    background-color: transparent;
  }
}

.deleteText {
  color: var(--emergency-text-color);
  font-size: 16px;
  font-weight: 500;
}

.deleteModalContent {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

.deleteAction {
  color: var(--emergency-text-color);
  font-size: 16px;
  font-weight: 500;
}

// 自定义头像选择模态框
.avatarSelectModal {
  position: fixed;
  top: 35px;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background-color);
  z-index: 999;
  width: 100%;
  height: 100%;
}

.avatarModalHeader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 56px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
}

.avatarModalLeft {
  // width: 24px;
  // height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatarModalRight {
  // width: 24px;
  // height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

// .avatarBackIcon {
//   width: 24px;
//   height: 24px;
// }

// .avatarCheckIcon {
//   width: 24px;
//   height: 24px;
// }

.avatarTitle {
  font-size: 20px;
  color: var(--title-color);
  text-align: center;
  flex: 1;
}

.avatarModalContent {
  position: absolute;
  top: 56px;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.currentAvatarWrapper {
  padding: 30px 0;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.currentAvatar {
  width: 73px;
  height: 73px;
  border-radius: 50%;
  object-fit: cover;
  display: block;
  margin: 0 auto;
}

.avatarLine {
  height: 1px;
  background-color: var(--thinLine-background-color);
  margin: 10px 24px;
}

.avatarGridContainer {
  position: absolute;
  top: 165px;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px;
  overflow-y: auto;
}

.avatarGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  padding-bottom: 16px;
}

.avatarOption {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border-radius: 12px;
  min-width: 75px;
  min-height: 100px;

  &::after {
    content: "";
    display: block;
    padding-bottom: 100%;
  }

  img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
  }

  &::before {
    content: "";
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 22px;
    height: 22px;
    background-color: #f0f0f0;
    border-radius: 50%;
    z-index: 5;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  &.highlightedOption {
    position: relative;
    box-shadow: none;
    border-radius: 12px;

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 12px;
      pointer-events: none;
      z-index: 6;
    }
  }
}

.selectedMark {
  position: absolute;
  bottom: 14px;
  right: 14px;
  width: 14px;
  height: 14px;
  background-color: var(--primary-color);
  border-radius: 50%;
  z-index: 6;
}

:global {
  .adm-modal-body {
    border-radius: 16px;
    background-color: var(--modal-content-background-color);
  }

  .adm-modal-content {
    padding: 0 32px 12px;
  }

  // 头像选择模态框全屏
  .avatar-modal {
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;

    .adm-mask {
      background-color: #fff !important;
      opacity: 1 !important;
    }

    .adm-modal-body {
      border-radius: 0 !important;
      width: 100% !important;
      height: 100% !important;
      max-width: 100% !important;
      max-height: 100% !important;
      box-shadow: none !important;
    }

    .adm-modal-content {
      padding: 0 !important;
      height: 100% !important;
    }
  }
}

// 照片编辑模态框样式
.photoEditModal {
  position: fixed;
  top: 35px;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: var(--background-color);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.photoEditHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.photoEditLeft,
.photoEditRight {
  display: flex;
  align-items: center;
  justify-content: center;
}

.photoEditTitle {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

.photoEditContent {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.photoEditGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.photoEditItem {
  position: relative;
  border-radius: 8px;
  // overflow: hidden;
  // aspect-ratio: 1;
}

.photoEditItemWrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.photoEditImage {
  width: 79px;
  height: 102px;
  border-radius: 12px;
  object-fit: cover;
}

.photoEditSelectCircle {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.photoEditSelected {
  background-color: var(--primary-color);
}

.photoEditCheckIcon {
  color: #fff;
  font-size: 12px;
  font-weight: bold;
}

.photoEditDeleteBar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: var(--background-color);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.photoEditDeleteButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  cursor: pointer;
  color: var(--text-color);
}

.photoEditDeleteIcon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.deleteConfirmContent {
  padding: 32px 0 24px;
  text-align: center;
}

.deleteConfirmText {
  font-size: 18px;
  color: var(--text-color);
  text-align: center;
  font-weight: 500;
  margin-bottom: 24px;
}

.deleteConfirmButtons {
  display: flex;
  padding: 0 16px;
  .cancelButton,
  .confirmButton {
    flex: 1;
    height: 48px;
    line-height: 48px;
    text-align: center;
    border-radius: 16px;
    font-size: 16px;
    margin: 0 8px;
    background-color: var(--card-active-background-color);
  }
  .cancelButton {
    color: var(--text-color);
  }

  .confirmButton {
    color: #ff0000;
  }

  .deleteAction {
    color: #ff0000;
    font-size: 16px;
    font-weight: 500;
  }
}
.modalBox {
  --min-width: 368px;
  :global {
    .adm-popover {
      --background: var(--modal-content-background-color);
    }
    .adm-popup-body {
      background: var(--modal-content-background-color);
    }
  }
}
