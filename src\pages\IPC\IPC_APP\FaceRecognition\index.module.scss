.container {
  min-height: calc(100vh - 35px);
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  background-color: var(--background-color);

  .header {
    padding: 0 10px;
    .backIcon {
      width: 40px;
      height: 40px;
    }
  }
  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px 16px;
  }
}

.content {
  flex: 1;
  padding: 0;
}

.list {
  margin-top: 10px;
  --border-inner: 0;
  --border-top: 0;
  --border-bottom: 0;
  // border-radius: 16px;
  :global{
    a.adm-list-item:active:not(.adm-list-item-disabled){
      background-color: var(--background-color);
    }
  }
}

.item {
  font-size: 16px;
  color: var(--text-color);
  padding: 0 0 0 16px;
  background-color: var(--background-color);
}

.label {
  font-size: 16px;
  color: var(--text-color);
}

.switch {
  flex-shrink: 0;
}
