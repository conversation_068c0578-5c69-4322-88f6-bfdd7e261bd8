.root_container {
  width: 100%;
  height: 100%;
  padding-top: 35px;
  background-color: var(--background-color);
}

.content {
  width: 100%;
  height: calc(100% - 60px);
  padding: 8px 28px;
}

.header {
  width: 100%;
}

.header_span {
  font-family: MiSans W;
  font-weight: 400;
  font-size: 32px;
  line-height: 100%;
  letter-spacing: 0px;
  color: var(--text-color);
}

.body {
  padding: 12px 8px;
}

.show_backup_machine_container {
  display: flex;
  width: 100%;
  height: 210px;
  background: rgba(217, 217, 217, 1);
  border-radius: 12px;

  justify-content: center;
  align-items: center;
}

.backup_machine_img {
  height: 100px;
}

.body_span_container {
  margin-top: 12px;
}

.body_span {
  font-family: MiSans;
  font-weight: 400;
  font-size: 10px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(116, 116, 116, 1);
}

.list_right_container {
  width: 230px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.list_right_container_disabled {
  opacity: 0.5;
  pointer-events: none;
}

.list_right_container_span {
  font-family: MiSans;
  font-weight: 400;
  font-size: 14px;

  width: 200px;
  white-space: normal;
  word-break: break-all;
}

.list_right_container_img {
  height: 18px;
}

.storage_plane {
  :global {
    .adm-floating-panel-header {
      background-color: var(--background-color) !important;
    }
  }
}

.float_panel_container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: var(--background-color);
}

// form 样式

.form_container {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
}

// number Input 组件的样式

.number_input_container {
  width: 220px !important;

  :global {
    .ant-input-number-outlined {
      width: 100% !important;
    }
    .ant-input-number-suffix {
      margin: 0 !important;
    }
    .ant-input-number-input-wrap,
    .ant-input-number-input {
      display: flex;
      height: 100%;
    }
  }
}

.number_input_img {
  height: 24px;
}
