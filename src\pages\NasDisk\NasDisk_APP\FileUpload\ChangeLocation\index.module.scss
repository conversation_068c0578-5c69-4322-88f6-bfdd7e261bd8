.uploadBDContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color, #ffffff);
}

.fixedHeader {
  flex-shrink: 0;
  background-color: var(--background-color, #ffffff);
}

.title {
  font-size: 24px;
  padding: 0 16px;
  color: var(--text-color, #000000);
  margin-bottom: 12px;
  font-weight: 500;
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--background-color, #ffffff);
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 8px;
  
  &::-webkit-scrollbar {
    display: none;
  }
  
  .breadcrumbItem {
    color: rgba(255, 178, 29, 1);
    background-color: #FFF4DD;
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    flex-shrink: 0;
    
    &.active {
      background-color: rgba(255, 178, 29, 0.2);
      color: rgba(255, 178, 29, 1);
      font-weight: 500;
    }
    
    &:hover {
      opacity: 0.8;
    }
  }
  
  .breadcrumbSeparator {
    margin: 0 4px;
    color: var(--secondary-text-color, #8C93B0);
    font-size: 12px;
    flex-shrink: 0;
  }
}

.scrollableContent {
  flex: 1;
  overflow-y: auto;
  // height:100%;
  padding-bottom: 140px; // 为底部按钮留出空间
}

// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
  color: var(--secondary-text-color, #8C93B0);
  
  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.fileList {
  padding: 0 16px;
  
  .fileItem {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    border-radius: 8px;
    margin-bottom: 4px;
    transition: background-color 0.2s ease;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    &.selected {
      background-color: rgba(50, 186, 192, 0.1);
      border: 1px solid rgba(50, 186, 192, 0.3);
    }
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    .fileIcon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .folderIcon {
        width: 100%;
        height: 100%;
        background-color: #FFC14F;
        border-radius: 4px;
        mask-size: cover;
        -webkit-mask-size: cover;
        mask-repeat: no-repeat;
        -webkit-mask-repeat: no-repeat;
        mask-position: center;
        -webkit-mask-position: center;
        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M4 4C2.89543 4 2 4.89543 2 6V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V8C22 6.89543 21.1046 6 20 6H12L10 4H4Z' fill='%23FFC14F'/%3E%3C/svg%3E");
        -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none'%3E%3Cpath d='M4 4C2.89543 4 2 4.89543 2 6V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V8C22 6.89543 21.1046 6 20 6H12L10 4H4Z' fill='%23FFC14F'/%3E%3C/svg%3E");
      }
    }
    
    .fileInfo {
      flex: 1;
      overflow: hidden;
      padding-right: 8px;
      
      .fileName {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: var(--text-color, #000000);
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 400;
      }
      
      .fileDetails {
        font-size: 12px;
        color: var(--secondary-text-color, #8C93B0);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  
  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 16px;
    color: var(--secondary-text-color, #8C93B0);
    font-size: 14px;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: var(--background-color);
  z-index: 100;

  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  
  .leftBut {
    width: 167px;
    height: 48px;
    border-radius: 30px;
    background-color: #fff;
    border: 1px solid rgba(116, 197, 255, 1);
    text-align: center;
    font-size: 13px;
    font-weight: 500;
    color: rgba(116, 197, 255, 1);
    
    &:active {
      background-color: rgba(116, 197, 255, 0.1);
    }
    
    &:disabled {
      background-color: #ccc;
      opacity: 0.6;
    }
  }
  
  .rightBut {
    width: 167px;
    height: 48px;
    border-radius: 30px;
    background-color: var(--library-button-bg);
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    
    &:active {
      background-color: #2A9EA3;
    }
    
    &:disabled {
      background-color: #999999;
      opacity: 1;
      cursor: not-allowed;
    }
  }
}


