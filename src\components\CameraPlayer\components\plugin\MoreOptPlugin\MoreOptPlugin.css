.moreOpt-container {
  position: relative;
  flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 8px 0;
}

.moreOpt-select-container {
  position: absolute;
  width: 200px;
  left: 28px;
  bottom: 15px;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  overflow: hidden;
}

.moreOpt-select-container.mobile {
  width: 80px;
  left: 10px;
  bottom: 20px;
}

.moreOpt-select-span {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 18px 28px;
  font-family: MiSans;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
  vertical-align: middle;


  img {
    height: 26px;
    margin-right: 20px;
  }
}

.moreOpt-select-span:hover {
  background-color: var(--primary-color);
}

.moreOpt-select-span-img {
  height: 26px;
  margin-right: 20px;
}

.moreOpt-select-span.mobile {
  padding: 4px 12px;
  font-size: 8px;

  img {
    height: 16px;
    margin-right: 6px;
  }
}

.moreOpt-select-span-img.mobile {
  height: 16px;
  margin-right: 6px;
}

.moreOpt_lookBackDetail_modal_container {
  width: calc(100% / 2);
}

.iconBtn_container_img {
  height: 28px;
  transform: scale(0.5);
  cursor: pointer;
}

.iconBtn_container_img.full.android {
  height: 16px;
}

.iconBtn_container_img.full.pc {
  height: 40px;
}

.iconBtn_container_img.notFull.pc {
  height: 28px;
  transform: scale(1);
}

.iconBtn_container_img.pc.dashboard {
  height: 40px;
  transform: scale(1);
}