import { useCallback, useEffect, useRef, useState } from 'react';
import volumeIcon1 from '@/Resources/player/volume.png';
import mutedIcon1 from '@/Resources/player/nosound.png';
import "./VolumePlugin.css";
import { px2rem } from '@/utils/setRootFontSize';
import Player, { Events } from 'xgplayer/es/player';
import { PreloadImage } from '@/components/Image';
import { getSystemType } from '@/utils/DeviceType';
import { IMonitorPlayerOptions } from '../../MonitorPlayer/MonitorPlayer';

interface IVolumePlugin {
  initVolume?: number
  isMuted?: boolean
  deviceType: 'pc' | 'mobile'
  player: Player | null
  volumeIcon?: string
  mutedIcon?: string
  isFull: boolean // 是否全屏播放
  isDashboard?: boolean
  options?: IMonitorPlayerOptions
}

const VolumePlugin = (props: IVolumePlugin) => {
  const { initVolume, deviceType, player, volumeIcon, mutedIcon, isFull, isDashboard, options } = props;
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [volume, setVolume] = useState<number>(initVolume ? initVolume * 100 : player ? player.volume : 0); // 默认音量0%
  const [isMuted, setIsMuted] = useState<boolean>(props.isMuted ? props.isMuted : false); //静音
  const [previousVolume, setPreviousVolume] = useState<number>(0);
  const [isTouching, setIsTouching] = useState(false);
  const sliderRef = useRef<HTMLInputElement>(null);
  const isAndroid = getSystemType() === 'android';
  const isIos = getSystemType() === 'ios';

  // 通用音量更新逻辑
  const updateVolume = useCallback((value: number) => {
    const clampedValue = Math.max(0, Math.min(100, value));
    setVolume(clampedValue);
    if (player) {
      player.volume = clampedValue / 100;
    }
    if (clampedValue > 0) { setIsMuted(false) } else { setIsMuted(true) }
  }, [player]);

  // 移动端触摸处理
  const handleTouchStart = (e: React.TouchEvent) => {
    setIsTouching(true);
    const touch = e.touches[0];
    if (sliderRef.current) {
      const rect = sliderRef.current.getBoundingClientRect();
      const percentage = (touch.clientY - rect.top) / rect.height;
      updateVolume(100 - percentage * 100);
    }
  };

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isTouching) return;
    const touch = e.touches[0];
    if (sliderRef.current) {
      const rect = sliderRef.current.getBoundingClientRect();
      let percentage = 0;
      if (isIos && player && player.isFullscreen) {
        percentage = (touch.clientX - rect.left) / rect.width;
      } else {
        percentage = 1 - (touch.clientY - rect.top) / rect.height;
      }
      updateVolume(percentage * 100);
    }
  }, [isIos, isTouching, player, updateVolume]);

  // PC端鼠标逻辑
  const handleMouseChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateVolume(Number(e.target.value));
  }, [updateVolume]);

  // 点击静音切换
  const handleMobileMute = useCallback(() => {
    if (!isHovered) { setIsHovered(true); return; }
    const nowMuted = !isMuted;
    setIsMuted(nowMuted);
    if (nowMuted) {
      console.log(player?.volume);
      player && setPreviousVolume(player.volume * 100);
      updateVolume(0);
      return;
    }
    player && updateVolume(Math.max(previousVolume, 10));
  }, [isHovered, isMuted, player, previousVolume, updateVolume]);

  useEffect(() => {
    if (player) {
      player.on(Events.VOLUME_CHANGE, () => {
        setVolume(player.volume * 100);
        if (player.volume === 0) {
          setIsMuted(true);
        } else {
          setIsMuted(false);
        }
      })
    }
  }, [player])

  useEffect(() => {
    if (player) {
      setIsMuted(player.muted);
    }
  }, [player])

  return (
    <div className="volume-container" onMouseEnter={() => deviceType === 'pc' ? setIsHovered(true) : null} onMouseLeave={() => setIsHovered(false)} onTouchEnd={() => setIsTouching(false)} onTouchCancel={() => setIsTouching(false)}>
      {
        isMuted ?
          <PreloadImage className={`iconBtn_container_img ${isFull ? 'full' : 'notFull'} ${options?.type}  ${isAndroid ? 'android' : ''} ${deviceType} ${isDashboard && !isFull ? 'dashboard' : ''}`} onClick={handleMobileMute} src={mutedIcon ? mutedIcon : mutedIcon1} alt="muted" /> :
          <PreloadImage className={`iconBtn_container_img ${isFull ? 'full' : 'notFull'} ${options?.type}  ${isAndroid ? 'android' : ''} ${deviceType} ${isDashboard && !isFull ? 'dashboard' : ''}`} onClick={handleMobileMute} src={volumeIcon ? volumeIcon : volumeIcon1} alt="volume" />
      }

      {/* 音量调节条 */}
      {isHovered && (
        <div className="volume-slider-container" onTouchStart={handleTouchStart} onTouchMove={handleTouchMove}>
          <input ref={sliderRef} type="range" min="0" max="100" step="1" value={volume} onChange={handleMouseChange} className="volume-slider" style={{ height: deviceType === 'mobile' ? px2rem("5px") : '', touchAction: 'none' }} />
        </div>
      )}
    </div>
  );
};

export default VolumePlugin;