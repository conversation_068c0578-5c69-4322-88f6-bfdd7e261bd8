import { Route, Switch, useRouteMatch } from "react-router-dom";
import styles from "./index.module.scss";
import FaceManagement from "./FaceManagement";
import FaceDetail from "./FaceDetail";

const FaceRecognition = () => {
  const { path } = useRouteMatch();

  return (
    <div className={styles.container}>
      <Switch>
      <Route exact path={`${path}/faceDetail/:id/:name`} component={FaceDetail} />
        <Route
          exact
          path={"/cameraManagement_pc/faceRecognition/faceManagement"}
          component={FaceManagement}
        />
      </Switch>
    </div>
  );
};

export default FaceRecognition;
