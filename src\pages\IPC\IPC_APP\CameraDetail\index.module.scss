/* 核心解决方案 */
html {
  height: 100%;
  overflow: hidden; /* 禁用默认滚动 */
}

body {
  height: 100%;
  margin: 0;
  overflow-y: auto; /* 启用滚动 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

/* Webkit浏览器滚动条隐藏 */
body::-webkit-scrollbar {
  width: 0;
  height: 0;
  background: transparent;
}

/* 保证内容容器可滚动 */
.cameraPlayer_container {
  height: 100%;
  min-height: calc(100vh - 35px);
  box-sizing: border-box;
  overflow-y: auto;
}

.cameraPlayer_container_error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cameraPlayer_content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 60px);
  scrollbar-width: none;
  overflow-y: auto;
}

.cameraPlayer_eventList {
  width: 100%;
  padding: 14px 28px;
}

.cameraPlayer_default_container {
  flex: 1;
  box-sizing: border-box;
  background-color: var(--camera-background-color);
}

.third_player_container {
  display: flex;
}
