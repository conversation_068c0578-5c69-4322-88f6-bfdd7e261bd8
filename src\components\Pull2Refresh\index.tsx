import { FC, useCallback, useEffect, useRef, useState } from "react";
import styles from './index.module.scss';
import { Loading } from "antd-mobile";

interface PullToRefreshProps {
  onRefresh: () => void;
  isTrigger: boolean;
  containerRef: React.MutableRefObject<HTMLDivElement | null>
  style?: React.CSSProperties;
}

const Pull2Refresh: FC<PullToRefreshProps> = ({ onRefresh, children, isTrigger, containerRef, style }) => {

  const [startY, setStartY] = useState(0);
  // const [currentY, setCurrentY] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const TRIGGER_THRESHOLD = 60; // 触发刷新的阈值（像素）
  const contentRef = useRef<HTMLDivElement>(null); // 子组件区域引用，判断点击区域
  const [isAtTop, setIsAtTop] = useState<boolean>(true); // 当页面滚动到顶部的时候才可以下拉刷新;

  const onScroll = useCallback(() => {
    if (!containerRef.current) return;
    setIsAtTop(containerRef.current.scrollTop === 0);
  }, [containerRef])

  useEffect(() => {
    if (!containerRef.current) return;
    const container = containerRef.current;
    container.addEventListener('scroll', onScroll);

    return () => container.removeEventListener('scroll', onScroll);
  }, [containerRef, onScroll])

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (!isTrigger || !contentRef.current) return;

    // 判断点击区域是否在内容区域
    const touch = e.touches[0];
    const contentRect = contentRef.current.getBoundingClientRect();
    const isInContent = (
      touch.clientX >= contentRect.left &&
      touch.clientX <= contentRect.right &&
      touch.clientY >= contentRect.top &&
      touch.clientY <= contentRect.bottom
    );

    if (isAtTop && isInContent && !isLoading) {
      setStartY(e.touches[0].pageY);
    }
  }, [isLoading, isAtTop, isTrigger]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (startY === 0 || isLoading) return;

    const currentPosition = e.touches[0].pageY;
    // setCurrentY(currentPosition);
    if (currentPosition > startY) {
      const distance = Math.min(currentPosition - startY, TRIGGER_THRESHOLD * 2);
      setPullDistance(distance);
    }
  }, [isLoading, startY]);

  const handleTouchEnd = useCallback(() => {
    if (pullDistance > TRIGGER_THRESHOLD) {
      setIsLoading(true);
      try {
        onRefresh();
      } finally {
        setIsLoading(false);
        setPullDistance(0);
        setStartY(0);
      }
    } else {
      setPullDistance(0);
      setStartY(0);
    }
  }, [onRefresh, pullDistance]);

  useEffect(() => {
    const content = contentRef.current;
    if (!content) return;

    content.addEventListener('touchstart', handleTouchStart);
    content.addEventListener('touchmove', handleTouchMove, { passive: false });
    content.addEventListener('touchend', handleTouchEnd);

    return () => {
      content.removeEventListener('touchstart', handleTouchStart);
      content.removeEventListener('touchmove', handleTouchMove);
      content.removeEventListener('touchend', handleTouchEnd);
    };
  }, [pullDistance, isLoading, handleTouchStart, handleTouchMove, handleTouchEnd]);

  return (
    <div className={styles.ptr_container} style={{ ...style }}>
      <div className={`${styles.ptr_indicator} ${isLoading ? styles.loading : ''}`} style={{ height: pullDistance }}>
        {isLoading ? (
          <Loading />
        ) : <>
          {
            pullDistance > TRIGGER_THRESHOLD ?
              <div className={styles.pull_status_container}>
                <div style={{ width: pullDistance / 4, height: pullDistance / 4, borderRadius: pullDistance < 45 ? pullDistance : '45px' }} className={styles.pull_icon}></div>
                <span>松开刷新</span>
              </div>
              : <div className={styles.pull_status_container}>
                <div style={{ width: pullDistance / 4, height: pullDistance / 4, borderRadius: pullDistance < 45 ? pullDistance : '45px' }} className={styles.pull_icon}></div>
                <span style={{ opacity: pullDistance === 0 ? 0 : 1 }}>下拉刷新</span>
              </div>
          }
        </>}
      </div>
      <div className={styles.ptr_content} ref={contentRef}>
        {children}
      </div>
    </div>
  )
}

export default Pull2Refresh;