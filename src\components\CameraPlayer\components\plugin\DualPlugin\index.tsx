import Player, { Events, IPlayerOptions } from "xgplayer/es/player";
import { IDualOptions, PlayerType, splitURL } from "../../MonitorPlayer/MonitorPlayer";
import { useEffect, useMemo } from "react";
import styles from "./index.module.scss";
import { px2rem } from "@/utils/setRootFontSize";
import HlsPlugin from "xgplayer-hls";
import { getSystemType, getWebDavInfo } from "@/utils/DeviceType";

interface IDualPlugin {
  cameraRef: React.MutableRefObject<Player | null> // 单摄

  mediaName: string
  type: PlayerType,
  width: string,
  isPlay: boolean
}

const DualPlugin = (props: IDualPlugin & IDualOptions) => {
  const { urls, cameraRef, mediaName, cameraRef_secondary, cameraRef_third, width, isPlay } = props;

  const webDAVParams = getWebDavInfo();

  const headersParams = useMemo(() => {
    return {
      'Authorization': `Basic ${btoa(`${webDAVParams.username}:${webDAVParams.password}`)}`
    }
  }, [webDAVParams.password, webDAVParams.username])

  // 双摄播放器配置
  const second_config: IPlayerOptions = useMemo(() => {
    if (!cameraRef.current) return {};
    const os = getSystemType();
    const curWidth = px2rem(Number(width.split('px')[0]) * 0.295 + 'px');
    const isMain: boolean = splitURL(urls.main) === cameraRef.current.config.url;
    const url = splitURL(isMain ? urls.secondary : urls.main);

    console.log(`此时主画面为${isMain ? '主' : '副'}摄,该播放器路径应该为:`, url);
    return {
      id: `${mediaName}_secondary`,
      width: curWidth,
      height: Number(curWidth.split('rem')[0]) * 0.5625 + "rem",
      autoplay: true,
      autoplayMuted: true,
      url: url,
      isLive: true,
      playsinline: true,
      lang: "zh",
      fullscreen: {
        rotateFullscreen: os === 'ios' ? true : false,
        useScreenOrientation: true,
        lockOrientationType: "landscape",
      },
      mobile: {
        gestureY: true
      },
      plugins: [HlsPlugin],
      videoAttributes: {
        crossOrigin: "anonymous"
      },
      replay: false,
      ignores: ['enter', 'error', 'start', 'control'],
      hls: {
        fetchOptions: {
          mode: 'cors',
          headers: {
            ...headersParams
          }
        }
      }
    }
  }, [cameraRef.current?.config.url, headersParams, mediaName, urls.main, urls.secondary, width]);

  // 三摄播放器配置
  const third_config: IPlayerOptions | undefined = useMemo(() => {
    if (!cameraRef.current || !urls.third) return;
    const os = getSystemType();
    const curWidth = px2rem(Number(width.split('px')[0]) * 0.295 + 'px');
    const isMain: boolean = splitURL(urls.main) === cameraRef.current.config.url;
    const url = splitURL(isMain ? urls.third : urls.main);

    console.log(`此时主画面为${isMain ? '主' : '三'}摄,该播放器路径应该为:`, url);
    return {
      id: `${mediaName}_third`,
      width: curWidth,
      height: Number(curWidth.split('rem')[0]) * 0.5625 + "rem",
      autoplay: true,
      autoplayMuted: true,
      url: url,
      isLive: true,
      playsinline: true,
      lang: "zh",
      fullscreen: {
        rotateFullscreen: os === 'ios' ? true : false,
        useScreenOrientation: true,
        lockOrientationType: "landscape",
      },
      mobile: {
        gestureY: true
      },
      plugins: [HlsPlugin],
      videoAttributes: {
        crossOrigin: "anonymous"
      },
      replay: false,
      ignores: ['enter', 'error', 'start', 'control'],
      hls: {
        fetchOptions: {
          mode: 'cors',
          headers: {
            ...headersParams
          }
        }
      }
    }
  }, [cameraRef.current?.config.url, headersParams, mediaName, urls.main, urls.third, width]);

  useEffect(() => {
    if (!cameraRef.current) return;

    // 创建新实例
    const second_player = new Player(second_config);

    if (third_config && cameraRef_third) { // 三摄配置及ref存在时，渲染三摄单位
      const third_player = new Player(third_config);

      third_player.on(Events.ERROR, async () => {
        third_player.config.url = splitURL(urls.third!);
        third_player.resetState();
        await third_player.play()
      })
      cameraRef_third.current = third_player;
    }


    if (cameraRef_secondary) {
      // 双摄监听
      second_player.on(Events.ERROR, async () => {
        second_player.config.url = splitURL(urls.secondary);
        second_player.resetState();
        await second_player.play()
      })
      cameraRef_secondary.current = second_player;
    }

    return () => {
      second_player.destroy();
      if (cameraRef_third && cameraRef_third.current) {
        cameraRef_third.current.destroy();
      }
    }
  }, [cameraRef.current?.config.url, second_config, cameraRef_secondary, cameraRef, urls.secondary, third_config, cameraRef_third, urls.third]);

  useEffect(() => {
    if (cameraRef.current && cameraRef_secondary && cameraRef_secondary.current) {
      if (isPlay) {
        cameraRef_secondary.current.config.url = splitURL(urls.secondary);
        cameraRef_secondary.current.resetState();
        cameraRef_secondary.current.play();
      } else {
        cameraRef_secondary.current.pause();
      }
    }
  }, [cameraRef, cameraRef_secondary, isPlay, urls.secondary])

  return (
    <div className={styles.container}>
      <div className={styles.dual_container} onClick={() => {
        if (cameraRef.current && cameraRef_secondary && cameraRef_secondary.current) {
          const url = !(urls.main === cameraRef.current?.config.url) ? urls.main : urls.secondary;
          const s_url = (urls.main === cameraRef.current?.config.url) ? urls.main : urls.secondary;
          console.log('切换路径:', splitURL(url));
          cameraRef.current.switchURL(splitURL(url));
          cameraRef_secondary.current?.switchURL(splitURL(s_url));
        }
      }}>
        <div id={`${mediaName}_secondary`} />
      </div>

      {
        (cameraRef.current && cameraRef_third && cameraRef_third.current && urls.third) && (
          <div className={styles.third_container} onClick={() => {
            if (cameraRef.current && cameraRef_third && cameraRef_third.current && urls.third) {
              const url = !(urls.main === cameraRef.current?.config.url) ? urls.main : urls.third;
              const s_url = (urls.main === cameraRef.current?.config.url) ? urls.main : urls.third;
              console.log('切换路径:', splitURL(url));
              cameraRef.current.switchURL(splitURL(url));
              cameraRef_third.current?.switchURL(splitURL(s_url));
            }
          }}>
            <div id={`${mediaName}_third`} />
          </div>
        )
      }
    </div>
  )
}

export default DualPlugin;