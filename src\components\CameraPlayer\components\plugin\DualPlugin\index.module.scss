.container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dual_container {
  padding: 2px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2%;

  img {
    width: 120px;
  }
}

.third_container {
  padding: 2px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2%;

  img {
    width: 120px;
  }
}
