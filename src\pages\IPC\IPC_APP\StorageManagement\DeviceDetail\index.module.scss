.container {
  background-color: var(--background-color);
  min-height: calc(100vh - 35px);
  // padding: 0 10px;

  .header {
    padding: 0 10px;
    .backIcon {
      width: 40px;
      height: 40px;
    }
  }
  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px 16px;
  }

  .deviceInfo {
    display: flex;
    align-items: center;
    padding: 16px 28px;

    .cameraImg {
      width: 60px;
      height: 60px;
    }

    .deviceName {
      height: 60px;
      margin-left: 3px;
      div {
        font-size: 20px;
        font-weight: 400;
        color: var(--title-color);
      }

      .networkInfo {
        display: flex;
        align-items: center;
        margin-top: 10px;
        span {
          font-size: 12px;
          color: #999;
          margin-right: 12px;
          display: flex;
          align-items: center;

          .highest,
          .minimum {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
  .thinLine {
    position: relative;
    margin: 0 16px;
  }

  .thinLine::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background: var(--thinLine-background-color);
    transform: scaleY(0.5);
    transform-origin: 0 100%;
  }

  .toggleSection {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 16px;
    :global {
      .adm-switch.adm-switch-checked .adm-switch-checkbox {
        background-color: var(--primary-color);
      }
    }

    span {
      font-size: 16px;
      color: var(--text-color);
    }
  }

  .infoList {
    margin-bottom: 24px;
    padding: 0 16px;
    .information {
      color: #8c93b0;
      font-size: 12px;
      padding: 16px 0;
    }

    .infoItem {
      display: flex;
      justify-content: space-between;
      padding: 12px 0;
      &:last-child {
        .infoValue {
          color: inherit;
          > span {
            color: inherit;
          }
        }
      }

      .infoLabel {
        font-size: 16px;
        color: var(--text-color);
      }

      .infoValue {
        font-size: 14px;
        color: var(--list-value-text-color);
        display: flex;
        align-items: center;
        .arrow {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
  .manage {
    color: #8c93b0;
    font-size: 12px;
    padding: 16px;
  }

  .manageSection {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 24px;

    span {
      font-size: 16px;
      color: var(--text-color);
    }
  }

  .buttonGroup {
    display: flex;
    gap: 40px;
    padding: 16px;

    button {
      flex: 1;
      height: 50px;
      border-radius: 16px;
      color: var(--emergency-text-color);
      background-color: var(--cancel-btn-background-color);
      border: none;
      font-weight: 500;
      font-size: 16px;

      &:first-child {
        color: var(--emergency-text-color);
      }

      &:last-child {
        color: var(--emergency-text-color);
      }
    }
  }

  .modalContent {
    .modalHeader {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
      color: var(--text-color);
    }

    .remarkInput {
      border-radius: 8px;
      padding: 12px ;
      margin-bottom: 16px;
      font-size: 14px;
      background-color: var(--cancel-btn-background-color);
      box-sizing: border-box;
      // :global {
      //   .adm-input-element {
      //     padding-left: 12px;
      //   }
      // }
    }

    .modalFooter {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;

      button {
        flex: 1;
        padding: 12px;
        border-radius: 8px;
        border: none;
        font-size: 16px;
      }

      .cancelButton {
        margin-right: 8px;
        background-color: var(--cancel-btn-background-color);
        color: var(--title-color);
      }

      .confirmButton {
        margin-left: 8px;
        background-color: var(--primary-color);
        color: #fff;
      }
    }
  }
  :global {
    .adm-modal-body {
      border-radius: 16px;
      background-color: var(--modal-content-background-color);
    }
    .adm-modal-content {
      padding: 0 36px 12px;
    }
    .adm-input-element {
      color: var(--text-color);
    }
  }
}
.modalBox{
  --min-width: 368px;
}
// :global {
//   .adm-toast-mask .adm-toast-main {
//     background-color: var(--modal-content-background-color);
//     color: var(--title-color);
//     font-size: 13px;
//   }
// }
.btnDisabled{
  opacity: 0.5;
  cursor: not-allowed;
}