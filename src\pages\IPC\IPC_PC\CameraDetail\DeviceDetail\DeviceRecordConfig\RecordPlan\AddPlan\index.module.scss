body {
  :global {
    .adm-popup {
      z-index: 10003;
    }
  }
}

.container {
  :global {
    .adm-center-popup-wrap {
      width: 30%;
    }
  }
  background-color: #f7f8fa;
  padding: 10px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .title {
    font-size: 28px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.8);
    padding: 10px 14px;
  }

  .content {
    padding: 16px 0;
    .listBox {
      --border-inner: 0;
      --border-bottom: 0;
      --border-top: 0;
      --padding-left: 0;
      --padding-right: 0;
      margin-top: 32px;
    }

    .settingList {
      --border-inner: 0;
      --border-bottom: 0;
      --border-top: 0;
      --padding-left: 0;
      --padding-right: 0;
      :global {
        .adm-list-body {
          border-radius: 12px;
        }
      }

      .listAll {
        padding: 0 16px;
        border-radius: 30px;
      }

      .settingItem {
        padding: 16px 0;

        .valueContainer {
          cursor: pointer;
          display: flex;
          align-items: center;
          .arrow {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
    .listBox {
      padding: 24px 16px;
      border-radius: 12px;
      background-color: #fff;
      :global {
        a.adm-list-item:active:not(.adm-list-item-disabled) {
          background-color: transparent;
        }
      }
    }
    .label {
      font-size: 16px;
      color: #000;
      font-weight: 500;
    }
    .value {
      font-size: 13px;
      color: rgba(0, 0, 0, 0.4);
    }
  }
  .thinLine {
    position: relative;
    margin: 0 16px;
  }

  .thinLine::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    transform: scaleY(0.5);
    transform-origin: 0 100%;
  }
}

// 重复模式选择弹窗样式
.repeatPopup {
  padding: 16px;
  background-color: #fff;

  .popupHeader {
    text-align: center;
    margin-bottom: 24px;

    .popupTitle {
      font-size: 18px;
      font-weight: 600;
      color: #000;
    }
  }

  .repeatOptions {
    margin-bottom: 24px;
  }

  .repeatOption {
    padding: 16px 0;
    font-size: 16px;
    color: #000;
    font-weight: 500;
    background-color: rgba(0, 0, 0, 0.04);
    margin-top: 10px;
    border-radius: 16px;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }
    &[data-selected="true"] {
      background-color: rgba(50, 186, 192, 0.1);
      .optionText {
        color: #32bac0;
      }
    }
  }

  .optionContent {
    display: flex;
    align-items: center;
    padding: 0 16px;
  }

  .iconContainer {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    position: relative;
  }

  .checkedIcon {
    width: 20px;
    height: 20px;
    position: absolute;
    left: 0;
    top: 0;
  }

  .optionText {
    flex: 1;
  }

  .popupFooter {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    padding: 0 10px;
  }

  .popupButton {
    flex: 1;
    text-align: center;
    padding: 12px 24px;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.06);
    color: rgba(0, 0, 0, 0.8);
    border-radius: 16px;
    cursor: pointer;

    &.primary {
      background-color: #32bac0;
      font-weight: 500;
      color: #fff;
    }
  }
}

:global {
  .adm-picker-header-button {
    font-size: 16px;
    color: #32bac0;
  }

  .adm-picker-view-column-item {
    font-size: 20px;
  }

  .adm-picker-view-column-item-label {
    font-size: 20px;
  }
}
