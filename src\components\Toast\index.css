#toast-root {
  /* 保持原有定位设置 */
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 10010;
  pointer-events: none;
  width: 100%;
  user-select: none;

  .toast-wrapper {
    /* 修改为弹性布局容器 */
    display: flex;
    flex-direction: column;
    align-items: center; /* 水平居中 */
    padding: 20px 0; /* 添加垂直间距 */
    min-height: 100vh; /* 确保垂直居中 */
  }
}

.toast-item {
  /* 核心修改点 */
  display: inline-flex; /* 改为行内弹性盒 */
  max-width: 80%; /* 防止过宽 */
  min-width: 80px; /* 最小宽度保证可读性 */
  width: auto !important; /* 覆盖可能的宽度限制 */
  justify-content: center;
  
  /* 保持原有样式 */
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;
  pointer-events: auto;
  opacity: 0;
  transform: translateY(-30px);
  
  /* 调整定位方式 */
  position: absolute;
  top: 50%;
  
  /* 文本处理 */
  white-space: nowrap; /* 不换行 */

  &.enter {
    opacity: 1;
    transform: translateY(0);
  }

  &.exit {
    opacity: 0;
    transform: translateY(-20px);
  }

  .toast-content {
    /* 内容自适应 */
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .toast-close {
    /* 调整关闭按钮定位 */
    position: static;
    margin-left: 15px;
  }
}
.bottom {
  top: unset;
  bottom: 100px;
}