import { useState } from 'react';

export interface OptionType {
  label: string;
  value: string;
}

export interface RecordingSettings {
  storagePath: string;
  duration: string;
  mode: string;
  quality?: string; // 仅移动端使用
  estimatedSpace: string;
}

export interface RecordingPopoverState {
  durationPopoverVisible: boolean;
  modePopoverVisible: boolean;
  qualityPopoverVisible?: boolean; // 仅移动端使用
}

export const getRecordingOptions = () => {
  // 基础选项
  const durationOptions: OptionType[] = [
    { label: "7天", value: "7天" },
    { label: "15天", value: "15天" },
    { label: "30天", value: "30天" },
    { label: "90天", value: "90天" },
  ];

  const modeOptions: OptionType[] = [
    { label: "连续录制", value: "连续录制" },
    { label: "事件触发", value: "事件触发" },
  ];

  // PC端与App端的清晰度选项可能不同
  const qualityOptions: OptionType[] = [
    { label: "480P", value: "480P" },
    { label: "720P", value: "720P" },
    { label: "1080P", value: "1080P" },
    { label: "4K", value: "4K" },
  ];

  return {
    durationOptions,
    modeOptions,
    qualityOptions
  };
};

// 共享的摄像机录制设置Hook
export const useRecordingSettings = (isAppMode: boolean = false) => {
  // 默认配置可根据平台不同设置不同的初始值
  const initialSettings: RecordingSettings = {
    storagePath: isAppMode ? "Xiaomi家庭存储/MI_IPC" : "Xiaomi家庭存储/MI_IPC",
    duration: "30天",
    mode: "连续录制",
    quality: isAppMode ? "4K" : undefined,
    estimatedSpace: "54.76GB"
  };

  const [settings, setSettings] = useState<RecordingSettings>(initialSettings);
  
  // 弹出选择器状态
  const [popoverState, setPopoverState] = useState<RecordingPopoverState>({
    durationPopoverVisible: false,
    modePopoverVisible: false,
    qualityPopoverVisible: isAppMode ? false : undefined
  });

  // 更新录像时长
  const handleDurationChange = (value: string) => {
    setSettings(prev => ({ ...prev, duration: value }));
    setPopoverState(prev => ({ ...prev, durationPopoverVisible: false }));
    return value;
  };

  // 更新录制模式
  const handleModeChange = (value: string) => {
    setSettings(prev => ({ ...prev, mode: value }));
    setPopoverState(prev => ({ ...prev, modePopoverVisible: false }));
    return value;
  };

  // 更新录制清晰度 (仅移动端)
  const handleQualityChange = (value: string) => {
    if (isAppMode) {
      setSettings(prev => ({ ...prev, quality: value }));
      setPopoverState(prev => ({ ...prev, qualityPopoverVisible: false }));
    }
    return value;
  };

  // 更新单个Popover状态
  const updatePopoverVisibility = (key: keyof RecordingPopoverState, value: boolean) => {
    setPopoverState(prev => ({ ...prev, [key]: value }));
  };

  // 计算预估存储空间
  const calculateEstimatedSpace = () => {
    const estimatedSpace = "54.76GB";
    setSettings(prev => ({ ...prev, estimatedSpace }));
    return estimatedSpace;
  };

  return {
    settings,
    setSettings,
    popoverState,
    updatePopoverVisibility,
    handleDurationChange,
    handleModeChange,
    handleQualityChange,
    calculateEstimatedSpace
  };
}; 