.modal {
  :global(.ant-modal-content) {
    border-radius: 20px;
    padding: 0;
    overflow: hidden;
    background-color: var(--desktop-modal-bg-color);
    width: 546px;
    height: 636px;
  }

  :global(.ant-modal-close) {
    left: 15px;
  }

  :global(.ant-modal-header) {
    padding: 20px 24px 0px;
    background-color: var(--desktop-modal-bg-color);
    text-align: center;

    :global(.ant-modal-title) {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-color);
    }
  }

  :global(.ant-modal-body) {
    padding: 0;
  }
}

.container {
  display: flex;
  flex-direction: column;
  height: 570px;
}

.scrollableContent {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  gap: 10px;
}

.userList {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

.userItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;

  &:last-child {
    border-bottom: none;
  }
}

.userInfo {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  margin-right: 12px;
  border-radius: 30%;
}

.userDetails {
  flex: 1;
}

.userName {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.userPosition {
  font-size: 12px;
  color: #999;
}

.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  font-size: 14px;
}

.selectedCount {
  text-align: center;
  padding: 10px;
  color: #666;
  font-size: 14px;
}

.checkbox {
  :global(.ant-checkbox-inner) {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

}

.footer {
  display: flex;
  justify-content: center;
  background-color: var(--desktop-modal-bg-color);
  :global(.ant-btn-variant-outlined:disabled){
    background-color: var(--primary-color);
    border:none;
    opacity: 0.3;
    color: var(--text-color);
  }
  :global(.ant-btn-variant-outlined:not(:disabled):hover) {
    opacity: 0.5;
    background: var(--primary-color);
    color: var(--text-color);
  }
}

.confirmButton {
  width: 336px;
  height: 50px;
  border-radius: 16px;
  font-size: 17px;
  background-color: var(--primary-color);
  color: var(--text-color);
  border:none;
}