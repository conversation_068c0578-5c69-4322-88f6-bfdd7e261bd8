import React, { FC, useEffect } from "react";
import { useHistory, useRouteMatch, Switch, Route } from "react-router-dom";
import DownloadNas from "./DownloadNas";
import UploadBD from "./UploadBD";
import styles from './index.module.scss';

const TaskManager: FC = () => {
  const { path, url } = useRouteMatch();
  const history = useHistory();

  useEffect(() => {
    // 默认跳转到下载任务页面
    if (history.location.pathname === path) {
      history.push(`${path}/downloadNas`);
    }
  }, [history, path]);

  const handleNavigation = (route: string) => {
    history.push(`${url}/${route}`);
  };

  const isActive = (route: string) => {
    return history.location.pathname.includes(route);
  };

  return (
    <div className={styles.task_manager_container}>
      {/* 导航栏 */}
      <div className={styles.nav_bar}>
        <div className={styles.nav_items}>
          <div 
            className={`${styles.nav_item} ${isActive('downloadNas') ? styles.active : ''}`}
            onClick={() => handleNavigation('downloadNas')}
          >
            <span>下载任务</span>
          </div>
          <div 
            className={`${styles.nav_item} ${isActive('uploadBD') ? styles.active : ''}`}
            onClick={() => handleNavigation('uploadBD')}
          >
            <span>上传任务</span>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className={styles.content_area}>
        <Switch>
          <Route path={`${path}/downloadNas`} component={DownloadNas} />
          <Route path={`${path}/uploadBD`} component={UploadBD} />
        </Switch>
      </div>
    </div>
  );
};

export default TaskManager;