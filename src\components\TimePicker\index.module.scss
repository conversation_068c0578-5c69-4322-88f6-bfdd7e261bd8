.timePickerPopup {
  padding: 16px;
  background-color: var(--modal-content-background-color);
  border-radius: 16px;
  margin: 0;
  
  .popupHeader {
    text-align: center;
    margin-bottom: 24px;
    
    .popupTitle {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
    }
  }
  
  .timePickerContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0 30px;
    position: relative;
    overflow: hidden;
    
    &::before, &::after {
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 85%;
      height: 1px;
      background: var(--thinLine-background-color);
      z-index: 1;
    }
    
    &::before {
      top: calc(50% - 20px);
    }
    
    &::after {
      bottom: calc(50% - 20px);
    }
    
    .timeColumn {
      position: relative;
      text-align: center;
      margin: 0 18px;
      width: 60px;
      height: 170px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      touch-action: pan-y;
      overflow: hidden;
      -webkit-overflow-scrolling: touch;
      
      .timeColumnInner {
        display: flex;
        flex-direction: column;
        width: 100%;
        will-change: transform;
        transition: transform 0.2s cubic-bezier(0.1, 0.7, 0.1, 1);
        
        .timeItemPrev, .timeItemNext {
          color: var(--title-color);
          font-size: 16px;
          opacity: 0.7;
          padding: 8px 0;
          cursor: pointer;
          transition: opacity 0.15s;
          user-select: none;
          
          &:hover {
            opacity: 0.9;
          }
        }
        
        .timeItemFar {
          color: var(--title-color);
          font-size: 14px;
          opacity: 0.4;
          padding: 6px 0;
          user-select: none;
        }
        
        .timeItemCurrent {
          color: var(--primary-color);
          font-size: 24px;
          font-weight: 500;
          padding: 12px 0;
          position: relative;
          z-index: 2;
          display: flex;
          justify-content: center;
          align-items: baseline;
          user-select: none;
        }
        
        .timeNumber {
          display: inline-block;
        }
        
        .timeUnitInline {
          display: inline-block;
          font-size: 14px;
          margin-left: 2px;
          color: var(--primary-color);
        }
      }
      
      .timeUnit {
        position: absolute;
        color: var(--title-color);
        font-size: 14px;
        right: -18px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
      }
    }
  }
  
  .popupFooter {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    margin-top: 0;
    
    .popupButton {
      flex: 1;
      text-align: center;
      padding: 12px 0;
      font-size: 16px;
      background-color: var(--cancel-btn-background-color);
      color: var(--title-color);
      border-radius: 16px;
      user-select: none;
      cursor: pointer;
      
      &.primary {
        background-color: var(--primary-color);
        font-weight: 500;
        color: #fff;
      }
    }
  }
} 

// 新增滚轮反馈样式
.wheelActive {
  transform: scale(0.98);
  transition: transform 0.15s ease;
  
  .timeItemCurrent {
    color: var(--adm-color-primary);
  }
}