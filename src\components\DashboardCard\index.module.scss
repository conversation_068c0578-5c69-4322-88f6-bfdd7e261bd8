.card_container {
  width: 360px;
  height: 190px;
  background-color: var(--background-color);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
}

.card_container.pc {
  width: 310px;
  height: 310px;
}

.card_status_bar {
  display: flex;
  align-items: center;
  padding: 20px;
  border-top-right-radius: 20px;
  border-top-left-radius: 20px;

  img {
    width: 30px;
    height: 30px;
    margin-right: 5px;
  }
}

.card_status_bar.pc {
  background-color: var(--modal-content-background-color);
}

.card_status_bar_text {
  display: flex;
  flex-direction: column;
  font-family: MiSans W;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  color: var(--text-color);
}

.card_status_bar_text.pc {
  font-family: MiSans W;
  font-weight: 400;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0px;

  span {
    margin: 4px;
  }
}

.subtitle {
  font-family: MiSans W;
  font-weight: 300;
  font-size: 11.96px;
  line-height: 100%;
  letter-spacing: 0px;
  color: var(--text-color);
}

.card_content {
  flex: 1;
  color: var(--text-color);
  padding: 20px;
}

.card_content.pc {
  padding: 0;
}

.card_content:hover {
  cursor: pointer;
}
