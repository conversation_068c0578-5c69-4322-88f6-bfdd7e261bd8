import { getDeviceType, DeviceType } from './DeviceType';
let baseValue: number = 39.2;
const setRootFontSize = () => {
    const clientWidth = document.documentElement.clientWidth; // 获取设备宽度
    let designWidth: number;


    // 根据设备类型设置不同的设计稿宽度和基准值
    const deviceType = getDeviceType();
    switch (deviceType) {
        case DeviceType.Mobile:
            designWidth = 392; // 手机设计稿宽度
            break;
        case DeviceType.Desktop:
            designWidth = 1234; // PC设计稿宽度
            break;
        case DeviceType.TV:
            designWidth = 1920; // TV设计稿宽度
            break;
        default:
            designWidth = 392; // 默认手机设计稿宽度
            break;
    }
    const rootFontSize = (baseValue * (clientWidth / designWidth)) + 'px'; // 计算根元素的字体大小
    document.documentElement.style.fontSize = rootFontSize; // 设置根元素的字体大小
}
export default setRootFontSize;

//行内元素里的px转换成rem

export const px2rem = (px: string) => {
    if (/%/ig.test(px)) {
        return px;
    } else {
        const deviceType = getDeviceType();
        if (deviceType === DeviceType.Mobile) {
            const footFontSize = document.documentElement.style.fontSize.split('px')[0];
            return (parseFloat(px) / parseFloat(footFontSize)) + 'rem'; // 修改基准值为根节点字体大小
        }
        return (parseFloat(px) / baseValue) + 'rem'; // 修改基准值为根节点字体大小
    }
}
