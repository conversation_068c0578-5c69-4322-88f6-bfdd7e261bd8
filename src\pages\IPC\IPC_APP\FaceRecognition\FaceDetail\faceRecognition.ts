// 提供通用的数据类型和工具函数，不强制组件使用特定状态管理逻辑

// 定义通用的数据类型
export interface PhotoItem {
  id: string | number;
  src?: string;
  url?: string; // App端使用url，PC端使用src
  selected?: boolean;
  type?: 'add';
}

export interface VideoItem {
  id: string | number;
  camera_lens: string;
  event_name: string;
  time: string;
  media_duration: number;
  cover: string;
  face_file?: string; // 视频封面图里的人脸图
  cover_file?: string; // 视频封面图
  file: string;
  date: string;
  subText?: string;
  isToday?: boolean;
}

// 通用工具函数

/**
 * 更新照片选中状态 
 */
export function togglePhotoSelection(
  photoId: string | number,
  photos: PhotoItem[],
  setPhotos: (photos: PhotoItem[]) => void,
  selectedIds: Array<string | number>,
  setSelectedIds: (ids: Array<string | number>) => void
) {
  const updatedPhotos = photos.map(photo => {
    if (photo.id === photoId) {
      return { ...photo, selected: !photo.selected };
    }
    return photo;
  });

  setPhotos(updatedPhotos);

  // 更新所选照片ID列表
  const newSelectedIds = updatedPhotos
    .filter(photo => photo.selected)
    .map(photo => photo.id);
  
  setSelectedIds(newSelectedIds);
}

/**
 * 删除选中照片
 */
export function deleteSelectedPhotos(
  albumPhotos: PhotoItem[],
  setAlbumPhotos: (photos: PhotoItem[]) => void,
  capturePhotos: PhotoItem[],
  setCapturePhotos: (photos: PhotoItem[]) => void,
  setSelectedPhotos: (ids: Array<string | number>) => void,
  setDeleteToastVisible: (visible: boolean) => void,
  resetEditState: () => void
) {
  // 从相册中删除选中照片
  setAlbumPhotos(albumPhotos.filter(photo => !photo.selected));

  // 从摄像机抓拍中删除选中照片
  setCapturePhotos(capturePhotos.filter(photo => !photo.selected));

  // 显示删除成功提示
  setDeleteToastVisible(true);

  // 2秒后隐藏提示并退出编辑模式
  setTimeout(() => {
    setDeleteToastVisible(false);
    // 清空选中状态并退出编辑模式
    setSelectedPhotos([]);
    resetEditState();
  }, 2000);
}

/**
 * 重置编辑状态
 */
export function resetEditState(
  setIsEditingPhotos: (editing: boolean) => void,
  setSelectedPhotos: (ids: Array<string | number>) => void,
  albumPhotos: PhotoItem[],
  setAlbumPhotos: (photos: PhotoItem[]) => void,
  capturePhotos: PhotoItem[],
  setCapturePhotos: (photos: PhotoItem[]) => void
) {
  setIsEditingPhotos(false);
  setSelectedPhotos([]);
  setAlbumPhotos(albumPhotos.map(photo => ({ ...photo, selected: false })));
  setCapturePhotos(capturePhotos.map(photo => ({ ...photo, selected: false })));
}

/**
 * 工具函数 - 获取照片的图像路径 (兼容src和url属性)
 */
export function getPhotoSrc(photo: PhotoItem): string {
  return photo.src || photo.url || '';
}

/**
 * 工具函数 - 获取视频的描述文本
 */
export function getVideoDesc(video: VideoItem): string {
  return video.subText || '';
} 