.file-selector-container {
  min-height: 100vh;
  background-color: #fff;
  
  .file-storage-info {
    padding: 10px 16px;
    background-color: #f5f5f5;
    font-size: 14px;
    color: #666;
  }
  
  .file-list {
    margin-bottom: 100px; // 留出底部按钮的空间
  }
  
  .file-item {
    display: flex;
    align-items: center;
    padding: 4px 0;
    position: relative;
    
    .file-icon {
      margin-right: 12px;
      font-size: 20px;
      
      img {
        width: 36px;
        height: 36px;
      }
    }
    
    .file-info {
      flex: 1;
      
      .file-name {
        font-size: 16px;
        color: #333;
        margin-bottom: 4px;
      }
      
      .file-time {
        font-size: 12px;
        color: #999;
      }
    }
    
    .expand-arrow {
      margin-left: 8px;
      font-size: 16px;
      transition: transform 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      
      &.expanded {
        transform: rotate(90deg);
      }
    }
  }
  
  .sub-files {
    margin-left: 20px;
    border-left: 1px dashed #ddd;
    
    .file-item-level-1 {
      padding-left: 16px;
    }
    
    .file-item-level-2 {
      padding-left: 32px;
    }
    
    .file-item-level-3 {
      padding-left: 48px;
    }
  }
  
  .footer-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    padding: 12px 16px;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    
    .new-folder-btn {
      flex: 1;
      margin-right: 12px;
      border: 1px solid #ddd;
      background-color: #fff;
      color: #333;
      border-radius: 4px;
      
      .folder-icon {
        margin-right: 4px;
      }
    }
    
    .confirm-btn {
      flex: 1;
      border-radius: 4px;
      background-color: #b1894f;
      border-color: #b1894f;
      
      &:disabled {
        opacity: 0.6;
      }
    }
  }
}

// 覆盖antd-mobile样式
:global {
  .adm-list-item {
    padding: 12px 16px;
  }
  
  .adm-list-item-content-main {
    padding-right: 12px;
  }
  
  .adm-checkbox {
    --icon-size: 20px;
  }
} 