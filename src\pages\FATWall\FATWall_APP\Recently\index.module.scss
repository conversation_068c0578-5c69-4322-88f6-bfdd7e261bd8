.content {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox 兼容 */
  -ms-overflow-style: none; /* IE/Edge 兼容 */
  // padding-bottom: 60px;
  padding: 0 20px;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
  }
}

.router_card_container {
  margin-bottom: 12px;
}

.router_card_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: MiSans;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  color: var(--text-color);
  margin-bottom: 12px;
}
.router_card_header_span {
  height: 20px;
  display: flex;
  align-items: center;
}

.router_card_header_img {
  height: 18px;
}

.filmCardList {
  margin-top: 16px;
  
  // 覆盖 FilmCardList 的默认样式
  :global {
    .card_list_container {
      width: 100%;
      height: 106px; // 82px 卡片高度 + 24px 底部文字空间
      overflow-x: auto;
      overflow-y: hidden;
      display: flex !important;
      gap: 12px;
      scrollbar-width: none;
      -ms-overflow-style: none;
      
      &::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
      }
      
      // 每个卡片容器的样式
      > div {
        flex: none; // 防止卡片收缩
        width: 144px !important;
        height: 82px !important;
        margin-right: 0 !important; // 重置默认margin，使用gap代替
        
        // 卡片内容容器
        > div:first-child {
          width: 144px !important;
          height: 82px !important;
          border-radius: 8px;
        }
        
        // 文字容器
        .text_container {
          width: 144px;
          margin-top: 4px;
          
          .text_container_title {
            font-size: 14px;
            line-height: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .text_container_subtitle {
            font-size: 12px;
            line-height: 16px;
            margin-top: 2px;
          }
        }
      }
    }
  }
}
