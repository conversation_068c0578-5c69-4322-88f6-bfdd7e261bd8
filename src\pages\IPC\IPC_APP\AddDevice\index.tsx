import React from "react";
import { List, Image, Checkbox, Button } from "antd-mobile";
import { Route, useHistory, useRouteMatch } from "react-router-dom";
import styles from "./index.module.scss";
import CameraStatusPage from "@/components/CameraStatusPage";
import supportTest from "@/Resources/camMgmtImg/support-test.png";
import close from "@/Resources/camMgmtImg/close.png";
import searchFail from "@/Resources/camMgmtImg/search-fail.png";
import outline from "@/Resources/camMgmtImg/outline.png";
import searching from "@/Resources/camMgmtImg/searching.png";
import success from "@/Resources/camMgmtImg/success.png";
import closeDark from "@/Resources/camMgmtImg/close-dark.png";
import searchFailDark from "@/Resources/camMgmtImg/search-fail-dark.png";
import outlineDark from "@/Resources/camMgmtImg/outline-dark.png";
import searchingDark from "@/Resources/camMgmtImg/searching-dark.png";
import successDark from "@/Resources/camMgmtImg/success-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { useDeviceManagement } from "./deviceManagement";

const AddDevice = (props: { children?: React.ReactNode }) => {
  const history = useHistory();
  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();

  // 使用共享逻辑Hook
  const {
    fetchState,
    toggleSelect,
    toggleSelectAll,
    handleRetry,
    selectedCount,
    selectedDevices,
  } = useDeviceManagement(supportTest);

  const handleNext = () => {
    console.log("已选择设备:", selectedDevices);
    history.push({
      pathname: `${path}/localRecord`, state: {
        selectedDevices,
      }
    });
  };

  // 渲染主页面内容
  const renderMainContent = () => {
    // 错误状态
    if (fetchState.status === "error") {
      const leftHeaderContent = (
        <Image
          className={styles.close}
          src={isDarkMode ? closeDark : close}
          onClick={() => history.push("/cameraManagement_app")}
        />
      );

      const errorIcon = (
        <Image
          src={isDarkMode ? searchFailDark : searchFail}
          className={styles.errorIcon}
        />
      );

      return (
        <CameraStatusPage
          className={styles.container}
          leftHeaderContent={leftHeaderContent}
          title="选择添加设备"
          icon={errorIcon}
          mainMessage="查找失败，请重试"
          buttonText="重试"
          onButtonClick={handleRetry}
        />
      );
    }

    // 空状态(未找到米系摄像机)
    if (fetchState.status === "empty") {
      const leftHeaderContent = (
        <Image
          className={styles.close}
          src={isDarkMode ? closeDark : close}
          onClick={() => history.push("/cameraManagement_app")}
        />
      );

      const emptyIcon = (
        <Image
          src={isDarkMode ? searchFailDark : searchFail}
          className={styles.placeholderCircle}
        />
      );

      return (
        <CameraStatusPage
          className={styles.container}
          leftHeaderContent={leftHeaderContent}
          title="选择添加设备"
          icon={emptyIcon}
          mainMessage="未找到米系摄像机"
          buttonText="查看支持的摄像机"
          onButtonClick={() =>
            history.push("/cameraManagement_app/supportInformation")
          }
        />
      );
    }

    // 正常状态(加载中或成功)
    return (
      <>
        <div className={styles.header}>
          <Image
            className={styles.close}
            src={isDarkMode ? closeDark : close}
            onClick={() => history.push("/cameraManagement_app")}
          />
          {(fetchState.status === "loading" ||
            fetchState.status === "success") && (
              <Image
                className={styles.outline}
                src={isDarkMode ? outlineDark : outline}
                style={{
                  opacity: fetchState.status === "loading" ? 0.5 : 1,
                  cursor: fetchState.status === "success" ? "pointer" : "default",
                }}
                onClick={
                  fetchState.status === "success" ? toggleSelectAll : undefined
                }
              />
            )}
        </div>
        <div className={styles.title}>选择添加设备</div>

        <div className={styles.content}>
          <div className={styles.list}>
            <div className={styles.all}>
              {fetchState.status === "loading" ? (
                <div className={styles.searching}>
                  <img src={isDarkMode ? searchingDark : searching} alt="" style={{width: '24px', height: '24px'}}/>
                  <span>查找中</span>
                </div>
              ) : fetchState.status === "success" ? (
                <div className={styles.result}>
                  <img src={isDarkMode ? successDark : success} alt="" style={{width: '24px', height: '24px'}}/>
                  <span>找到{fetchState.devices.length}个米系摄像机设备</span>
                </div>
              ) : null}
            </div>

            {fetchState.status === "success" && (
              <>
                <List className={styles.deviceList}>
                  {fetchState.devices.map((device) => (
                    <List.Item
                      key={device.id}
                      className={styles.deviceItem}
                      prefix={
                        <Image
                          src={device.thumbnail}
                          className={styles.thumbnail}
                          fit="cover"
                        />
                      }
                      extra={
                        <div onClick={(e) => e.stopPropagation()}>
                          <Checkbox
                            checked={device.selected}
                            onChange={() => toggleSelect(device.id)}
                            style={{
                              "--icon-size": "18px",
                              "--font-size": "18px",
                              "--gap": "12px",
                            }}
                          />
                        </div>
                      }
                      onClick={() => toggleSelect(device.id)}
                      arrow={false}
                    >
                      <div className={styles.deviceName}>{device.name}</div>
                    </List.Item>
                  ))}
                </List>
              </>
            )}
          </div>
        </div>
        {fetchState.status === "success" && (
          <div className={styles.footer}>
            <Button
              block
              color="primary"
              className={styles.nextButton}
              onClick={handleNext}
              disabled={selectedCount === 0}
            >
              下一步
            </Button>
          </div>
        )}
      </>
    );
  };

  return (
    <div className={styles.container}>
      {props.children}
      <Route exact path={path}>
        {renderMainContent()}
      </Route>
    </div>
  );
};

export default AddDevice;
