.volume-container {
  position: relative;
  flex-direction: column;
  padding: 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
}

.volume-slider-container {
  position: absolute;
  top: -33px;
  padding: 6px 6px;
  background-color: #0000008A;
  transform: rotate(-90deg);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
}

.volume-content {
  display: block;
}

.volume-slider {
  width: 50px;
  height: 3px;
  background: #ffffff73;
  border-radius: 2px;
  cursor: pointer;
}

.iconBtn_container_img {
  height: 28px;
  transform: scale(0.5);
  cursor: pointer;
}

.iconBtn_container_img.full.android {
  height: 16px;
}

.iconBtn_container_img.full.pc {
  height: 40px;
}

.iconBtn_container_img.notFull.pc {
  height: 28px;
  transform: scale(1);
}

.iconBtn_container_img.pc.dashboard {
  height: 40px;
  transform: scale(1);
}