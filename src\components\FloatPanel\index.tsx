import { px2rem } from "@/utils/setRootFontSize"
import { FloatingPanel, FloatingPanelRef } from "antd-mobile"
import { Dispatch, FC, SetStateAction, useCallback, useEffect, useRef, useState } from "react"
import styles from './index.module.scss';

export interface IFloatPanelRef {
  floatPanelRef: React.MutableRefObject<FloatingPanelRef | null>
}
interface floatPanelProps {
  showFloatPanel: boolean;
  setShowFloatPanel: Dispatch<SetStateAction<boolean>>;
  borderRadius?: string;
  zIndex?: string;
  anchors?: number[];
  className?: string;
}

interface OverlayProps {
  isOpen: boolean;
  onClose?: () => void;
  children?: React.ReactNode;
  closeOnClickOutside?: boolean;
  opacity?: number;
}

export const Overlay = (props: OverlayProps) => {
  const { isOpen, onClose, children, closeOnClickOutside = true, opacity = 0.5 } = props;
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  const startExitAnimation = useCallback(() => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      setIsExiting(false);
      onClose?.();
    }, 300);
  }, [onClose]);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setIsExiting(false);
      document.body.style.overflow = 'hidden';
    } else if (isVisible) {
      startExitAnimation();
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, isVisible, startExitAnimation]);

  // 处理 ESC 按键关闭
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && onClose) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  return (
    <div className={`${styles.overlay} ${isExiting ? styles.fancy_exit : ''}`} role="dialog" aria-modal="true" style={{ '--overlay-opacity': opacity, display: isVisible ? 'block' : 'none' } as React.CSSProperties}>
      <div className={styles.overlay_content} onClick={(e) => {
        if (closeOnClickOutside && e.target === e.currentTarget && onClose) onClose();
      }} >
        {children}
      </div>
    </div>
  );
};

const FloatPanel: FC<floatPanelProps> = ({
  children, zIndex, borderRadius, setShowFloatPanel, showFloatPanel, anchors = [0, 500], className
}) => {
  const floatPanelRef = useRef<FloatingPanelRef | null>(null);

  useEffect(() => {
    if (floatPanelRef && floatPanelRef.current) {
      if (showFloatPanel) {
        floatPanelRef.current.setHeight(anchors[1]);
        return;
      }
      floatPanelRef.current.setHeight(anchors[0]);
    }
  }, [anchors, floatPanelRef, showFloatPanel])

  const heightChange = useCallback((height, animation) => {
    if (height === 0 && !animation) {
      setShowFloatPanel(false);
    }
  }, [setShowFloatPanel])

  return (
    <Overlay isOpen={showFloatPanel} onClose={() => setShowFloatPanel(false)} opacity={0.7}>
      <FloatingPanel className={className} style={{ '--border-radius': borderRadius || px2rem('24px'), '--z-index': zIndex || '1000' }} ref={floatPanelRef} anchors={anchors} onHeightChange={heightChange}>
        {
          showFloatPanel && (children)
        }
      </FloatingPanel>
    </Overlay>
  )
}

export default FloatPanel;