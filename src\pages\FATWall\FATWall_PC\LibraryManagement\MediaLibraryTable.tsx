import React from 'react';
import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import ActionButtons from './ActionButtons';

// 媒体库项目接口
export interface IMediaLibrary {
  id: string;
  title: string;
  cover: string[];
  lastUpdateTime?: string;
  sharedBy?: string;
  paths?: string[];
  scan_percent?: number;
  scan_status?: string;
  scan_error_code?: string;
  lib_id?: number;
  tv_visable?: number; // 添加电视可见性属性
  share2who_list?: (string | number)[]; // 添加分享用户列表属性
}

// 扫描进度状态接口
interface ScanProgress {
  [libraryId: string]: number; // 0-100的进度百分比
}

interface MediaLibraryTableProps {
  libraries: IMediaLibrary[];
  isShared?: boolean;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onShare: (id: string) => void;
  onExitShare?: (id: string) => void;
  sectionTitle: string;
  scanProgress?: ScanProgress;
  completedScans?: Set<string>; // 添加已完成扫描的媒体库集合
  isOne?: boolean;
}

// 媒体库表格组件
const MediaLibraryTable: React.FC<MediaLibraryTableProps> = ({ 
  libraries, 
  isShared = false, 
  onEdit, 
  onDelete, 
  onShare, 
  onExitShare,
  sectionTitle, 
  scanProgress,
  completedScans,
  isOne
}) => {
  // 根据图片数量返回适当的样式类名
  const getCoverGroupClassName = (coverCount: number) => {
    switch(coverCount) {
      case 1:
        return styles.coverGroupSingle;
      case 2:
        return styles.coverGroupDouble;
      case 3:
        return styles.coverGroupTriple;
      default:
        return styles.coverGroupEmpty;
    }
  };

  // 根据扫描状态生成显示文本
  const getScanDisplayText = (library: IMediaLibrary) => {
    const libIdStr = library.lib_id?.toString();
    
    // 如果是刚完成扫描的媒体库，显示"扫描完成"
    if (completedScans?.has(libIdStr || '')) {
      return '扫描完成';
    }
    
    switch (library.scan_status) {
      case '扫描中':
        return `扫描中 ${Math.round(library.scan_percent || 0)}%`;
      case '扫描异常':
        return `扫描失败：${library.scan_error_code || '未知错误'}`;
      case '扫描完成':
      case '未开始':
      default:
        return null; // 不显示遮罩
    }
  };

  return (
    <div className={styles.tableContainer}>
      {/* 表头 */}
      <div className={styles.tableHeader}>
        <div className={styles.headerCell}>{sectionTitle}</div>
        <div className={styles.headerCell}>媒体库</div>
        <div className={styles.headerCell}>来源</div>
        <div className={styles.headerCell}>更新时间</div>
        <div className={styles.headerCell}>操作</div>
      </div>
      
      {/* 表格内容 */}
      <div className={styles.tableBody}>
        {libraries.map((library) => (
          <div key={library.id} className={styles.tableRow}>
            {/* 首列 - 标题 */}
            <div className={styles.headerCell}>
              <div className={styles.mediaTitle}>
                {library.title}
              </div>
            </div>
            
            {/* 媒体库列 */}
            <div className={styles.mediaCell}>
              <div className={`${styles.coverGroup} ${getCoverGroupClassName(library.cover.length)}`}>
                {library.cover.length > 0 ? (
                  library.cover.slice(0, 3).map((coverImg: string, index: number) => (
                    <div 
                      key={index} 
                      className={`${styles.coverItem} ${styles[`coverItem${index + 1}`]}`}
                    >
                      <PreloadImage src={coverImg} alt={`${library.title}-${index}`} />
                    </div>
                  ))
                ) : (
                  <div className={styles.noCover}>
                    <span>暂无影片</span>
                  </div>
                )}
                
                {/* 扫描进度遮罩层 */}
                {getScanDisplayText(library) && (
                  <div className={styles.scanOverlay}>
                    <div className={styles.progressContainer}>
                      <div className={styles.progressText}>
                        {getScanDisplayText(library)}
                      </div>
                      {/* 只有在扫描中状态时才显示进度条 */}
                      {library.scan_status === '扫描中' && (
                        <div className={styles.progressBar}>
                          <div 
                            className={styles.progressFill}
                            style={{ width: `${library.scan_percent || 0}%` }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {/* 来源列 */}
            <div className={styles.sourceCell}>
              {isShared ? (
                <div>来自"{library.sharedBy}"分享</div>
              ) : (
                <div className={styles.pathsList}>
                  {library.paths && library.paths.map((path: string, index: number) => (
                    <div key={index} className={styles.pathItem}>
                      {path}
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* 更新时间列 */}
            <div className={styles.updateTimeCell}>
              {library.lastUpdateTime}
            </div>
            
            {/* 操作列 */}
            <div className={styles.actionCell}>
              <ActionButtons 
                id={library.id}
                onEdit={onEdit} 
                onDelete={onDelete} 
                onShare={onShare}
                onExitShare={onExitShare}
                isOne={isOne}
                isShared={isShared}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MediaLibraryTable; 