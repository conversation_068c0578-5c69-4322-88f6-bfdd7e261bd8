.container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.left {
  flex: 1;
}

.right {
  width: 125px;
  /* 新增滚动关键属性 */
  overflow-y: auto;
  /* 启用垂直滚动 */
  -webkit-overflow-scrolling: touch;
  /* 启用iOS平滑滚动 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE/Edge */

  margin: 14px;
}

.listLabel {
  font-family: MiSans;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(140, 147, 176, 1);
  width: 125px;
  padding: 12px 8px 0 4px;
}

.previewImage {
  width: 100%;
  height: 65px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
  cursor: pointer;

  img {
    width: 100%;
    height: 65px;
  }
}

.activeText {
  width: 100%;
  height: 65px;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: MiSans;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  color: #fff;
}

.right_container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
