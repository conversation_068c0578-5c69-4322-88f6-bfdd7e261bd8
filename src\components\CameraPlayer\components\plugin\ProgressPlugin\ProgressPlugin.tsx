import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import "./ProgressPlugin.css";
import Player, { Events } from 'xgplayer/es/player';
import { getSystemType } from '@/utils/DeviceType';

interface IProgressPlugin {
  deviceType: 'pc' | 'mobile',
  player: Player | null,
  type: 'full' | 'notFull'
}
function ProgressPlugin(props: IProgressPlugin) {
  const { player, type } = props;
  const [progress, setProgress] = useState<number>(0);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const sliderRef = useRef<HTMLInputElement>(null);

  // 通用更新逻辑
  const updateTime = useCallback((value: number) => {
    if (player) {
      player.currentTime = player.duration * value / 100;
      setCurrentTime(player.duration * value / 100);
      setProgress(player.currentTime / player.duration * 100);
    }
  }, [player, player?.duration]);

  const onEvent = useCallback(() => {
    if (player) {
      player.on(Events.TIME_UPDATE, () => {
        setCurrentTime(player.currentTime);
        setProgress(player.currentTime / player.duration * 100);
      })
    }
  }, [player, player?.duration])

  useEffect(() => {
    onEvent();
  }, [onEvent])

  const curTime = useMemo(() => {
    let curMinutes = Math.floor(currentTime / 60);
    let curSecond = Math.floor(currentTime % 60);
    return `${String(curMinutes).padStart(2, '0')}:${String(curSecond).padStart(2, '0')}`;
  }, [currentTime])

  const durTime = useMemo(() => {
    let durMinutes = 0;
    let durSecond = 0;
    if (player) {
      durMinutes = Math.floor(player.duration / 60);
      durSecond = Math.floor(player.duration % 60);
    }
    return `${String(durMinutes).padStart(2, '0')}:${String(durSecond).padStart(2, '0')}`
  }, [player, player?.duration])

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    if (sliderRef.current) {
      const rect = sliderRef.current.getBoundingClientRect();
      let percentage = 0
      if (getSystemType() === 'ios') {
        percentage = (touch.clientY - rect.top) / rect.height;
      } else {
        percentage = (touch.clientX - rect.left) / rect.width;
      }
      updateTime(percentage * 100);
    }
  }, [updateTime])

  const handleMouseChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateTime(Number(e.target.value));
  }, [updateTime])

  return (
    <>
      {
        type === 'full' ? <div className="progress-slider-container" onTouchStart={handleTouchStart}>
          <div>
            <span>{`${curTime} / ${durTime}`}</span>
          </div>
          <input ref={sliderRef} type="range" min="0" max="100" step="1" value={progress} onChange={handleMouseChange} className="progress-slider" />
        </div> :
          <div className="progress-slider-container notFull" onTouchStart={handleTouchStart}>
            <span>{curTime}</span><input ref={sliderRef} type="range" min="0" max="100" step="1" value={progress} onChange={handleMouseChange} className="progress-slider" /><span>{durTime}</span>
          </div>
      }
    </>
  )
}

export default ProgressPlugin;