import React, { useState } from 'react';
import { Button, Toast } from 'antd-mobile';
import { WebDavInfo } from '@/utils/DeviceType';
import styles from './index.module.scss';

interface CreateFolderProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (folderName?: string) => void;
  currentPath?: string;
  webDavConfig?: WebDavInfo;
}

const CreateNasFolder: React.FC<CreateFolderProps> = ({
  visible,
  onCancel,
  onSuccess,
  currentPath = '/测试目录',
  webDavConfig
}) => {
  const [folderName, setFolderName] = useState<string>('');
  const [creating, setCreating] = useState<boolean>(false);

  // 清除输入框内容
  const handleClearInput = () => {
    setFolderName('');
  };

  // 使用WebDAV MKCOL方法创建文件夹
  const createDirectoryWithWebDAV = async (path: string) => {
    
    if (!webDavConfig) {
      return { success: false, message: "WebDAV配置未获取到" };
    }
    
    try {
      // 确保路径格式正确
      let dirPath = path;
      if (!dirPath.startsWith('/')) {
        dirPath = '/' + dirPath;
      }
      
      // 从路径中移除 alias_root 前缀
      if (webDavConfig.alias_root && dirPath.startsWith(webDavConfig.alias_root)) {
        dirPath = dirPath.substring(webDavConfig.alias_root.length);
      }
      
      // 使用encodeURIComponent处理文件夹名称部分
      const pathParts = dirPath.split('/');
      const folderNameEncoded = encodeURIComponent(pathParts[pathParts.length - 1]);
      pathParts[pathParts.length - 1] = folderNameEncoded;
      const encodedPath = pathParts.join('/');
      
      // 获取当前主机，优先使用实际IP地址
      const hostWithoutPort = window.location.hostname || window.location.host.replace(/:\d+$/, "");
      // const hostWithoutPort = '*************';

      
      // 构建WebDAV URL
      const webDAVUrl = `https://${hostWithoutPort}:${webDavConfig.port}${encodedPath}`;
      
      // 使用WebDAV MKCOL方法创建文件夹
      const response = await fetch(webDAVUrl, {
        method: 'MKCOL',
        headers: {
          'Depth': '1',
          'Content-Type': 'application/xml',
          'Authorization': 'Basic ' + btoa(`${webDavConfig.username}:${webDavConfig.password}`),
          'Range': 'bytes=0-1',
        },
      });

      if (response.status === 201) {
        // 201 Created - 文件夹创建成功
        return { success: true, message: '文件夹创建成功' };
      } else if (response.status === 405) {
        // 405 Method Not Allowed - 文件夹已存在
        return { success: false, message: '文件夹已存在' };
      } else if (response.status === 409) {
        // 409 Conflict - 父目录不存在
        return { success: false, message: '父目录不存在' };
      } else if (response.status === 403) {
        // 403 Forbidden - 权限不足
        return { success: false, message: '权限不足，无法创建文件夹' };
      } else {
        return { success: false, message: `创建失败，状态码: ${response.status}` };
      }
    } catch (error) {
      console.error('WebDAV MKCOL 请求失败:', error);
      return { success: false, message: '网络请求失败，请检查连接' };
    }
  };

  // 处理弹窗确定
  const handleConfirm = async () => {
    if (!folderName.trim()) {
      Toast.show({
        content: '请输入文件夹名称',
        position: 'bottom',
        duration: 2000,
      });
      return;
    }
    
    // 检查文件夹名称是否包含特殊字符
    const invalidChars = /[\\/:*?"<>|]/;
    if (invalidChars.test(folderName)) {
      Toast.show({
        content: '文件夹名称不能包含以下字符: \\ / : * ? " < > |',
        position: 'bottom',
        duration: 2000,
      });
      return;
    }
    
    // 显示创建中状态
    setCreating(true);
    
    try {
      // 构建完整路径
      let folderPath = currentPath;
      if (!folderPath.endsWith('/')) {
        folderPath += '/';
      }
      folderPath += folderName.trim();
      
      // 使用WebDAV创建文件夹
      const result = await createDirectoryWithWebDAV(folderPath);
      
      if (result.success) {
        Toast.show({
          content: '文件夹创建成功',
          position: 'bottom',
          duration: 2000,
        });
        
        // 重置状态
        setFolderName('');
        onSuccess(folderName.trim());
      } else {
        Toast.show({
          content: result.message,
          position: 'bottom',
          duration: 3000,
        });
      }
    } catch (error) {
      console.error('创建文件夹失败:', error);
      Toast.show({
        content: '创建文件夹失败，请重试',
        position: 'bottom',
        duration: 3000,
      });
    } finally {
      setCreating(false);
    }
  };

  // 处理弹窗取消
  const handleCancel = () => {
    if (creating) return;
    
    setFolderName('');
    onCancel();
  };

  if (!visible) {
    return null;
  }

  return (
    <div className={styles.modalOverlay} onClick={handleCancel}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <div className={styles.modalTitle}>新建文件夹</div>
          <div 
            className={`${styles.modalClose} ${creating ? styles.disabled : ''}`} 
            onClick={creating ? undefined : handleCancel}
          >
            ×
          </div>
        </div>
        <div className={styles.modalBody}>
          <div className={styles.inputContainer}>
            <input
              type="text"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              className={styles.folderNameInput}
              placeholder="请输入文件夹名称"
              maxLength={255}
            />
            {folderName && (
              <div className={styles.clearButton} onClick={handleClearInput}>×</div>
            )}
          </div>
        </div>
        <div className={styles.modalFooter}>
          <Button 
            className={styles.cancelButton}
            onClick={handleCancel}
            disabled={creating}
          >
            取消
          </Button>
          <Button 
            className={styles.confirmButton}
            onClick={handleConfirm}
            disabled={creating || !folderName.trim()}
          >
            {creating ? '创建中...' : '确定'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateNasFolder;
