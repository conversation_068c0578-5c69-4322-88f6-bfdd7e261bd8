// 引导页面样式
.guidePanel {
  display: flex;
  flex-direction: column;
  height: 70%;
  padding: 0 24px;
}

.guideContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.guideIcons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  gap: 16px;
}

.guideTitle {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 24px;
  line-height: 1.4;
}

.guideDescription {
  font-size: 14px;
  color: var(--text-color);
  padding: 0 20px;
  margin-bottom: 40px;
}

.upgradeButton {
  // 按钮容器样式可以根据需要自定义
}

.defaultButton {
  height: 48px;
  border-radius: 16px;
  background-color: #402C00;
  border: none;
  font-size: 16px;
  font-weight: 600;
  color: #E2AE1E;
  
  &:active {
    background-color: #6B4220;
  }
} 