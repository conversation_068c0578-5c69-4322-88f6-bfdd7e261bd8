.container {
  margin-bottom: 20px;
  padding: 0 40px;
}

.header {
  width: 100%;
  display: inline-flex;
  align-items: center;
  padding: 40px 0 0 0;
}

.category_container {
  display: flex;
  align-items: center;
  height: 120px;
  padding: 16px;
  gap: 8px;
}

.tabs_item {
  min-width: 130px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 45px;
  cursor: pointer;

  span {
    color: #fff;
    font-family: MiSans;
    font-size: 36px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  &:focus {
    background-color: #fff !important;
    border-radius: 45px;

    span {
      color: #0c0d0e;
      font-family: MiSans;
      font-size: 36px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }
}

.tabs_item.selected {
  font-weight: 700;
  background-color: rgba(255, 255, 255, 0.1);
}

.content {
  width: 100%;
  overflow-x: auto;
  scrollbar-width: none;
  scroll-behavior: smooth;
}

.filter_content_container {
  display: flex;
  gap: 20px;
}

.scroll_container {
  display: flex;
  gap: 20px;
  padding: 8px 0;
}

.scroll_item {
  min-width: 160px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  padding: 10px 10px;

  color: rgba(255, 255, 255, 0.6);
  font-family: MiSans;
  font-size: 30px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.selected {
  color: #fff;
  font-family: MiSans;
  font-size: 30px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.scroll_item:focus {
  background-color: #fff;
  border-radius: 45px;
  color: #0c0d0e;
  font-family: MiSans;
  font-size: 30px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}
