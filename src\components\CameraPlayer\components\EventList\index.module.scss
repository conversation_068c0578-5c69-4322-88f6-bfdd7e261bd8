.eventList_container {
  width: 100%;
  height: 100%;
  user-select: none;
}

.eventList_title {
  font-size: 12px;
}

.eventListCard_container {
  height: 80px;
  padding: 10px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: var(--card-background-color);
  border-radius: 12px;
  margin: 10px 0;
}

.eventListCard_icon {
  width: 28px;
  height: 28px;
  border-radius: 14px;

  img {
    width: 28px;
    height: 28px;
  }
}

.eventListCard_content {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 15px;
}

.eventListCard_title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
}

.eventListCard_subtitle {
  font-size: 12px;
  font-weight: 500;
  color: rgba(102, 102, 102, 1);
}

.eventListCard_poster {
  width: 90px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  position: relative;

  img {
    width: 90px;
    height: 60px;
  }
}

.eventListCard_rightOpt {
  padding: 8;
}

.eventListCard_rightOpt_button,
.eventListCard_rightOpt_status {
  width: 80px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--primary-btn-background-color);
  color: var(--primary-color);
  border-radius: 70px;
  font-size: 14px;
  font-weight: 500;
}

.eventListCard_rightOpt_button {
  cursor: pointer;
}

.eventListCard_moreOpt {
  cursor: pointer;
  width: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  > div {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  img {
    width: 3px;
    height: 17px;
  }
}

.eventListCard_content_container {
  display: flex;
  align-items: center;
  flex: 1;
}

.activeText {
  width: 100%;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: MiSans;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  color: #fff;
  border-radius: 8px;
  z-index: 0;
}
