import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Checkbox, List, message } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import { PreloadImage } from "@/components/Image";
import { getPoolInfo, listDirectory } from "@/api/fatWall";
import { uploadToBaiduNetdisk, BaiduUploadPathItem } from "@/api/nasDisk";
import UploadLocationModal from "../UploadLocationModal";
import EmptyState from "../EmptyState";
import emptyImg from "@/Resources/nasDiskImg/no-file.png";
import outline from "@/Resources/camMgmtImg/outline.png";
import outlineDark from "@/Resources/camMgmtImg/outline-dark.png";
import { useUser } from "@/utils/UserContext";
import { useTheme } from "@/utils/themeDetector";
import { modalShow } from "@/components/List";
import { useHistory } from "react-router-dom";
import { getFileIcon } from "@/utils/fileTypeUtils";
interface UploadModalProps {
  visible: boolean;
  onClose: () => void;
  onUpload: (selectedPaths: string[]) => void;
}

interface FileItem {
  id: string;
  name: string;
  type: "folder" | "file";
  time: string;
  isLiked?: boolean;
  path: string;
  isDirectory?: boolean;
  dataDir?: string;
}

const UploadModal: React.FC<UploadModalProps> = ({
  visible,
  onClose,
  onUpload,
}) => {
  const history = useHistory();
  const { userInfo } = useUser();
  const { nas_vip } = userInfo || {};
  const { isDarkMode } = useTheme();
  // 面包屑导航路径
  const [breadcrumbPath, setBreadcrumbPath] = useState<string[]>(["百度网盘"]);

  // 当前路径
  const [, setCurrentPath] = useState<string>("/");

  // 文件列表
  const [fileList, setFileList] = useState<FileItem[]>([]);

  // 选中的文件/文件夹
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // 获取存储池信息
  const { run: fetchPoolInfo } = useRequest(getPoolInfo, {
    manual: true,
    onSuccess: (response) => {
      if (response.code === 0 && response.data) {
        // 获取第一个存储池的顶层目录作为顶层文件夹
        if (response.data.internal_pool.length > 0) {
          const firstPool = response.data.internal_pool[0];

          let pathParent = firstPool.data_dir;

          if (response.data.webDAV?.alias_root) {
            const dataDir = firstPool.data_dir.endsWith("/")
              ? firstPool.data_dir.slice(0, -1)
              : firstPool.data_dir;
            const aliasRoot = response.data.webDAV.alias_root;
            pathParent = aliasRoot + dataDir;
          }

          // 获取顶层目录
          fetchDirectoryList({
            path: {
              parent: pathParent,
              recursion: false,
            },
          });
        }
      }
    },
    onError: (error) => {
      console.error("获取存储池信息失败：", error);
      setFileList([]);
    },
  });

  // 获取目录列表
  const { run: fetchDirectoryList } = useRequest(listDirectory, {
    manual: true,
    onSuccess: (response) => {
      if (response.code === 0 && response.data) {
        const files: FileItem[] = response.data.files.map((file, index) => ({
          id: `file_${index}`,
          name: file.name,
          type: file.xattr.directory ? ("folder" as const) : ("file" as const),
          time: new Date(parseInt(file.modified_time))
            .toLocaleDateString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            })
            .replace(/\//g, "/")
            .replace(/,/g, ""),
          path: `${file.parent}/${file.name}`,
          isDirectory: file.xattr.directory,
          isLiked: file.xattr.favorite,
        }));
        setFileList(files);
      }
    },
    onError: (error) => {
      console.error("获取目录失败：", error);
      setFileList([]);
    },
  });

  // 上传到百度网盘
  const { run: runUpload, loading: uploadLoading } = useRequest(
    (params: {
      localPaths: BaiduUploadPathItem[];
      remotePath: string;
      autotask: number;
    }) => {
      return uploadToBaiduNetdisk({
        action: "upload",
        autotask: params.autotask,
        remotepath: params.remotePath,
        localpath: params.localPaths,
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result.code === 0) {
          message.success("上传任务添加成功");
          onClose(); // 关闭弹窗
          onUpload([]); // 调用父组件回调
        } else {
          message.error(`上传任务添加失败: ${result.errmsg || "未知错误"}`);
        }
      },
      onError: (error) => {
        console.error("上传请求出错:", error);
        message.error("上传请求出错，请重试");
      },
    }
  );

  // 初始化时获取存储池信息
  useEffect(() => {
    if (visible) {
      fetchPoolInfo({});
      setSelectedItems([]);
      setBreadcrumbPath(["百度网盘"]);
      setCurrentPath("/");
    }
  }, [visible, fetchPoolInfo]);

  // 处理文件夹点击
  const handleFolderClick = (folder: FileItem) => {
    // 只有文件夹才能导航
    if (!folder.isDirectory) {
      return;
    }

    // 导航到子文件夹
    setCurrentPath(folder.path);
    setBreadcrumbPath([...breadcrumbPath, folder.name]);

    // 获取子文件夹内容
    fetchDirectoryList({
      path: {
        parent: folder.path,
        recursion: false,
      },
    });

    // 清空选择
    setSelectedItems([]);
  };

  // 处理面包屑导航点击
  const handleBreadcrumbClick = (index: number) => {
    if (index === 0) {
      // 点击根目录
      fetchPoolInfo({});
      setBreadcrumbPath(["百度网盘"]);
      setCurrentPath("/");
    } else {
      // 点击中间路径
      const newPath = breadcrumbPath.slice(0, index + 1);
      setBreadcrumbPath(newPath);

      // 构建路径
      const pathToFetch = "/" + newPath.slice(1).join("/");
      setCurrentPath(pathToFetch);

      // 获取该路径下的内容
      fetchDirectoryList({
        path: {
          parent: pathToFetch,
          recursion: false,
        },
      });
    }

    // 清空选择
    setSelectedItems([]);
  };

  // 处理选择变化
  const handleSelectionChange = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems((prev) => [...prev, id]);
    } else {
      setSelectedItems((prev) => prev.filter((itemId) => itemId !== id));
    }
  };

  // 处理全选
  const handleSelectAll = () => {
    if (selectedItems.length === fileList.length && fileList.length > 0) {
      // 如果已经全选，则取消全选
      setSelectedItems([]);
    } else {
      // 否则全选
      setSelectedItems(fileList.map((item) => item.id));
    }
  };

  // 上传位置选择弹窗
  const [locationModalVisible, setLocationModalVisible] =
    useState<boolean>(false);

  // 选择的上传位置
  const [uploadLocation, setUploadLocation] = useState<{
    path: string;
    displayPath: string;
  }>({
    path: "/来自：Xiaomi 智能存储",
    displayPath: "/来自：Xiaomi 智能存储",
  });

  // 打开上传位置选择弹窗
  const handleOpenLocationModal = () => {
    if (nas_vip !== 1) {
      modalShow(
        "会员权益",
        "该功能为网盘NAS会员权益，是否要开启？",
        (m) => {
          // 确认按钮点击
          m.destroy();
          history.push(`/baiduNetdisk_pc/mine`);
        },
        () => {
          // 取消按钮点击
        },
        false,
        {
          position: "center",
          okBtnText: "开通会员",
          cancelBtnText: "取消",
          okBtnStyle: { backgroundColor: "#402C00", color: "#E2AE1E" },
        }
      );
      return;
    }
    setLocationModalVisible(true);
  };

  // 处理上传位置选择
  const handleLocationSelect = (path: string, displayPath: string) => {
    setUploadLocation({
      path,
      displayPath,
    });
    setLocationModalVisible(false);
  };

  // 处理上传
  const handleUpload = () => {
    if (selectedItems.length === 0) {
      message.warning("请至少选择一个文件或文件夹");
      return;
    }

    const selectedFiles = fileList.filter((file) =>
      selectedItems.includes(file.id)
    );

    if (selectedFiles.length === 0) {
      message.warning("选中的文件路径无效，请重新选择");
      return;
    }

    // 格式化为对象数组格式
    const formattedLocalpath: BaiduUploadPathItem[] = selectedFiles.map(
      (file) => ({
        type: file.isDirectory ? "directory" : "file",
        path: file.path,
      })
    );

    // 调用上传接口
    runUpload({
      localPaths: formattedLocalpath,
      remotePath: uploadLocation.path, // 使用path而不是displayPath
      autotask: 0,
    });

    // 上传成功后会在onSuccess回调中关闭弹窗
  };

  // 渲染面包屑导航
  const renderBreadcrumb = () => {
    return (
      <div className={styles.breadcrumbContainer}>
        {breadcrumbPath.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <span className={styles.breadcrumbSeparator}>&gt;</span>
            )}
            <span
              className={`${styles.breadcrumbItem} ${
                index === breadcrumbPath.length - 1
                  ? styles.breadcrumbCurrent
                  : styles.breadcrumbLink
              }`}
              onClick={() => handleBreadcrumbClick(index)}
            >
              {item}
            </span>
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <Modal
      title={
        <div className={styles.modalHeader}>
          <ArrowLeftOutlined className={styles.backIcon} onClick={onClose} />
          <span className={styles.modalTitle}>选择要上传的文件</span>
          <div className={styles.selectAllContainer} onClick={handleSelectAll}>
            <PreloadImage
              src={isDarkMode ? outlineDark : outline}
              alt="全选"
              className={styles.selectAllIcon}
            />
          </div>
        </div>
      }
      open={visible}
      footer={null}
      onCancel={onClose}
      width={546}
      className={styles.uploadModal}
      closeIcon={null}
      centered
      styles={{
        body: {
          height: "calc(636px - 90px)",
          padding: 0,
          overflow: "hidden",
        },
      }}
    >
      <div className={styles.modalContent}>
        <div className={styles.breadcrumbHeader}>
          {renderBreadcrumb()}
        </div>

        <div className={styles.fileListContainer}>
          {fileList.length > 0 ? (
            <List
              className={styles.fileList}
              dataSource={fileList}
              renderItem={(file) => (
                <List.Item
                  key={file.id}
                  className={`${styles.fileItem} ${
                    selectedItems.includes(file.id) ? styles.selectedItem : ""
                  }`}
                >
                  <div
                    className={`${styles.fileContent} ${
                      selectedItems.includes(file.id)
                        ? styles.selectedContent
                        : ""
                    }`}
                    onClick={() => {
                      if (file.isDirectory) {
                        handleFolderClick(file);
                      }
                    }}
                    title={file.isDirectory ? "点击进入文件夹" : ""}
                  >
                    <PreloadImage
                      src={getFileIcon(file)}
                      alt={file.isDirectory ? "folder" : "file"}
                      className={styles.fileIcon}
                    />
                    <div className={styles.fileInfo}>
                      <div className={styles.fileName}>{file.name}</div>
                      <div className={styles.fileDetails}>{file.time}</div>
                    </div>
                  </div>
                  <div className={styles.checkboxCell}>
                    <Checkbox
                      checked={selectedItems.includes(file.id)}
                      onChange={(e) =>
                        handleSelectionChange(file.id, e.target.checked)
                      }
                      onClick={(e) => e.stopPropagation()}
                      className={styles.checkbox}
                    />
                  </div>
                </List.Item>
              )}
            />
          ) : (
            <div className={styles.emptyContainer}>
              <EmptyState icon={emptyImg} />
            </div>
          )}
        </div>

        <div className={styles.footerContainer}>
          <div className={styles.uploadPathContainer}>
            {nas_vip !== 1 && (<div className={styles.vipTip}>VIP专享</div>)}
            <Button
              className={styles.uploadPathButton}
              onClick={handleOpenLocationModal}
            >
              <span className={styles.pathLabel}>上传到: </span>
              <span className={styles.pathContainer}>
                <span className={styles.uploadPath}>{uploadLocation.displayPath}</span>
              </span>
            </Button>
          </div>
          <Button
            type="primary"
            className={styles.uploadButton}
            disabled={selectedItems.length === 0 || uploadLoading}
            loading={uploadLoading}
            onClick={handleUpload}
          >
            {uploadLoading ? "上传中..." : `上传 (${selectedItems.length})`}
          </Button>
        </div>
      </div>

      {/* 上传位置选择弹窗 */}
      <UploadLocationModal
        visible={locationModalVisible}
        onClose={() => setLocationModalVisible(false)}
        onSelect={handleLocationSelect}
      />
    </Modal>
  );
};

export default UploadModal;
