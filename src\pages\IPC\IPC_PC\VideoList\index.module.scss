.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 20px 20px;
  // background-color: var(--home-background-color);
}

.header {
  margin-bottom: 20px;
}

// 选中状态信息行 - 固定显示在右侧按钮上方
.selectedInfoRow {
  display: flex;
  justify-content: start;
  padding: 8px 0;
  height: 24px; // 固定高度，避免布局跳动
  visibility: visible; // 始终占据空间
}

// 选中状态的信息显示
.selectedInfo {
  display: flex;
  align-items: center;

  span {
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 22px;
    line-height: 24px;
    letter-spacing: 0%;
    vertical-align: middle;
    color: var(--title-color);
  }
}

// 操作按钮行 - 左侧操作按钮与右侧按钮水平对齐
.operationRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 7px;
  padding: 12px 0;
}

// 左侧操作按钮组
.leftActions {
  display: flex;
  align-items: center;
  gap: 16px;
}

// 右侧操作按钮组
.rightActions {
  display: flex;
  align-items: center;
  gap: 16px;
  .cameraList {
    font-family: MiSans W;
    font-weight: 600;
    font-style: Demibold;
    font-size: 12px;
    line-height: 100%;
    letter-spacing: 0%;
    color: var(--text-color);
  }
  .operationItem {
    padding: 4px;
  }
}

.operationItem {
  display: flex;
  align-items: center;
  // gap: 8px;
  padding: 8px 12px;
  background: var(--background-color);
  border: 1px solid var(--thinLine-background-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  span {
    font-family: MiSans;
    font-weight: 500;
    font-style: Medium;
    font-size: 17px;
    line-height: 100%;
    letter-spacing: 0px;
    vertical-align: middle;

    color: var(--subtitle-text-color);
  }

  img {
    width: 24px;
    height: 24px;
  }

  // DatePicker 样式
  :global(.ant-picker) {
    border: none;
    background: transparent;
    box-shadow: none;
    padding: 0;

    .ant-picker-input > input {
      font-size: 14px;
      color: var(--list-value-text-color);
    }

    &:hover,
    &:focus {
      border-color: transparent;
      box-shadow: none;
    }
  }
}

.operationItem_datePicker {
  :global(.ant-picker) {
    border-radius: 6px;
  }
}

.content {
  flex: 1;
  overflow-y: auto;
  background: var(--background-color);
  border-radius: 8px;
  padding-top: 20px;
  // padding: 20px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dateTitle {
  font-family: MiSans W;
  font-weight: 400;
  font-style: Normal;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0px;
  color: var(--text-color);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--thinLine-background-color);
}

.videoGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 24px;
  padding: 10px 0;
}

.videoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;

  // &:hover {
  //   transform: translateY(-2px);
  // }
}

.thumbnailContainer {
  position: relative;
  width: 110px;
  height: 78px;
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--thinLine-background-color);
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selectItem {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 4px;
  right: 4px;
  visibility: hidden;
  background-color: var(--modal-content-background-color);
  border-radius: 8px;

  &:hover {
    cursor: pointer;
  }

  img {
    height: 16px;
  }
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.thumbnailPlaceholder {
  width: 100%;
  height: 100%;
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 1px dashed var(--thinLine-background-color);

  span {
    color: var(--list-value-text-color);
    font-size: 12px;
  }
}

.timeLabel {
  font-family: MiSans W;
  font-weight: 400;
  font-style: Normal;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: var(--text-color);
}

// 空状态样式
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  min-height: 300px;
  flex: 1;
}

.emptyIcon {
  img {
    width: 120px;
    height: 120px;
    object-fit: contain;
  }
}

.emptyText {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

// 加载状态
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .loadingText {
    color: var(--list-value-text-color);
    font-size: 14px;
  }
}

// 加载更多按钮容器
.loadMoreContainer {
  display: flex;
  justify-content: center;
  padding: 20px;
  margin-top: 16px;
}

// 加载更多按钮
.loadMoreButton {
  padding: 8px 24px;
  background-color: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;

  // &:hover:not(:disabled) {
  //   background-color: var(--primary-hover-color);
  // }

  &:disabled {
    background-color: var(--disabled-color);
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// 没有更多数据提示
.noMoreData {
  display: flex;
  justify-content: center;
  padding: 20px;
  margin-top: 16px;
  color: var(--list-value-text-color);
  font-size: 14px;
}
