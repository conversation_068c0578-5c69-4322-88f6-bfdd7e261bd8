import { useCallback, useEffect, useRef, useState } from "react";

const useRowDragScroll = () => {
  const rowRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef(0);
  const startScrollLeftRef = useRef(0);
  const [isDragging, setIsDragging] = useState(false);

  // 获取滚动边界
  const getScrollBounds = useCallback(() => {
    if (!rowRef.current) return { min: 0, max: 0 };
    const { scrollWidth, clientWidth } = rowRef.current;
    return {
      min: 0,
      max: Math.max(0, scrollWidth - clientWidth),
    };
  }, []);

  // 应用滚动限制
  const applyScrollBounds = useCallback((scroll: number) => {
    const bounds = getScrollBounds();
    return Math.max(bounds.min, Math.min(bounds.max, scroll));
  }, [getScrollBounds]);

  // 开始拖拽
  const startDrag = useCallback((clientX: number) => {
    if (!rowRef.current) return;

    setIsDragging(true);
    startXRef.current = clientX;
    startScrollLeftRef.current = rowRef.current.scrollLeft;
  }, []);

  // 拖拽处理
  const drag = useCallback((clientX: number) => {
    if (!rowRef.current || !isDragging) return;
    // 1. 计算位移
    const deltaX = clientX - startXRef.current;
    const targetScroll = startScrollLeftRef.current - deltaX;
    // 2. 应用滚动
    rowRef.current.scrollLeft = applyScrollBounds(targetScroll);
  }, [isDragging, applyScrollBounds]);

  // 事件监听器
  useEffect(() => {
    const row = rowRef.current;
    if (!row) return;

    // 鼠标事件处理
    const handleMouseMove = (e: MouseEvent) => drag(e.clientX);
    const handleMouseUp = () => {
      setIsDragging(false);
    };

    // 触摸事件处理
    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length === 1) drag(e.touches[0].clientX);
    };
    const handleTouchEnd = () => {
      setIsDragging(false);
    };

    //当鼠标移出该行的时候调用handleMouseUp
    const handleMouseLeave = () => {
      if (isDragging) {
        handleMouseUp();
      }
    };

    // 绑定事件
    row.addEventListener("mousemove", handleMouseMove);
    row.addEventListener("mouseleave", handleMouseLeave);
    row.addEventListener("mouseup", handleMouseUp);
    row.addEventListener("touchmove", handleTouchMove);
    row.addEventListener("touchend", handleTouchEnd);

    return () => {
      row.removeEventListener("mousemove", handleMouseMove);
      row.removeEventListener("mouseup", handleMouseUp);
      row.removeEventListener("touchmove", handleTouchMove);
      row.removeEventListener("touchend", handleTouchEnd);
      row.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, [drag, rowRef, setIsDragging, isDragging]);

  return {
    rowRef,
    handleMouseDown: useCallback(
      (e: React.MouseEvent) => {
        startDrag(e.clientX);
      },
      [startDrag]
    ),
    handleTouchStart: useCallback(
      (e: React.TouchEvent) => {
        if (e.touches.length === 1) startDrag(e.touches[0].clientX);
      },
      [startDrag]
    ),
    isDragging,
    drag,
    setIsDragging,
  };
};

export default useRowDragScroll;
