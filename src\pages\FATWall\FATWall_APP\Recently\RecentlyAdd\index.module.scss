.container {
  background-color: var(--background-color);
  min-height: calc(100vh - 35px);
  margin-bottom: 24px;
  position: relative;
  overflow: hidden; 

  .title {
    font-size: 32px;
    margin-bottom: 16px;
    color: var(--title-color);
    padding: 0 16px;
  }

  .filmsContainer {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 0 16px;
    overflow-y: auto; 
    max-height: calc(100vh - 150px); 
    padding-bottom: 105px; 
    scrollbar-width: none;
  }

  .filmItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.noDataTip {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100px;
  color: var(--text-secondary-color);
  font-size: 14px;
}
