.container {
  display: flex;
  width: 100%;
  height: 100%;

  :global {
    .ant-divider-vertical {
      height: 100%;
      border-inline-start: 1px solid rgba(5, 5, 5, 0.2);
    }
    .ant-divider-horizontal {
      margin: 16px 0;
    }
    .ant-btn {
      padding: 0;
      justify-content: flex-start;
      &:hover {
        background: transparent !important;
        color: var(--emergency-text-color) !important;
      }
    }
  }
}

.left {
  width: 290px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.left_icon {
  width: 64px;
  height: 64px;

  img {
    width: 100%;
    height: 100%;
  }
}

.left_name {
  font-family: MiSans;
  font-weight: 500;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
  color: var(--text-color);
}

.left_traffic {
  width: 100%;
  height: 20px;
  padding: 28px 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-color);

  span {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  img {
    width: 16px;
    height: 16px;
  }
}

.right {
  flex: 1;
  height: 100%;
  padding: 0 8px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.right_spanText {
  font-family: MiSans;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(140, 147, 176, 1);
  display: block;
  margin: 12px 0 12px 0;
}

.right_btns {
  display: flex;
  flex-direction: column;
  width: fit-content;
  max-width: 100%; /* 防止溢出父容器 */
}

.right_btn {
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  vertical-align: middle;
  color: var(--emergency-text-color);
  margin-bottom: 20px;
}
