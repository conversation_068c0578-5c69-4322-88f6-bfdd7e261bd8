import { splitURL } from "../MonitorPlayer/MonitorPlayer";
import EventListCard, { IEventListCard } from "./EventListCard";
import styles from "./index.module.scss";

interface IEventList {
  data: IEventListCard[]
  callback?: (value: IEventListCard) => void
  title?: string
  needHeader?: boolean
}

const EventList = (props: IEventList) => {
  const { data, callback, title, needHeader } = props;
  return (
    <div className={styles.eventList_container}>
      <div>
        <span className={styles.eventList_title}>{title}</span>
      </div>
      {
        (data || []).map((item, index: number) => (
          <EventListCard key={item.title + index} callback={callback} {...item} poster={item.poster && (needHeader ? splitURL(item.poster) : item.poster)} needHeader={needHeader} />
        ))
      }
    </div>
  )
}

export default EventList;